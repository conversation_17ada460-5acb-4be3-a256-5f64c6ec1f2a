# AI填充数据清理规则设计

## 当前问题分析

通过分析现有的AI填充prompt，发现以下问题：

1. **缺少数据过滤机制**：当前prompt没有明确指示AI过滤司机个人信息
2. **无隐私保护规则**：没有识别和移除敏感个人信息的指令
3. **内容清理不足**：缺少对非订单相关内容的过滤规则
4. **数据优先级不明确**：当无法区分司机和客户信息时，没有明确的处理策略

## 数据清理规则设计

### 1. 司机信息过滤规则

#### 1.1 关键词识别
```javascript
const DRIVER_KEYWORDS = [
    // 中文关键词
    '司机', '师傅', '驾驶员', '开车的', '车主', '代驾', '网约车司机',
    '滴滴司机', '出租车司机', '专车司机', '快车司机',
    
    // 英文关键词
    'driver', 'chauffeur', 'operator', 'pilot',
    
    // 平台相关
    'didi', '滴滴', 'uber', 'lyft', 'grab'
];
```

#### 1.2 个人信息模式
```javascript
const PERSONAL_INFO_PATTERNS = [
    // 身份证号：18位数字或17位数字+X
    /\b\d{17}[\dXx]\b/g,
    
    // 手机号：11位数字，1开头
    /\b1[3-9]\d{9}\b/g,
    
    // 车牌号：省份+字母+数字组合
    /[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4,5}[A-Z0-9挂学警港澳]/g,
    
    // 驾驶证号：通常与身份证号相同
    /驾驶证.*?(\d{17}[\dXx])/g
];
```

### 2. 非订单内容清理规则

#### 2.1 聊天记录过滤
```javascript
const CHAT_PATTERNS = [
    // 问候语
    /^(你好|您好|hello|hi|嗨|哈喽)/i,
    
    // 确认语句
    /^(好的|收到|明白|知道了|ok|okay)/i,
    
    // 感谢语句
    /^(谢谢|感谢|thanks|thank you)/i,
    
    // 时间相关对话
    /^(几点|什么时候|多久|时间)/i,
    
    // 位置相关对话
    /^(在哪|位置|地址|到了吗)/i
];
```

#### 2.2 系统通知过滤
```javascript
const SYSTEM_NOTIFICATIONS = [
    // 平台通知
    /系统消息|系统通知|平台提醒/,
    
    // 订单状态更新
    /订单已接单|司机已到达|行程开始|行程结束/,
    
    // 支付通知
    /支付成功|扣费通知|账单生成/,
    
    // 评价提醒
    /请为本次服务评价|评分|点评/
];
```

### 3. 核心订单信息保留规则

#### 3.1 必须保留的信息类型
```javascript
const ESSENTIAL_ORDER_INFO = {
    // 订单标识
    orderIdentifiers: [
        'Order ID', '订单ID', '订单编号', '单号',
        'OTA Reference', 'Booking Reference',
        'Transaction ID', 'Invoice Number', 'Receipt Number'
    ],
    
    // 客户信息（非司机）
    customerInfo: [
        '乘客', '客户', '用户', '顾客', 'passenger', 'customer', 'user'
    ],
    
    // 服务详情
    serviceDetails: [
        '服务类型', '行程', '路线', '起点', '终点',
        '时间', '日期', '距离', '时长'
    ],
    
    // 费用信息
    priceInfo: [
        '费用', '价格', '金额', '总计', '小计',
        'price', 'amount', 'total', 'subtotal', 'cost'
    ]
};
```

#### 3.2 数据优先级规则
```javascript
const DATA_PRIORITY_RULES = {
    // 当无法区分司机和客户信息时的处理策略
    ambiguousDataHandling: {
        // 优先保留客户相关数据
        prioritizeCustomer: true,
        
        // 如果同时存在多个姓名，选择策略
        nameSelection: 'first_non_driver', // 选择第一个非司机相关的姓名
        
        // 如果同时存在多个电话，选择策略
        phoneSelection: 'customer_context', // 选择客户上下文中的电话
        
        // 默认保留策略
        defaultKeep: ['orderInfo', 'serviceDetails', 'priceInfo'],
        defaultFilter: ['driverPersonalInfo', 'chatMessages', 'systemNotifications']
    }
};
```

### 4. 智能识别规则

#### 4.1 上下文分析
```javascript
const CONTEXT_ANALYSIS = {
    // 通过上下文判断信息归属
    contextClues: {
        // 司机相关上下文
        driverContext: [
            '司机信息', '驾驶员资料', '车辆信息',
            '接单司机', '您的司机', 'driver info'
        ],
        
        // 客户相关上下文
        customerContext: [
            '乘客信息', '客户资料', '用户信息',
            '联系人', '收货人', 'passenger info', 'customer details'
        ],
        
        // 订单相关上下文
        orderContext: [
            '订单详情', '服务信息', '行程信息',
            '费用明细', 'order details', 'service info'
        ]
    }
};
```

#### 4.2 数据验证规则
```javascript
const DATA_VALIDATION = {
    // 验证提取的数据是否合理
    validation: {
        // 客户姓名验证
        customerName: {
            // 不应包含司机相关词汇
            excludeKeywords: ['司机', '师傅', '驾驶员'],
            // 长度限制
            minLength: 2,
            maxLength: 20
        },
        
        // 电话号码验证
        phoneNumber: {
            // 中国手机号格式
            pattern: /^1[3-9]\d{9}$/,
            // 不应在司机信息上下文中出现
            excludeContext: ['司机电话', '司机联系方式']
        },
        
        // 订单号验证
        orderNumber: {
            // 常见订单号格式
            patterns: [
                /^[A-Z]{2,4}\d{6,}$/,  // 字母+数字
                /^\d{10,}$/,           // 纯数字
                /^[A-Z0-9-]{8,}$/      // 字母数字组合
            ]
        }
    }
};
```

### 5. 错误处理和兜底策略

#### 5.1 数据冲突处理
```javascript
const CONFLICT_RESOLUTION = {
    // 当检测到数据冲突时的处理策略
    conflictHandling: {
        // 多个姓名冲突
        multipleNames: 'select_customer_context',
        
        // 多个电话冲突
        multiplePhones: 'select_first_valid',
        
        // 信息归属不明确
        ambiguousOwnership: 'prefer_customer',
        
        // 无法确定时的默认行为
        defaultBehavior: 'exclude_suspicious_data'
    }
};
```

#### 5.2 数据质量保证
```javascript
const QUALITY_ASSURANCE = {
    // 确保数据质量的检查机制
    qualityChecks: {
        // 检查是否意外包含司机信息
        driverInfoCheck: true,
        
        // 检查数据完整性
        completenessCheck: true,
        
        // 检查数据格式正确性
        formatCheck: true,
        
        // 检查敏感信息泄露
        privacyCheck: true
    }
};
```

## 实现策略

### 1. Prompt优化策略
- 在现有prompt前添加数据清理指令
- 明确指示AI识别和过滤司机个人信息
- 提供具体的过滤规则和示例
- 强调隐私保护的重要性

### 2. 后处理验证
- 在AI返回结果后进行二次验证
- 使用正则表达式检查敏感信息
- 实施数据质量检查
- 提供人工审核机制

### 3. 渐进式优化
- 先实施基础过滤规则
- 根据实际使用情况调整规则
- 收集用户反馈优化算法
- 持续改进识别准确性

## 预期效果

1. **隐私保护**：有效过滤司机个人敏感信息
2. **数据质量**：提高订单信息提取的准确性
3. **用户体验**：减少无关信息干扰
4. **合规性**：符合数据保护法规要求

这套数据清理规则将确保AI填充功能既能准确提取订单信息，又能有效保护司机隐私，提升整体系统的安全性和可靠性。
