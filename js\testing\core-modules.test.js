/**
 * 核心模块单元测试
 * @file core-modules.test.js - 核心模块的单元测试
 * @description 测试AppConfig、DOMCache、DebugManager等核心模块
 */

// 测试AppConfig模块
describe('AppConfig模块测试', () => {
    beforeEach(() => {
        // 重置配置到默认状态
        if (window.AppConfig) {
            window.AppConfig.currentCompany = 'gomyhire';
            window.AppConfig.currentCurrency = 'MYR';
            window.AppConfig.debugMode = false;
        }
    });
    
    it('应该正确加载AppConfig模块', () => {
        Assert.ok(window.AppConfig, 'AppConfig模块应该存在');
        Assert.type(window.AppConfig, 'object', 'AppConfig应该是对象');
    });
    
    it('应该包含必要的配置属性', () => {
        const config = window.AppConfig;
        Assert.ok(config.hasOwnProperty('currentCompany'), '应该有currentCompany属性');
        Assert.ok(config.hasOwnProperty('currentCurrency'), '应该有currentCurrency属性');
        Assert.ok(config.hasOwnProperty('debugMode'), '应该有debugMode属性');
        Assert.ok(config.hasOwnProperty('geminiApiKey'), '应该有geminiApiKey属性');
    });
    
    it('应该正确更新配置', () => {
        const config = window.AppConfig;
        const newConfig = {
            currentCompany: 'sky-mirror',
            debugMode: true
        };
        
        config.updateConfig(newConfig);
        
        Assert.equal(config.currentCompany, 'sky-mirror', '公司配置应该更新');
        Assert.equal(config.debugMode, true, '调试模式应该更新');
    });
    
    it('应该正确保存和加载本地存储', () => {
        const config = window.AppConfig;
        
        // 设置测试配置
        config.currentCompany = 'test-company';
        config.currentCurrency = 'USD';
        config.saveToLocalStorage();
        
        // 重置配置
        config.currentCompany = 'gomyhire';
        config.currentCurrency = 'MYR';
        
        // 从本地存储加载
        config.loadFromLocalStorage();
        
        Assert.equal(config.currentCompany, 'test-company', '应该从本地存储加载公司配置');
        Assert.equal(config.currentCurrency, 'USD', '应该从本地存储加载货币配置');
    });
});

// 测试CurrencyConfig模块
describe('CurrencyConfig模块测试', () => {
    it('应该正确加载CurrencyConfig模块', () => {
        Assert.ok(window.CurrencyConfig, 'CurrencyConfig模块应该存在');
        Assert.type(window.CurrencyConfig, 'object', 'CurrencyConfig应该是对象');
    });
    
    it('应该包含预定义的货币', () => {
        const currencies = window.CurrencyConfig;
        Assert.ok(currencies.MYR, '应该包含马来西亚林吉特');
        Assert.ok(currencies.CNY, '应该包含人民币');
        Assert.ok(currencies.USD, '应该包含美元');
        Assert.ok(currencies.EUR, '应该包含欧元');
    });
    
    it('货币配置应该包含必要的属性', () => {
        const myr = window.CurrencyConfig.MYR;
        Assert.ok(myr.symbol, '应该有货币符号');
        Assert.ok(myr.name, '应该有货币名称');
        Assert.ok(myr.code, '应该有货币代码');
        Assert.type(myr.decimals, 'number', '小数位数应该是数字');
    });
});

// 测试DOMCache模块
describe('DOMCache模块测试', () => {
    let testElement;
    
    beforeEach(() => {
        // 创建测试元素
        testElement = document.createElement('div');
        testElement.id = 'test-element';
        document.body.appendChild(testElement);
    });
    
    afterEach(() => {
        // 清理测试元素
        if (testElement && testElement.parentNode) {
            testElement.parentNode.removeChild(testElement);
        }
    });
    
    it('应该正确加载DOMCache模块', () => {
        Assert.ok(window.DOMCache, 'DOMCache模块应该存在');
        Assert.type(window.DOMCache, 'object', 'DOMCache应该是对象');
    });
    
    it('应该包含必要的方法', () => {
        const cache = window.DOMCache;
        Assert.type(cache.initCache, 'function', '应该有initCache方法');
        Assert.type(cache.getCacheStats, 'function', '应该有getCacheStats方法');
        Assert.type(cache.refreshCache, 'function', '应该有refreshCache方法');
        Assert.type(cache.get, 'function', '应该有get方法');
    });
    
    it('应该正确获取缓存统计', () => {
        const cache = window.DOMCache;
        const stats = cache.getCacheStats();
        
        Assert.type(stats, 'object', '统计应该是对象');
        Assert.type(stats.总元素数, 'number', '总元素数应该是数字');
        Assert.type(stats.有效元素数, 'number', '有效元素数应该是数字');
        Assert.type(stats.缓存命中率, 'number', '缓存命中率应该是数字');
    });
    
    it('应该正确验证缓存', () => {
        const cache = window.DOMCache;
        const validation = cache.validateCache();
        
        Assert.type(validation, 'object', '验证结果应该是对象');
        Assert.ok(Array.isArray(validation.valid), 'valid应该是数组');
        Assert.ok(Array.isArray(validation.invalid), 'invalid应该是数组');
        Assert.type(validation.needsRefresh, 'boolean', 'needsRefresh应该是布尔值');
    });
});

// 测试SafeDOM模块
describe('SafeDOM模块测试', () => {
    let testElement;
    
    beforeEach(() => {
        testElement = document.createElement('input');
        testElement.id = 'safe-dom-test';
        testElement.type = 'text';
        document.body.appendChild(testElement);
    });
    
    afterEach(() => {
        if (testElement && testElement.parentNode) {
            testElement.parentNode.removeChild(testElement);
        }
    });
    
    it('应该正确加载SafeDOM模块', () => {
        Assert.ok(window.SafeDOM, 'SafeDOM模块应该存在');
        Assert.type(window.SafeDOM, 'object', 'SafeDOM应该是对象');
    });
    
    it('应该安全获取DOM元素', () => {
        const safeDOM = window.SafeDOM;
        const element = safeDOM.get('safe-dom-test');
        
        Assert.ok(element, '应该获取到元素');
        Assert.equal(element.id, 'safe-dom-test', '应该是正确的元素');
    });
    
    it('应该安全设置和获取元素值', () => {
        const safeDOM = window.SafeDOM;
        const testValue = 'test value';
        
        const setResult = safeDOM.setValue('safe-dom-test', testValue);
        Assert.ok(setResult, '设置值应该成功');
        
        const getValue = safeDOM.getValue('safe-dom-test');
        Assert.equal(getValue, testValue, '获取的值应该正确');
    });
    
    it('应该处理不存在的元素', () => {
        const safeDOM = window.SafeDOM;
        
        const element = safeDOM.get('non-existent-element');
        Assert.equal(element, null, '不存在的元素应该返回null');
        
        const setResult = safeDOM.setValue('non-existent-element', 'test');
        Assert.equal(setResult, false, '设置不存在元素的值应该返回false');
        
        const getValue = safeDOM.getValue('non-existent-element');
        Assert.equal(getValue, '', '获取不存在元素的值应该返回空字符串');
    });
});

// 测试DebugManager模块
describe('DebugManager模块测试', () => {
    beforeEach(() => {
        if (window.DebugManager) {
            // 重置调试管理器
            window.DebugManager.enabled = true;
            window.DebugManager.currentLogLevel = 1; // INFO级别
            window.DebugManager.logHistory = [];
            window.DebugManager.performanceMetrics = {};
        }
    });
    
    it('应该正确加载DebugManager模块', () => {
        Assert.ok(window.DebugManager, 'DebugManager模块应该存在');
        Assert.type(window.DebugManager, 'object', 'DebugManager应该是对象');
    });
    
    it('应该包含必要的方法', () => {
        const debug = window.DebugManager;
        Assert.type(debug.init, 'function', '应该有init方法');
        Assert.type(debug.log, 'function', '应该有log方法');
        Assert.type(debug.startPerformance, 'function', '应该有startPerformance方法');
        Assert.type(debug.endPerformance, 'function', '应该有endPerformance方法');
    });
    
    it('应该正确记录日志', () => {
        const debug = window.DebugManager;
        const testMessage = 'test log message';
        
        debug.log('INFO', testMessage);
        
        Assert.ok(debug.logHistory.length > 0, '应该有日志记录');
        const lastLog = debug.logHistory[debug.logHistory.length - 1];
        Assert.equal(lastLog.level, 'INFO', '日志级别应该正确');
        Assert.equal(lastLog.message, testMessage, '日志消息应该正确');
    });
    
    it('应该正确监控性能', () => {
        const debug = window.DebugManager;
        const operationName = 'test-operation';
        
        const id = debug.startPerformance(operationName);
        Assert.type(id, 'string', '性能监控ID应该是字符串');
        Assert.ok(debug.performanceMetrics[id], '应该创建性能监控记录');
        
        // 模拟一些操作时间
        setTimeout(() => {
            const metric = debug.endPerformance(id);
            Assert.ok(metric, '应该返回性能指标');
            Assert.type(metric.duration, 'number', '持续时间应该是数字');
            Assert.ok(metric.duration >= 0, '持续时间应该大于等于0');
        }, 10);
    });
    
    it('应该正确获取系统状态', () => {
        const debug = window.DebugManager;
        const status = debug.getSystemStatus();
        
        Assert.type(status, 'object', '系统状态应该是对象');
        Assert.ok(status.hasOwnProperty('调试模式'), '应该包含调试模式信息');
        Assert.ok(status.hasOwnProperty('日志级别'), '应该包含日志级别信息');
        Assert.ok(status.hasOwnProperty('浏览器信息'), '应该包含浏览器信息');
        Assert.ok(status.hasOwnProperty('页面信息'), '应该包含页面信息');
    });
    
    it('应该正确设置日志级别', () => {
        const debug = window.DebugManager;
        
        debug.setLogLevel('ERROR');
        Assert.equal(debug.currentLogLevel, debug.logLevels.ERROR, '应该设置为ERROR级别');
        
        debug.setLogLevel('DEBUG');
        Assert.equal(debug.currentLogLevel, debug.logLevels.DEBUG, '应该设置为DEBUG级别');
    });
    
    it('应该正确导出日志', () => {
        const debug = window.DebugManager;
        
        // 添加一些测试日志
        debug.log('INFO', 'test message 1');
        debug.log('ERROR', 'test message 2');
        
        const jsonLogs = debug.exportLogs('json');
        Assert.type(jsonLogs, 'string', 'JSON导出应该是字符串');
        Assert.ok(jsonLogs.includes('test message 1'), '应该包含测试消息1');
        
        const csvLogs = debug.exportLogs('csv');
        Assert.type(csvLogs, 'string', 'CSV导出应该是字符串');
        Assert.ok(csvLogs.includes('Timestamp,Level,Message'), '应该包含CSV头部');
        
        const txtLogs = debug.exportLogs('txt');
        Assert.type(txtLogs, 'string', 'TXT导出应该是字符串');
        Assert.ok(txtLogs.includes('[INFO]'), '应该包含日志级别标记');
    });
});

// 测试ExportConfig模块
describe('ExportConfig模块测试', () => {
    it('应该正确加载ExportConfig模块', () => {
        Assert.ok(window.ExportConfig, 'ExportConfig模块应该存在');
        Assert.type(window.ExportConfig, 'object', 'ExportConfig应该是对象');
    });
    
    it('应该包含A4尺寸配置', () => {
        const config = window.ExportConfig;
        Assert.ok(config.a4, '应该有A4配置');
        Assert.equal(config.a4.widthPx, 794, 'A4宽度应该是794px');
        Assert.equal(config.a4.heightPx, 1123, 'A4高度应该是1123px');
        Assert.equal(config.a4.dpi, 300, 'DPI应该是300');
    });
    
    it('应该包含图片配置', () => {
        const config = window.ExportConfig;
        Assert.ok(config.images, '应该有图片配置');
        Assert.ok(config.images.header, '应该有页眉配置');
        Assert.ok(config.images.footer, '应该有页脚配置');
        Assert.ok(config.images.stamp, '应该有印章配置');
        
        Assert.equal(config.images.header.height, 130, '页眉高度应该是130px');
        Assert.equal(config.images.footer.height, 110, '页脚高度应该是110px');
        Assert.equal(config.images.stamp.width, 96, '印章宽度应该是96px');
        Assert.equal(config.images.stamp.height, 96, '印章高度应该是96px');
    });
    
    it('应该包含质量配置', () => {
        const config = window.ExportConfig;
        Assert.ok(config.quality, '应该有质量配置');
        Assert.equal(config.quality.dpi, 300, '导出DPI应该是300');
        Assert.type(config.quality.scale, 'number', '缩放倍数应该是数字');
        Assert.type(config.quality.compression, 'number', '压缩质量应该是数字');
    });
});

// 测试TemplateConfig模块
describe('TemplateConfig模块测试', () => {
    it('应该正确加载TemplateConfig模块', () => {
        Assert.ok(window.TemplateConfig, 'TemplateConfig模块应该存在');
        Assert.type(window.TemplateConfig, 'object', 'TemplateConfig应该是对象');
    });
    
    it('应该包含发票和收据模板配置', () => {
        const config = window.TemplateConfig;
        Assert.ok(config.invoice, '应该有发票模板配置');
        Assert.ok(config.receipt, '应该有收据模板配置');
        Assert.ok(config.common, '应该有通用配置');
    });
    
    it('发票模板应该显示公司信息', () => {
        const invoice = window.TemplateConfig.invoice;
        Assert.equal(invoice.showCompanyInfo, true, '发票应该显示公司信息');
        Assert.equal(invoice.showTaxInfo, true, '发票应该显示税务信息');
        Assert.ok(Array.isArray(invoice.fields), '字段配置应该是数组');
    });
    
    it('收据模板应该隐藏公司信息', () => {
        const receipt = window.TemplateConfig.receipt;
        Assert.equal(receipt.showCompanyInfo, false, '收据应该隐藏公司信息');
        Assert.equal(receipt.showTaxInfo, false, '收据应该隐藏税务信息');
        Assert.ok(Array.isArray(receipt.fields), '字段配置应该是数组');
    });
});
