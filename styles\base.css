/**
 * @file 基础样式文件
 * @description CSS变量定义、基础重置、通用样式
 * @version 5.0
 * @date 2024-12-21
 */

/* #region CSS变量定义 */
:root {
    /* A4纸张标准尺寸 */
    --a4-width-px: 794px;
    --a4-height-px: 1123px;
    
    /* 布局变量 - 修正为用户要求的尺寸 */
    --header-height: 130px;  /* 页眉高度 - 修正为130px */
    --footer-height: 110px;  /* 页脚高度 */
    --content-padding: 20px;

    /* 页边距 */
    --margin-top-px: 100px;
    --margin-bottom-px: 80px;
    --margin-left-px: 37.8px;
    --margin-right-px: 37.8px;

    /* 内容边距 - 修正为用户要求的30px */
    --content-margin-left: 30px;
    --content-margin-right: 30px;
    
    /* 颜色变量 */
    --primary-color: #1e40af;
    --secondary-color: #3b82f6;
    --accent-color: #f59e0b;
    --light-color: #f3f4f6;
    --dark-color: #1f2937;
    --background-color: #f9fafb;
    --text-color: #333333;
    --border-color: #e5e7eb;
    
    /* 字体变量 */
    --base-font-family: 'Roboto', 'Noto Sans SC', sans-serif;
    --classic-font-family: 'Times New Roman', 'SimSun', serif;
    --elegant-font-family: 'Georgia', 'STZhongsong', serif;
    --base-font-size: 11pt;
    --title-font-size: 18pt;
    --small-font-size: 9pt;
    --line-height: 1.5;
    
    /* 阴影变量 */
    --box-shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --box-shadow-medium: 0 4px 15px -1px rgba(0, 0, 0, 0.1), 0 6px 8px -1px rgba(0, 0, 0, 0.05);
    
    /* 过渡变量 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    
    /* Z-index层级 - 修复重叠问题，印章应该在最上层 */
    --z-index-header: 100;
    --z-index-footer: 100;
    --z-index-total: 200;       /* 总金额层级 */
    --z-index-stamp: 300;       /* 印章最高层级，确保始终浮在最上层 */
    
    /* 预览容器缩放比例 */
    --preview-scale-factor: 0.75;
    
    /* 印章定位 - 修正为用户要求的15px左移 */
    --stamp-bottom-offset: 15%;
    --stamp-right-offset: calc(5% + 15px);
    
    /* 图片相关变量 */
    --image-quality-rendering: high-quality;
    --image-object-fit-header: cover;   /* 页眉图片：完全填满130px区域 */
    --image-object-fit-footer: cover;   /* 页脚图片：完全填满110px区域 */
    --image-object-fit-stamp: contain;  /* 印章图片：保持完整显示 */
    --image-object-position: center;
    
    /* 导出相关变量 */
    --export-dpi: 300;
    --export-quality: 1.0;
}
/* #endregion */

/* #region 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--base-font-family);
    font-size: var(--base-font-size);
    line-height: var(--line-height);
    color: var(--text-color);
    background-color: var(--background-color);
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 清除浮动 */
.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

/* 无选择文本 */
.no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
/* #endregion */

/* #region 通用工具类 */
/* 文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* 显示控制 */
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }

/* Flex布局 */
.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flex-column {
    flex-direction: column;
}

/* 边距工具类 */
.m-0 { margin: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }

.p-0 { padding: 0; }
.pt-1 { padding-top: 0.25rem; }
.pt-2 { padding-top: 0.5rem; }
.pt-3 { padding-top: 1rem; }
.pb-1 { padding-bottom: 0.25rem; }
.pb-2 { padding-bottom: 0.5rem; }
.pb-3 { padding-bottom: 1rem; }

/* 内容边距工具类 - 修复文字紧贴边缘问题 */
.content-margin {
    padding-left: var(--content-margin-left);
    padding-right: var(--content-margin-right);
    box-sizing: border-box;
}

.content-margin-table {
    margin-left: var(--content-margin-left);
    margin-right: var(--content-margin-right);
    width: calc(100% - var(--content-margin-left) - var(--content-margin-right));
}
/* #endregion */

/* #region 图片基础样式 - 防拉伸变形核心 */
/* 图片通用样式 - 防止拉伸变形的基础设置 */
img {
    /* 防拉伸变形的关键属性 */
    min-width: 0;
    min-height: 0;
    flex-shrink: 0;
    
    /* 高质量渲染 */
    image-rendering: var(--image-quality-rendering);
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    
    /* 硬件加速 */
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    
    /* 字体平滑 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 页眉图片基础样式 - 完全填满130px区域 */
.document-header-image-container img {
    height: 100%;
    width: 100%; /* 修改为100%以配合cover填充 */
    object-fit: var(--image-object-fit-header); /* 使用cover完全填满 */
    object-position: var(--image-object-position);
    margin: 0;
    display: block;
}

/* 页脚图片基础样式 - 完全填满110px区域 */
.company-footer-image-container img,
.unified-document-footer img {
    height: 100%;
    width: 100%; /* 修改为100%以配合cover填充 */
    object-fit: var(--image-object-fit-footer); /* 使用cover完全填满 */
    object-position: var(--image-object-position);
    margin: 0;
    display: block;
}

/* 印章图片基础样式 - 真正的半透明叠加效果，去除白色背景 */
.company-stamp img {
    width: 100%;
    height: 100%;
    object-fit: var(--image-object-fit-stamp); /* 使用contain保持完整显示 */
    object-position: var(--image-object-position);
    display: block;
    margin: 0;
    padding: 0;
    opacity: 0.75; /* 调整为0.75，更好的透明叠加效果 */
    background: transparent !important; /* 强制透明背景 */
    background-color: transparent !important; /* 强制透明背景色 */
    border: none !important; /* 移除边框 */
    box-shadow: none !important; /* 移除阴影 */
    /* 使用混合模式实现真正的透明叠加 */
    mix-blend-mode: multiply; /* 正片叠底模式，实现真实印章效果 */
    isolation: auto; /* 允许混合模式生效 */
}

/* 印章容器基础样式 - 防裁切，完全透明背景 */
.company-stamp {
    overflow: visible; /* 关键：防止印章被裁切 */
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent !important; /* 强制透明背景，避免遮挡 */
    background-color: transparent !important; /* 强制透明背景色 */
    border: none !important; /* 移除可能的边框 */
    box-shadow: none !important; /* 移除可能的阴影 */
}
/* #endregion */

/* #region 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-success { background-color: #10b981; }
.status-error { background-color: #ef4444; }
.status-warning { background-color: #f59e0b; }
.status-info { background-color: var(--primary-color); }
/* #endregion */

/* #region 占位符基础样式 */
.image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #666;
    font-size: 10px;
    opacity: 0.7;
    background: #f9f9f9;
    border: 2px dashed #ddd;
    border-radius: 4px;
    padding: 10px;
    box-sizing: border-box;
    line-height: 1.4;
}
/* #endregion */

/* #region 加载动画 */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
/* #endregion */
