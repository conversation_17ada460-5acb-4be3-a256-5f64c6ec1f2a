/**
 * 应用配置模块
 * @file app-config.js - 应用程序的核心配置管理
 * @description 统一管理应用程序的配置信息，包括公司信息、货币配置、AI配置等
 */

/**
 * 应用程序主配置
 * @namespace AppConfig - 应用程序的主要配置对象
 */
const AppConfig = {
    // 基础配置
    currentCompany: 'gomyhire',     // 当前选中的公司
    currentCurrency: 'MYR',         // 当前选中的货币
    
    // 多订单模式配置
    multiOrderMode: false,          // 是否启用多订单模式
    multiOrderData: [],             // 多订单数据数组
    currentOrderIndex: 0,           // 当前选中的订单索引
    orderCounter: 1,                // 订单计数器
    
    // AI配置
    geminiApiKey: '',               // Gemini API密钥
    geminiModel: 'gemini-2.5-flash-preview-05-20', // 使用的AI模型
    geminiApiVersion: 'v1beta',     // API版本
    geminiTemperature: 0.1,         // AI温度参数
    geminiMaxTokens: 8192,          // 最大token数
    
    // 调试配置
    debugMode: false,               // 调试模式开关
    logLevel: 'INFO',               // 日志级别
    
    // 性能配置
    debounceDelay: 300,             // 防抖延迟时间(ms)
    maxRetryCount: 3,               // 最大重试次数
    
    // 导出配置
    exportTimeout: 60000,           // 导出超时时间(ms)
    exportQuality: 300,             // 导出DPI质量
    
    /**
     * 更新配置
     * @function updateConfig - 更新应用配置
     * @param {Object} newConfig - 新的配置对象
     */
    updateConfig(newConfig) {
        Object.assign(this, newConfig);
        this.saveToLocalStorage();
    },
    
    /**
     * 保存配置到本地存储
     * @function saveToLocalStorage - 保存配置到localStorage
     */
    saveToLocalStorage() {
        try {
            const configToSave = {
                currentCompany: this.currentCompany,
                currentCurrency: this.currentCurrency,
                debugMode: this.debugMode,
                logLevel: this.logLevel
            };
            localStorage.setItem('smartoffice_config', JSON.stringify(configToSave));
        } catch (error) {
            console.warn('保存配置到本地存储失败:', error);
        }
    },
    
    /**
     * 从本地存储加载配置
     * @function loadFromLocalStorage - 从localStorage加载配置
     */
    loadFromLocalStorage() {
        try {
            const savedConfig = localStorage.getItem('smartoffice_config');
            if (savedConfig) {
                const config = JSON.parse(savedConfig);
                Object.assign(this, config);
            }
        } catch (error) {
            console.warn('从本地存储加载配置失败:', error);
        }
    }
};

/**
 * 货币配置
 * @namespace CurrencyConfig - 支持的货币配置
 */
const CurrencyConfig = {
    'MYR': {
        symbol: 'RM',
        name: '马来西亚林吉特',
        code: 'MYR',
        decimals: 2
    },
    'CNY': {
        symbol: '¥',
        name: '人民币',
        code: 'CNY',
        decimals: 2
    },
    'USD': {
        symbol: '$',
        name: '美元',
        code: 'USD',
        decimals: 2
    },
    'EUR': {
        symbol: '€',
        name: '欧元',
        code: 'EUR',
        decimals: 2
    }
};

/**
 * 公司信息配置
 * @namespace CompanyInfo - 支持的公司信息
 */
const CompanyInfo = {
    'gomyhire': {
        name: 'GoMyHire',
        fullName: 'GoMyHire Sdn Bhd',
        address: 'Kuala Lumpur, Malaysia',
        phone: '+60-***********',
        email: '<EMAIL>',
        website: 'www.gomyhire.com',
        logo: 'gomyhire-logo.png'
    },
    'sky-mirror': {
        name: 'Sky Mirror',
        fullName: 'Sky Mirror Tours Sdn Bhd',
        address: 'Selangor, Malaysia',
        phone: '+60-***********',
        email: '<EMAIL>',
        website: 'www.skymirror.com',
        logo: 'sky-mirror-logo.png'
    }
};

/**
 * 导出配置
 * @namespace ExportConfig - 导出相关配置
 */
const ExportConfig = {
    // A4尺寸配置 (300DPI)
    a4: {
        widthPx: 794,      // A4宽度(像素)
        heightPx: 1123,    // A4高度(像素)
        widthMm: 210,      // A4宽度(毫米)
        heightMm: 297,     // A4高度(毫米)
        dpi: 300           // 分辨率
    },
    
    // 图片配置
    images: {
        header: {
            height: 130,    // 页眉图片高度
            width: 794      // 页眉图片宽度
        },
        footer: {
            height: 110,    // 页脚图片高度
            width: 794      // 页脚图片宽度
        },
        stamp: {
            width: 96,      // 印章宽度
            height: 96,     // 印章高度
            opacity: 0.9    // 印章透明度
        }
    },
    
    // 质量配置
    quality: {
        dpi: 300,           // 导出DPI
        scale: 2,           // 缩放倍数
        format: 'png',      // 默认格式
        compression: 0.95   // 压缩质量
    },
    
    // 超时配置
    timeout: 60000,         // 导出超时时间(ms)
    
    // 边距配置
    margins: {
        left: 30,           // 左边距
        right: 30,          // 右边距
        top: 20,            // 上边距
        bottom: 20          // 下边距
    }
};

/**
 * 模板配置
 * @namespace TemplateConfig - 模板相关配置
 */
const TemplateConfig = {
    // 发票模板配置
    invoice: {
        title: '发票 / INVOICE',
        showCompanyInfo: true,
        showTaxInfo: true,
        showStamp: true,
        fields: [
            'companyName', 'taxId', 'companyAddress', 
            'companyPhone', 'contactPerson'
        ]
    },
    
    // 收据模板配置
    receipt: {
        title: '收据 / RECEIPT',
        showCompanyInfo: false,
        showTaxInfo: false,
        showStamp: true,
        fields: []
    },
    
    // 通用配置
    common: {
        dateFormat: 'YYYY-MM-DD',
        timeFormat: 'HH:mm:ss',
        numberFormat: '0,0.00',
        maxItems: 50,
        maxDescriptionLength: 200
    }
};

// 导出配置对象到全局作用域
if (typeof window !== 'undefined') {
    window.AppConfig = AppConfig;
    window.CurrencyConfig = CurrencyConfig;
    window.CompanyInfo = CompanyInfo;
    window.ExportConfig = ExportConfig;
    window.TemplateConfig = TemplateConfig;
    
    // 初始化时从本地存储加载配置
    AppConfig.loadFromLocalStorage();
    
    console.log('✅ 应用配置模块已加载');
}

// ES6模块导出
export { AppConfig, CurrencyConfig, CompanyInfo, ExportConfig, TemplateConfig };
