/**
 * 预览模块 - 发票/收据生成器预览功能
 * @file preview-module.js
 * @description 独立的预览功能模块，包含预览渲染、更新机制、模板生成等核心功能
 * @version 1.0.0
 * <AUTHOR> Team
 * @date 2024-12-21
 */

/**
 * 预览模块命名空间
 * @namespace PreviewModule
 * @description 包含所有预览相关的功能和配置
 */
const PreviewModule = (function() {
    'use strict';

    // #region 私有变量和配置
    /**
     * 预览更新重试计数器
     * @private
     */
    let previewUpdateRetryCount = 0;
    const MAX_PREVIEW_RETRY = 3;

    /**
     * 预览状态管理
     * @private
     */
    const previewState = {
        isUpdating: false,
        pendingUpdate: false,
        updateRetryCount: 0,
        maxRetries: 3
    };

    /**
     * 模块配置
     * @private
     */
    const config = {
        debounceDelay: 200,
        autoPreviewEnabled: true,
        maxRetries: 3,
        retryDelay: 100
    };
    // #endregion

    // #region 依赖检查器
    /**
     * 依赖检查器
     * @namespace DependencyChecker
     * @description 检查预览模块所需的外部依赖
     */
    const DependencyChecker = {
        /**
         * 检查必需的函数依赖
         * @function checkFunctionDependencies
         * @description 检查预览模块需要的外部函数是否存在
         * @returns {Object} 依赖检查结果
         */
        checkFunctionDependencies() {
            const requiredFunctions = {
                collectFormData: typeof collectFormData === 'function',
                formatCurrency: typeof formatCurrency === 'function',
                getCurrentCurrencySymbol: typeof getCurrentCurrencySymbol === 'function',
                escapeHtml: typeof escapeHtml === 'function',
                checkA4ContentOverflow: typeof checkA4ContentOverflow === 'function'
            };

            const optionalFunctions = {
                updateTotalAmount: typeof updateTotalAmount === 'function',
                generateDocumentNumber: typeof generateDocumentNumber === 'function'
            };

            return {
                required: requiredFunctions,
                optional: optionalFunctions,
                allRequiredAvailable: Object.values(requiredFunctions).every(Boolean)
            };
        },

        /**
         * 检查模板类依赖
         * @function checkTemplateDependencies
         * @description 检查模板类是否存在
         * @returns {Object} 模板依赖检查结果
         */
        checkTemplateDependencies() {
            return {
                InvoiceTemplate: typeof InvoiceTemplate !== 'undefined',
                ReceiptTemplate: typeof ReceiptTemplate !== 'undefined',
                allTemplatesAvailable: typeof InvoiceTemplate !== 'undefined' && typeof ReceiptTemplate !== 'undefined'
            };
        },

        /**
         * 检查配置对象依赖
         * @function checkConfigDependencies
         * @description 检查配置对象是否存在
         * @returns {Object} 配置依赖检查结果
         */
        checkConfigDependencies() {
            return {
                AppConfig: typeof AppConfig !== 'undefined',
                CompanyInfo: typeof CompanyInfo !== 'undefined',
                CurrencyConfig: typeof CurrencyConfig !== 'undefined',
                ImageBase64: typeof ImageBase64 !== 'undefined'
            };
        },

        /**
         * 检查CSS模块依赖
         * @function checkCSSModules
         * @description 检查CSS模块是否正确加载
         * @returns {Object} CSS模块检查结果
         */
        checkCSSModules() {
            const cssFiles = [
                'styles/base.css',
                'styles/layout.css',
                'styles/components.css',
                'styles/preview.css',
                'styles/export.css',
                'styles/print.css',
                'styles/responsive.css'
            ];

            const loadedStyles = Array.from(document.styleSheets).map(sheet => {
                try {
                    return sheet.href ? sheet.href.split('/').pop() : 'inline';
                } catch (e) {
                    return 'unknown';
                }
            });

            return {
                expectedFiles: cssFiles,
                loadedStyles: loadedStyles,
                cssModulesLoaded: cssFiles.some(file =>
                    loadedStyles.some(loaded => loaded.includes(file.split('/').pop()))
                )
            };
        },

        /**
         * 执行完整的依赖检查
         * @function checkAllDependencies
         * @description 执行所有依赖检查
         * @returns {Object} 完整的依赖检查结果
         */
        checkAllDependencies() {
            const results = {
                functions: this.checkFunctionDependencies(),
                templates: this.checkTemplateDependencies(),
                configs: this.checkConfigDependencies(),
                css: this.checkCSSModules(),
                timestamp: new Date().toISOString()
            };

            // 计算整体可用性
            results.overallStatus = {
                canRender: results.functions.allRequiredAvailable && results.templates.allTemplatesAvailable,
                hasConfigs: Object.values(results.configs).every(Boolean),
                hasCSSModules: results.css.cssModulesLoaded
            };

            return results;
        }
    };
    // #endregion

    // #region 工具函数
    /**
     * 安全执行函数
     * @function safeExecute
     * @description 安全执行函数，捕获并处理错误
     * @param {Function} func - 要执行的函数
     * @param {string} context - 执行上下文
     * @param {...any} args - 函数参数
     * @returns {any} 函数执行结果或null
     */
    function safeExecute(func, context, ...args) {
        try {
            return func(...args);
        } catch (error) {
            console.error(`[${context}] 执行失败:`, error);
            return null;
        }
    }

    /**
     * 检查DOM元素是否存在
     * @function checkDOMElement
     * @description 检查指定ID的DOM元素是否存在
     * @param {string} elementId - 元素ID
     * @returns {HTMLElement|null} DOM元素或null
     */
    function checkDOMElement(elementId) {
        const element = document.getElementById(elementId);
        if (!element) {
            console.warn(`⚠️ DOM元素不存在: ${elementId}`);
        }
        return element;
    }

    /**
     * 确保DOM完全就绪
     * @function ensureDOMReady
     * @description 确保DOM元素完全加载
     * @returns {Promise<boolean>} DOM是否就绪
     */
    async function ensureDOMReady() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve(true);
            } else {
                const checkReady = () => {
                    if (document.readyState === 'complete') {
                        resolve(true);
                    } else {
                        setTimeout(checkReady, 100);
                    }
                };
                checkReady();
            }
        });
    }
    // #endregion

    // #region 预览状态管理
    /**
     * 预览状态显示管理器
     * @namespace StatusManager
     * @description 管理预览状态的显示和隐藏
     */
    const StatusManager = {
        /**
         * 显示预览状态
         * @function show
         * @description 显示预览更新状态
         * @param {string} message - 状态消息
         * @param {string} type - 状态类型 ('updating', 'success', 'error', 'ready')
         */
        show(message, type = 'updating') {
            const statusElement = checkDOMElement('preview-status');
            const statusText = checkDOMElement('preview-status-text');

            if (statusElement && statusText) {
                statusText.textContent = message;

                // 重置类名并添加新的状态类
                statusElement.className = `preview-status-indicator ${type}`;

                // 显示或隐藏状态元素
                if (message.trim() === '') {
                    statusElement.classList.add('hidden');
                } else {
                    statusElement.classList.remove('hidden');
                }

                // 自动隐藏成功和错误状态（但不隐藏ready状态）
                if (type === 'success' || type === 'error') {
                    setTimeout(() => {
                        statusElement.classList.add('hidden');
                    }, 3000);
                }
            }
        },

        /**
         * 隐藏预览状态
         * @function hide
         * @description 隐藏预览状态指示器
         */
        hide() {
            const statusElement = checkDOMElement('preview-status');
            if (statusElement) {
                statusElement.classList.add('hidden');
            }
        }
    };
    // #endregion

    // #region 预览容器管理
    /**
     * 预览容器管理器
     * @namespace ContainerManager
     * @description 管理预览容器的创建、修复和维护
     */
    const ContainerManager = {
        /**
         * 获取预览容器
         * @function getContainer
         * @description 获取document-container元素，如果不存在则尝试修复
         * @returns {HTMLElement|null} 预览容器元素
         */
        getContainer() {
            let container = checkDOMElement('document-container');
            
            if (!container) {
                console.log('🔧 预览容器不存在，尝试修复...');
                container = this.repairContainer();
            }
            
            return container;
        },

        /**
         * 修复预览容器
         * @function repairContainer
         * @description 尝试重新创建预览容器结构
         * @returns {HTMLElement|null} 修复后的容器元素
         */
        repairContainer() {
            // 防止无限循环：检查重试次数
            if (previewUpdateRetryCount >= MAX_PREVIEW_RETRY) {
                console.error('❌ 预览容器修复重试次数已达上限，停止尝试');
                previewUpdateRetryCount = 0;
                return null;
            }

            console.log(`🔧 尝试重新创建预览容器结构 (第${previewUpdateRetryCount + 1}次尝试)`);

            // 尝试重新创建预览容器结构
            const previewContainer = checkDOMElement('document-preview');
            if (previewContainer) {
                previewContainer.innerHTML = `
                    <div id="document-container">
                        <div class="empty-preview-message">
                            请填写表单信息并点击"更新预览"按钮<br>
                            Please fill in the form and click "Update Preview" button
                        </div>
                    </div>
                `;
                console.log('✅ 预览容器结构已重新创建');
                
                // 增加重试计数器
                previewUpdateRetryCount++;
                
                return document.getElementById('document-container');
            } else {
                console.error('❌ document-preview元素也不存在，无法修复预览功能');
                previewUpdateRetryCount = 0;
                return null;
            }
        },

        /**
         * 验证容器结构
         * @function validateStructure
         * @description 验证预览容器结构的完整性
         * @param {HTMLElement} container - 预览容器
         * @param {string} html - 要插入的HTML内容
         */
        validateStructure(container, html) {
            // 确保document-preview容器结构完整（不直接覆盖内容）
            const exportContainer = checkDOMElement('document-preview');
            if (exportContainer && exportContainer !== container) {
                // 检查document-preview是否包含document-container
                const innerContainer = exportContainer.querySelector('#document-container');
                if (!innerContainer) {
                    // 如果document-container不存在，重新创建结构
                    console.log('🔧 重新创建document-container结构');
                    exportContainer.innerHTML = `<div id="document-container">${html}</div>`;
                }
                console.log(`✅ 预览更新完成 - 保持了正确的容器结构`);
            } else {
                console.log(`✅ 预览更新完成 - 使用统一容器`);
            }
        }
    };
    // #endregion

    // #region 模板渲染引擎
    /**
     * 模板渲染引擎
     * @namespace TemplateEngine
     * @description 处理发票和收据模板的渲染
     */
    const TemplateEngine = {
        /**
         * 渲染预览内容
         * @function render
         * @description 根据数据和文档类型渲染预览内容
         * @param {Object} data - 表单数据
         * @returns {string} 渲染后的HTML字符串
         */
        render(data) {
            try {
                let html = '';
                
                // 检查模板类是否存在
                if (typeof InvoiceTemplate === 'undefined' || typeof ReceiptTemplate === 'undefined') {
                    throw new Error('模板类未定义，请确保InvoiceTemplate和ReceiptTemplate已加载');
                }

                if (data.documentType === 'invoice') {
                    html = InvoiceTemplate.render(data);
                } else {
                    html = ReceiptTemplate.render(data);
                }

                return html;
            } catch (error) {
                console.error('❌ 模板渲染失败:', error);
                return this.renderError(error);
            }
        },

        /**
         * 渲染错误信息
         * @function renderError
         * @description 渲染错误信息的HTML
         * @param {Error} error - 错误对象
         * @returns {string} 错误信息HTML
         */
        renderError(error) {
            return `
                <div class="error-message" style="color: red; padding: 20px; text-align: center;">
                    预览渲染失败，请检查数据格式<br>
                    Preview rendering failed, please check data format<br>
                    <small>${error.message}</small>
                </div>
            `;
        }
    };
    // #endregion

    // #region 预览更新管理器
    /**
     * 预览更新管理器
     * @namespace UpdateManager
     * @description 管理预览的更新逻辑和性能优化
     */
    const UpdateManager = {
        /**
         * 更新预览
         * @function updatePreview
         * @description 更新文档预览，支持发票和收据模板
         */
        async updatePreview() {
            // 显示更新状态
            StatusManager.show('更新预览中... / Updating preview...', 'updating');

            // 检查数据收集函数是否存在
            if (typeof collectFormData !== 'function') {
                console.error('❌ collectFormData函数未定义');
                StatusManager.show('数据收集失败 / Data collection failed', 'error');
                return;
            }

            const data = collectFormData();

            console.log(`🔄 更新预览 - 数据收集:`, {
                项目数量: data.items.length,
                总金额原始值: data.total,
                总金额格式化: typeof formatCurrency === 'function' ? formatCurrency(data.total) : data.total,
                货币符号: typeof getCurrentCurrencySymbol === 'function' ? getCurrentCurrencySymbol() : 'RM',
                文档类型: data.documentType,
                模式: (typeof AppConfig !== 'undefined' && AppConfig.multiOrderMode) ? '多订单' : '单订单',
                显示模式: (typeof AppConfig !== 'undefined' && AppConfig.displayMode) || '标准'
            });

            // 获取预览容器
            const container = ContainerManager.getContainer();
            if (!container) {
                console.error('❌ 无法获取或修复预览容器');
                StatusManager.show('预览容器错误 / Container error', 'error');
                return;
            }

            // 成功找到容器，重置重试计数器
            previewUpdateRetryCount = 0;

            // 自动填充单据号码和日期
            this.autoFillFields(data);

            try {
                // 渲染预览内容
                const html = TemplateEngine.render(data);
                container.innerHTML = html;
                console.log('✅ 预览内容已更新到document-container');

                // 验证容器结构
                ContainerManager.validateStructure(container, html);

                // 更新配置
                if (typeof AppConfig !== 'undefined') {
                    AppConfig.currentDocumentType = data.documentType;
                }

                // 检查A4内容溢出（延迟执行以确保DOM更新完成）
                setTimeout(() => {
                    if (typeof checkA4ContentOverflow === 'function') {
                        checkA4ContentOverflow();
                    }
                }, 100);

                // 显示成功状态
                StatusManager.show('预览更新完成 / Preview updated', 'success');

            } catch (error) {
                console.error('❌ 预览渲染失败:', error);
                container.innerHTML = TemplateEngine.renderError(error);
                StatusManager.show('预览更新失败 / Preview update failed', 'error');
            }
        },

        /**
         * 自动填充字段
         * @function autoFillFields
         * @description 自动填充单据号码和日期字段
         * @param {Object} data - 表单数据
         */
        autoFillFields(data) {
            // 自动填充单据号码（如果为空）
            const documentNumberInput = checkDOMElement('document-number');
            if (documentNumberInput && !documentNumberInput.value) {
                documentNumberInput.value = data.documentNumber;
            }

            // 自动填充日期（如果为空）
            const documentDateInput = checkDOMElement('document-date');
            if (documentDateInput && !documentDateInput.value) {
                documentDateInput.value = data.date;
            }
        },

        /**
         * 安全的预览更新
         * @function safeUpdatePreview
         * @description 安全的预览更新，避免重复调用和错误处理
         */
        async safeUpdatePreview() {
            if (previewState.isUpdating) {
                previewState.pendingUpdate = true;
                return;
            }

            // 检查重试次数，防止无限循环
            if (previewState.updateRetryCount >= previewState.maxRetries) {
                console.warn('⚠️ 预览更新重试次数已达上限，停止更新');
                previewState.updateRetryCount = 0;
                previewState.pendingUpdate = false;
                return;
            }

            previewState.isUpdating = true;
            try {
                // 检查关键DOM元素是否存在
                const documentContainer = checkDOMElement('document-container');
                const documentPreview = checkDOMElement('document-preview');

                if (!documentContainer && !documentPreview) {
                    console.error('❌ 关键预览元素缺失，跳过预览更新');
                    return;
                }

                await this.updatePreview();

                // 成功更新，重置重试计数器
                previewState.updateRetryCount = 0;

                // 如果有待处理的更新，再次执行（但要检查重试次数）
                if (previewState.pendingUpdate) {
                    previewState.pendingUpdate = false;
                    previewState.updateRetryCount++;
                    if (previewState.updateRetryCount < previewState.maxRetries) {
                        setTimeout(() => this.safeUpdatePreview(), config.retryDelay);
                    } else {
                        console.warn('⚠️ 待处理更新重试次数已达上限');
                        previewState.updateRetryCount = 0;
                    }
                }
            } catch (error) {
                console.error('预览更新失败:', error);
                previewState.updateRetryCount++;

                // 只在重试次数未达上限时尝试恢复
                if (previewState.updateRetryCount < previewState.maxRetries) {
                    ContainerManager.repairContainer();
                }
            } finally {
                previewState.isUpdating = false;
            }
        }
    };
    // #endregion

    // #region 防抖管理器
    /**
     * 防抖管理器
     * @namespace DebounceManager
     * @description 管理预览更新的防抖功能
     */
    const DebounceManager = {
        timers: new Map(),

        /**
         * 创建防抖函数
         * @function debounce
         * @description 创建防抖函数，避免频繁调用
         * @param {Function} func - 要防抖的函数
         * @param {number} delay - 延迟时间（毫秒）
         * @param {string} key - 防抖键名
         * @returns {Function} 防抖后的函数
         */
        debounce(func, delay, key) {
            return (...args) => {
                // 清除之前的定时器
                if (this.timers.has(key)) {
                    clearTimeout(this.timers.get(key));
                }

                // 设置新的定时器
                const timer = setTimeout(() => {
                    this.timers.delete(key);
                    func.apply(this, args);
                }, delay);

                this.timers.set(key, timer);
            };
        },

        /**
         * 清除所有防抖定时器
         * @function clearAll
         * @description 清除所有防抖定时器
         */
        clearAll() {
            this.timers.forEach(timer => clearTimeout(timer));
            this.timers.clear();
        }
    };
    // #endregion

    // #region 预览内容提取器
    /**
     * 预览内容提取器
     * @namespace ContentExtractor
     * @description 从预览中提取各种内容的工具函数
     */
    const ContentExtractor = {
        /**
         * 从预览中提取文字内容
         * @function extractText
         * @description 从预览容器中提取指定关键词的文字内容
         * @param {HTMLElement} container - 预览容器
         * @param {...string} keywords - 关键词列表
         * @returns {string} 提取的文字内容
         */
        extractText(container, ...keywords) {
            try {
                for (const keyword of keywords) {
                    const elements = container.querySelectorAll('*');
                    for (const element of elements) {
                        if (element.textContent.includes(keyword)) {
                            const text = element.textContent;
                            const match = text.match(new RegExp(`${keyword}[：:]*\\s*([^\\n\\r]+)`));
                            if (match) {
                                return match[1].trim();
                            }
                        }
                    }
                }
                return '';
            } catch (error) {
                console.error('提取文字内容失败:', error);
                return '';
            }
        },

        /**
         * 从预览中提取项目列表
         * @function extractItems
         * @description 从预览表格中提取项目数据
         * @param {HTMLElement} container - 预览容器
         * @returns {Array} 项目数组
         */
        extractItems(container) {
            try {
                const rows = container.querySelectorAll('table tbody tr');
                return Array.from(rows).map(row => {
                    const cells = row.querySelectorAll('td');
                    return {
                        description: cells[1]?.textContent.trim() || '',
                        quantity: cells[2]?.textContent.trim() || '',
                        price: cells[3]?.textContent.trim() || '',
                        amount: cells[4]?.textContent.trim() || ''
                    };
                });
            } catch (error) {
                console.error('提取项目失败:', error);
                return [];
            }
        },

        /**
         * 从预览中提取总金额
         * @function extractTotal
         * @description 从预览中提取总金额信息
         * @param {HTMLElement} container - 预览容器
         * @returns {string} 总金额文字
         */
        extractTotal(container) {
            try {
                const totalElement = container.querySelector('.total-amount-container');
                return totalElement ? totalElement.textContent.trim() : '';
            } catch (error) {
                console.error('提取总金额失败:', error);
                return '';
            }
        },

        /**
         * 从预览中提取备注
         * @function extractNotes
         * @description 从预览中提取备注内容
         * @param {HTMLElement} container - 预览容器
         * @returns {string} 备注内容
         */
        extractNotes(container) {
            try {
                const notesElements = container.querySelectorAll('*');
                for (const element of notesElements) {
                    if (element.textContent.includes('备注') || element.textContent.includes('Notes')) {
                        const parent = element.parentElement;
                        const notesDiv = parent?.querySelector('div[style*="pre-wrap"]');
                        return notesDiv ? notesDiv.textContent.trim() : '';
                    }
                }
                return '';
            } catch (error) {
                console.error('提取备注失败:', error);
                return '';
            }
        },

        /**
         * 调试内容一致性
         * @function debugContentConsistency
         * @description 诊断预览内容与表单数据的一致性问题
         * @returns {Object} 诊断结果
         */
        debugContentConsistency() {
            console.log('🔍 开始内容一致性诊断...');

            try {
                // 检查数据收集函数是否存在
                if (typeof collectFormData !== 'function') {
                    return { error: 'collectFormData函数未定义' };
                }

                // 1. 收集表单数据
                const formData = collectFormData();
                console.log('📋 表单数据:', formData);

                // 2. 分析预览容器内容
                const container = checkDOMElement('document-container');
                if (!container) {
                    console.error('❌ 预览容器不存在');
                    return { error: '预览容器不存在' };
                }

                // 3. 提取预览中的文字内容
                const previewText = {
                    customerName: this.extractText(container, '客户名称', '客户'),
                    documentNumber: this.extractText(container, '单据号码', 'Document Number'),
                    items: this.extractItems(container),
                    totalAmount: this.extractTotal(container),
                    notes: this.extractNotes(container)
                };

                console.log('🖼️ 预览内容:', previewText);

                // 4. 对比分析
                const comparison = {
                    customerName: {
                        form: formData.customerName,
                        preview: previewText.customerName,
                        match: formData.customerName === previewText.customerName
                    },
                    documentNumber: {
                        form: formData.documentNumber,
                        preview: previewText.documentNumber,
                        match: formData.documentNumber === previewText.documentNumber
                    },
                    itemsCount: {
                        form: formData.items.length,
                        preview: previewText.items.length,
                        match: formData.items.length === previewText.items.length
                    }
                };

                console.log('🔍 对比结果:', comparison);
                return {
                    formData,
                    previewText,
                    comparison,
                    success: true
                };

            } catch (error) {
                console.error('❌ 内容一致性诊断失败:', error);
                return { error: error.message };
            }
        }
    };
    // #endregion

    // #region 初始化管理器
    /**
     * 初始化管理器
     * @namespace InitManager
     * @description 管理预览模块的初始化
     */
    const InitManager = {
        /**
         * 初始化默认预览显示
         * @function initializeDefaultPreview
         * @description 页面加载后自动显示默认预览
         */
        async initializeDefaultPreview() {
            console.log('🔄 初始化默认预览显示...');

            try {
                // 确保DOM元素已准备就绪
                await ensureDOMReady();

                setTimeout(() => {
                    // 检查预览容器是否存在
                    const container = ContainerManager.getContainer();
                    if (!container) {
                        console.warn('⚠️ 预览容器未找到，跳过默认预览初始化');
                        return;
                    }

                    // 显示初始化状态
                    StatusManager.show('正在生成默认预览... / Generating default preview...', 'updating');

                    // 检查数据收集函数是否存在
                    if (typeof collectFormData !== 'function') {
                        console.error('❌ collectFormData函数未定义，无法初始化默认预览');
                        StatusManager.show('初始化失败 / Initialization failed', 'error');
                        return;
                    }

                    // 收集当前表单的默认数据
                    const defaultData = collectFormData();

                    // 如果没有客户名称，设置一个默认示例
                    if (!defaultData.customerName || defaultData.customerName.trim() === '') {
                        defaultData.customerName = '示例客户 / Sample Customer';
                    }

                    // 如果没有项目，添加一个示例项目
                    if (!defaultData.items || defaultData.items.length === 0) {
                        defaultData.items = [{
                            description: '示例服务项目 / Sample Service Item',
                            quantity: 1,
                            price: 100.00,
                            amount: 100.00
                        }];
                        defaultData.total = 100.00;
                    }

                    // 生成预览HTML
                    const html = TemplateEngine.render(defaultData);

                    // 更新预览容器
                    container.innerHTML = html;

                    // 显示成功状态
                    StatusManager.show('默认预览已加载 / Default preview loaded', 'success');

                    console.log('✅ 默认预览显示完成');

                    // 3秒后隐藏状态指示器
                    setTimeout(() => {
                        StatusManager.show('', 'ready');
                    }, 3000);

                }, 100); // 短暂延迟确保DOM完全加载

            } catch (error) {
                console.error('❌ 默认预览初始化失败:', error);
                StatusManager.show('预览初始化失败 / Preview initialization failed', 'error');
            }
        },

        /**
         * 初始化预览模块
         * @function initialize
         * @description 初始化预览模块的所有功能
         */
        async initialize() {
            console.log('🚀 初始化预览模块...');

            try {
                // 确保DOM就绪
                await ensureDOMReady();

                // 检查依赖关系
                const dependencies = DependencyChecker.checkAllDependencies();
                console.log('🔍 依赖检查结果:', dependencies);

                // 验证关键依赖
                if (!dependencies.overallStatus.canRender) {
                    console.warn('⚠️ 关键依赖缺失，预览功能可能受限');

                    // 列出缺失的依赖
                    const missingFunctions = Object.entries(dependencies.functions.required)
                        .filter(([name, available]) => !available)
                        .map(([name]) => name);

                    if (missingFunctions.length > 0) {
                        console.warn('❌ 缺失的必需函数:', missingFunctions);
                    }

                    if (!dependencies.templates.allTemplatesAvailable) {
                        console.warn('❌ 模板类未完全加载');
                    }
                }

                // 检查CSS模块
                if (!dependencies.css.cssModulesLoaded) {
                    console.warn('⚠️ CSS模块可能未正确加载，样式可能受影响');
                }

                // 初始化默认预览（即使有依赖问题也尝试初始化）
                await this.initializeDefaultPreview();

                // 创建防抖的预览更新函数
                const debouncedUpdate = DebounceManager.debounce(
                    () => UpdateManager.safeUpdatePreview(),
                    config.debounceDelay,
                    'preview_update'
                );

                // 将防抖函数暴露给全局
                if (typeof window !== 'undefined') {
                    window.safeDebouncedUpdatePreview = debouncedUpdate;
                }

                // 设置依赖检查结果到配置中
                config.lastDependencyCheck = dependencies;

                console.log('✅ 预览模块初始化完成');
                return {
                    success: true,
                    dependencies: dependencies
                };

            } catch (error) {
                console.error('❌ 预览模块初始化失败:', error);
                return {
                    success: false,
                    error: error.message
                };
            }
        }
    };
    // #endregion

    // #region 公共API
    /**
     * 预览模块公共API
     * @description 暴露给外部使用的公共接口
     */
    const PublicAPI = {
        /**
         * 更新预览
         * @function updatePreview
         * @description 手动更新预览
         */
        updatePreview: () => UpdateManager.updatePreview(),

        /**
         * 安全更新预览
         * @function safeUpdatePreview
         * @description 安全的预览更新，带错误处理
         */
        safeUpdatePreview: () => UpdateManager.safeUpdatePreview(),

        /**
         * 显示预览状态
         * @function showStatus
         * @description 显示预览状态信息
         * @param {string} message - 状态消息
         * @param {string} type - 状态类型
         */
        showStatus: (message, type) => StatusManager.show(message, type),

        /**
         * 隐藏预览状态
         * @function hideStatus
         * @description 隐藏预览状态信息
         */
        hideStatus: () => StatusManager.hide(),

        /**
         * 初始化预览模块
         * @function init
         * @description 初始化预览模块
         */
        init: () => InitManager.initialize(),

        /**
         * 获取模块配置
         * @function getConfig
         * @description 获取模块配置
         * @returns {Object} 配置对象
         */
        getConfig: () => ({ ...config }),

        /**
         * 更新模块配置
         * @function updateConfig
         * @description 更新模块配置
         * @param {Object} newConfig - 新的配置
         */
        updateConfig: (newConfig) => {
            Object.assign(config, newConfig);
            console.log('📝 预览模块配置已更新:', config);
        },

        /**
         * 获取模块状态
         * @function getStatus
         * @description 获取模块当前状态
         * @returns {Object} 状态对象
         */
        getStatus: () => ({
            isUpdating: previewState.isUpdating,
            pendingUpdate: previewState.pendingUpdate,
            updateRetryCount: previewState.updateRetryCount,
            activeTimers: DebounceManager.timers.size
        }),

        /**
         * 清理模块资源
         * @function cleanup
         * @description 清理模块使用的资源
         */
        cleanup: () => {
            DebounceManager.clearAll();
            previewState.isUpdating = false;
            previewState.pendingUpdate = false;
            previewState.updateRetryCount = 0;
            console.log('🧹 预览模块资源已清理');
        },

        /**
         * 调试内容一致性
         * @function debugContentConsistency
         * @description 诊断预览内容与表单数据的一致性
         */
        debugContentConsistency: () => ContentExtractor.debugContentConsistency(),

        /**
         * 提取预览文字内容
         * @function extractTextFromPreview
         * @description 从预览中提取文字内容（向后兼容）
         */
        extractTextFromPreview: (container, ...keywords) => ContentExtractor.extractText(container, ...keywords),

        /**
         * 提取预览项目
         * @function extractItemsFromPreview
         * @description 从预览中提取项目列表（向后兼容）
         */
        extractItemsFromPreview: (container) => ContentExtractor.extractItems(container),

        /**
         * 提取预览总金额
         * @function extractTotalFromPreview
         * @description 从预览中提取总金额（向后兼容）
         */
        extractTotalFromPreview: (container) => ContentExtractor.extractTotal(container),

        /**
         * 提取预览备注
         * @function extractNotesFromPreview
         * @description 从预览中提取备注（向后兼容）
         */
        extractNotesFromPreview: (container) => ContentExtractor.extractNotes(container),

        /**
         * 检查依赖关系
         * @function checkDependencies
         * @description 检查预览模块的所有依赖关系
         * @returns {Object} 依赖检查结果
         */
        checkDependencies: () => DependencyChecker.checkAllDependencies(),

        /**
         * 检查模块健康状态
         * @function checkHealth
         * @description 检查预览模块的健康状态
         * @returns {Object} 健康状态报告
         */
        checkHealth: () => {
            const dependencies = DependencyChecker.checkAllDependencies();
            const status = PublicAPI.getStatus();

            return {
                dependencies: dependencies,
                moduleStatus: status,
                health: {
                    canRender: dependencies.overallStatus.canRender,
                    isStable: !status.isUpdating && status.updateRetryCount === 0,
                    hasActiveTimers: status.activeTimers > 0,
                    cssLoaded: dependencies.css.cssModulesLoaded
                },
                recommendations: dependencies.overallStatus.canRender ?
                    ['模块运行正常'] :
                    ['检查缺失的依赖函数', '确保模板类已加载', '验证CSS模块加载']
            };
        }
    };
    // #endregion

    // #region 模块导出
    /**
     * 返回预览模块的公共接口
     * @description 暴露预览模块的所有功能
     */
    return {
        // 公共API
        ...PublicAPI,

        // 内部管理器（用于调试和扩展）
        StatusManager,
        ContainerManager,
        TemplateEngine,
        UpdateManager,
        DebounceManager,
        InitManager,
        ContentExtractor,
        DependencyChecker,

        // 配置和状态
        config,
        getStatus: PublicAPI.getStatus
    };
    // #endregion

})();

// #region 全局暴露和兼容性
/**
 * 全局暴露预览模块
 * @description 将预览模块暴露到全局作用域，保持向后兼容
 */
if (typeof window !== 'undefined') {
    // 暴露预览模块到全局
    window.PreviewModule = PreviewModule;

    // 保持向后兼容的全局函数
    window.updatePreview = PreviewModule.updatePreview;
    window.showPreviewStatus = PreviewModule.showStatus;
    window.hidePreviewStatus = PreviewModule.hideStatus;
    window.debugContentConsistency = PreviewModule.debugContentConsistency;
    window.extractTextFromPreview = PreviewModule.extractTextFromPreview;
    window.extractItemsFromPreview = PreviewModule.extractItemsFromPreview;
    window.extractTotalFromPreview = PreviewModule.extractTotalFromPreview;
    window.extractNotesFromPreview = PreviewModule.extractNotesFromPreview;
    window.checkPreviewDependencies = PreviewModule.checkDependencies;
    window.checkPreviewHealth = PreviewModule.checkHealth;

    console.log('🌐 预览模块已暴露到全局作用域');
}

// 如果是Node.js环境，导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PreviewModule;
}
// #endregion
