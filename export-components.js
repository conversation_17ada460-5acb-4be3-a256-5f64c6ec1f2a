/**
 * @file 导出组件模块 - Export Components Module
 * @description 发票/收据生成器的导出功能独立模块，包含PDF和图片导出的所有组件
 * <AUTHOR> Team
 * @version 3.0
 * @date 2024-12-21
 * 
 * 主要功能：
 * - 300DPI高质量PDF/图片导出
 * - A4尺寸精确控制(794x1123px)
 * - 页眉/页脚图片处理(150px/110px)
 * - 印章透明度控制(0.9)
 * - 多订单支持
 * - 样式一致性保证
 */

// #region 导出配置模块
/**
 * 导出系统配置
 * @description 统一的导出配置，包含A4尺寸、DPI设置、质量参数等
 */
const ExportConfig = {
    // A4页面尺寸 (210mm x 297mm)
    a4: {
        width: 210, // mm
        height: 297, // mm
        widthPx: 794, // px at 96 DPI
        heightPx: 1123 // px at 96 DPI
    },
    
    // 300DPI高质量设置
    dpi: 300,
    quality: {
        scale: 3.125, // 300DPI / 96DPI = 3.125 (精确达到300DPI)
        pngQuality: 1.0,
        jpegQuality: 0.95
    },
    
    // 图片尺寸标准 - 修正为用户要求的130px页眉
    images: {
        header: { height: 130 }, // 页眉固定高度130px
        footer: { height: 110 }  // 页脚固定高度110px
    },
    
    // 印章配置 - 修正为用户要求的15px左移，优化透明度实现真正叠加效果
    stamp: {
        opacity: 0.9, // 印章透明度，应与CSS变量--stamp-opacity保持一致
        width: 96,     // 96x96px尺寸
        height: 96,    // 96x96px尺寸
        zIndex: 300,   // 最高层级，确保始终浮在最上层
        bottomOffset: '15%',
        rightOffset: 'calc(5% + 15px)', // 修正为左移15px
        mixBlendMode: 'multiply' // 混合模式，实现真实印章效果
    },
    
    // 导出状态管理
    state: {
        isExporting: false,
        currentProgress: 0,
        currentOperation: ''
    },
    
    // 边距设置 - 修正为用户要求的30px
    margins: {
        left: 30, // 左边距30px（用户要求）
        right: 30, // 右边距30px（用户要求）
        top: 20,
        bottom: 20
    },

    // 性能和超时配置
    timeout: 60000,             // 导出超时时间（毫秒）
    maxRetries: 3,              // 最大重试次数
    retryDelay: 2000,           // 重试延迟（毫秒）

    // 内存管理配置
    memory: {
        maxUsagePercent: 0.8,   // 最大内存使用百分比
        cleanupThreshold: 10,   // 清理阈值（导出次数）
        forceGC: false          // 是否强制垃圾回收
    }
};
// #endregion

// #region 标准调试管理器
/**
 * 标准调试管理器类
 * @class StandardDebugManager - 提供完整的调试和性能监控功能
 */
class StandardDebugManager {
    constructor() {
        this.debugMode = true;
        this.logLevels = { ERROR: 0, WARN: 1, INFO: 2, DEBUG: 3 };
        this.currentLogLevel = 3;
        this.performanceMetrics = {};
    }

    init() { return this; }

    log(level, message, data = null) {
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${level}] [导出系统]`;
        if (data) {
            console.log(`${prefix} ${message}`, data);
        } else {
            console.log(`${prefix} ${message}`);
        }
    }

    startPerformanceMetric(name) {
        this.performanceMetrics[name] = {
            startTime: performance.now(),
            endTime: null,
            duration: null
        };
    }

    endPerformanceMetric(name) {
        const metric = this.performanceMetrics[name];
        if (!metric) return 0;

        metric.endTime = performance.now();
        metric.duration = metric.endTime - metric.startTime;

        this.log('DEBUG', `性能指标: ${name}`, {
            耗时: `${metric.duration.toFixed(2)}ms`
        });

        return metric.duration;
    }

    exportLog(level, message, data = null) {
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${level}] [导出模块]`;

        if (data) {
            console.log(`${prefix} ${message}`, data);
        } else {
            console.log(`${prefix} ${message}`);
        }
    }
}

// #region 调试管理器扩展
/**
 * 导出模块调试管理器扩展
 * @description 扩展主系统的DebugManager，添加导出专用功能
 */
const ExportDebugManager = {
    /**
     * 扩展主DebugManager的功能
     * @function extend - 扩展调试管理器
     */
    extend() {
        // 检查主DebugManager是否存在，如果不存在则创建标准调试管理器
        if (typeof DebugManager === 'undefined') {
            console.warn('主DebugManager未找到，创建标准调试管理器');
            // 创建标准的调试管理器（非临时）
            window.DebugManager = new StandardDebugManager();
        }

        // 添加导出专用的调试方法
        if (!DebugManager.exportLog) {
            DebugManager.exportLog = function(level, message, data = null) {
                const timestamp = new Date().toISOString();
                const prefix = `[${timestamp}] [${level}] [导出模块]`;

                if (data) {
                    console.log(`${prefix} ${message}`, data);
                } else {
                    console.log(`${prefix} ${message}`);
                }
            };
        }

        return DebugManager;
    }
};

// 初始化扩展
ExportDebugManager.extend();
// #endregion

// #region 导出样式管理器
/**
 * 导出样式管理器类
 * @class ExportStyleManager - 正式的导出样式应用和清理管理
 */
class ExportStyleManager {
    static styleId = 'export-transform-override';

    /**
     * 应用导出样式
     * @function applyExportStyles - 应用导出模式的样式覆盖
     * @param {HTMLElement} element - 目标元素
     */
    static applyExportStyles(element) {
        try {
            // 创建或获取样式表
            let styleElement = document.getElementById(this.styleId);
            if (!styleElement) {
                styleElement = document.createElement('style');
                styleElement.id = this.styleId;
                document.head.appendChild(styleElement);
            }

            // 应用统一的导出样式
            styleElement.textContent = this.getExportStyleRules();

            // 强制重新计算样式
            if (element) {
                element.offsetHeight;
            }

            DebugManager.log('DEBUG', '导出样式应用成功');

        } catch (error) {
            DebugManager.log('ERROR', '导出样式应用失败', error);
        }
    }

    /**
     * 移除导出样式
     * @function removeExportStyles - 清理导出模式的样式
     */
    static removeExportStyles() {
        try {
            const styleElement = document.getElementById(this.styleId);
            if (styleElement && styleElement.parentNode) {
                styleElement.parentNode.removeChild(styleElement);
                DebugManager.log('DEBUG', '导出样式清理成功');
            }
        } catch (error) {
            DebugManager.log('ERROR', '导出样式清理失败', error);
        }
    }

    /**
     * 获取导出样式规则
     * @function getExportStyleRules - 生成导出样式CSS规则
     * @returns {string} CSS规则字符串
     */
    static getExportStyleRules() {
        return `
            /* 统一的导出模式变换重置 */
            html body .export-mode #document-preview,
            body .export-mode #document-preview,
            .export-mode #document-preview {
                transform: scale(1) !important;
                scale: 1 !important;
                zoom: 1 !important;
                --preview-scale-factor: 1 !important;
                width: 794px !important;
                height: 1123px !important;
                transform-origin: top center !important;
            }

            /* 媒体查询统一覆盖 */
            @media screen,
                   (max-width: 1024px),
                   (orientation: landscape) and (max-width: 1024px),
                   (max-width: 768px),
                   (max-width: 480px) {
                html body .export-mode #document-preview,
                body .export-mode #document-preview,
                .export-mode #document-preview {
                    transform: scale(1) !important;
                    scale: 1 !important;
                    zoom: 1 !important;
                    --preview-scale-factor: 1 !important;
                    width: 794px !important;
                    height: 1123px !important;
                    transform-origin: top center !important;
                }
            }
        `;
    }
}

// #region 表单数据服务
/**
 * 统一的表单数据收集服务
 * @class FormDataService - 提供统一的表单数据收集、验证和处理功能
 */
class FormDataService {
    /**
     * 收集表单数据
     * @function collect - 收集所有表单输入数据
     * @returns {Object} 表单数据对象
     */
    static collect() {
        try {
            // 使用全局的collectFormData函数
            if (typeof window.collectFormData === 'function') {
                return window.collectFormData();
            }

            // 备用数据收集逻辑
            return this.fallbackCollect();

        } catch (error) {
            DebugManager.log('ERROR', 'FormDataService: 数据收集失败', error);
            return this.fallbackCollect();
        }
    }

    /**
     * 备用数据收集方法
     * @function fallbackCollect - 备用的数据收集逻辑
     * @returns {Object} 基础表单数据
     */
    static fallbackCollect() {
        const data = {};

        try {
            // 收集基础字段
            const basicFields = [
                'customer-name', 'customer-phone', 'customer-address',
                'document-number', 'document-type', 'payment-method',
                'notes', 'company-name', 'tax-id', 'company-address'
            ];

            basicFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    data[this.camelCase(fieldId)] = element.value || '';
                }
            });

            // 收集项目数据
            data.items = this.collectItems();

            // 计算总金额
            data.total = this.calculateTotal(data.items);

            DebugManager.log('DEBUG', 'FormDataService: 备用数据收集完成', {
                字段数量: Object.keys(data).length,
                项目数量: data.items?.length || 0
            });

        } catch (error) {
            DebugManager.log('ERROR', 'FormDataService: 备用数据收集失败', error);
        }

        return data;
    }

    /**
     * 收集项目数据
     * @function collectItems - 收集所有项目明细
     * @returns {Array} 项目数组
     */
    static collectItems() {
        const items = [];
        const itemRows = document.querySelectorAll('.item-row');

        itemRows.forEach((row, index) => {
            const descInput = row.querySelector('.item-description');
            const qtyInput = row.querySelector('.item-quantity');
            const priceInput = row.querySelector('.item-price');

            if (descInput || qtyInput || priceInput) {
                items.push({
                    description: descInput?.value || '',
                    quantity: parseFloat(qtyInput?.value) || 0,
                    price: parseFloat(priceInput?.value) || 0
                });
            }
        });

        return items;
    }

    /**
     * 计算总金额
     * @function calculateTotal - 计算项目总金额
     * @param {Array} items - 项目数组
     * @returns {number} 总金额
     */
    static calculateTotal(items) {
        if (!Array.isArray(items)) return 0;

        return items.reduce((total, item) => {
            const quantity = parseFloat(item.quantity) || 0;
            const price = parseFloat(item.price) || 0;
            return total + (quantity * price);
        }, 0);
    }

    /**
     * 验证表单数据
     * @function validate - 验证表单数据的完整性和有效性
     * @param {Object} data - 表单数据
     * @returns {Object} 验证结果
     */
    static validate(data) {
        const validation = {
            valid: true,
            errors: [],
            warnings: []
        };

        try {
            // 必填字段检查
            const requiredFields = ['customerName', 'documentNumber'];
            requiredFields.forEach(field => {
                if (!data[field] || data[field].trim() === '') {
                    validation.errors.push(`${field}为必填字段`);
                    validation.valid = false;
                }
            });

            // 项目数据检查
            if (!data.items || data.items.length === 0) {
                validation.warnings.push('未添加任何项目明细');
            } else {
                data.items.forEach((item, index) => {
                    if (!item.description || item.description.trim() === '') {
                        validation.warnings.push(`第${index + 1}项缺少描述`);
                    }
                    if (item.quantity <= 0) {
                        validation.warnings.push(`第${index + 1}项数量无效`);
                    }
                    if (item.price < 0) {
                        validation.warnings.push(`第${index + 1}项价格无效`);
                    }
                });
            }

            // 总金额检查
            if (data.total <= 0) {
                validation.warnings.push('总金额为零或负数');
            }

        } catch (error) {
            validation.valid = false;
            validation.errors.push(`验证过程出错: ${error.message}`);
        }

        return validation;
    }

    /**
     * 转换为驼峰命名
     * @function camelCase - 将连字符命名转换为驼峰命名
     * @param {string} str - 原始字符串
     * @returns {string} 驼峰命名字符串
     */
    static camelCase(str) {
        return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
    }

    /**
     * 格式化数据用于导出
     * @function formatForExport - 格式化数据用于导出
     * @param {Object} data - 原始数据
     * @returns {Object} 格式化后的数据
     */
    static formatForExport(data) {
        const formatted = { ...data };

        try {
            // 格式化金额
            if (typeof formatted.total === 'number') {
                formatted.totalFormatted = formatted.total.toFixed(2);
            }

            // 格式化项目
            if (Array.isArray(formatted.items)) {
                formatted.items = formatted.items.map(item => ({
                    ...item,
                    quantity: parseFloat(item.quantity) || 0,
                    price: parseFloat(item.price) || 0,
                    subtotal: (parseFloat(item.quantity) || 0) * (parseFloat(item.price) || 0)
                }));
            }

            // 添加时间戳
            formatted.exportTimestamp = new Date().toISOString();

        } catch (error) {
            DebugManager.log('ERROR', 'FormDataService: 数据格式化失败', error);
        }

        return formatted;
    }
}

// #region 工具函数模块
/**
 * 生成导出文件名
 * @function generateExportFilename - 统一的文件名生成逻辑
 * @param {string} format - 文件格式 ('pdf', 'png', 'jpeg')
 * @param {object} data - 表单数据对象（可选）
 * @returns {string} 格式化的文件名
 */
function generateExportFilename(format, data = null) {
    try {
        // 如果没有传入数据，使用FormDataService收集
        if (!data) {
            data = FormDataService.collect();
        }
        
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        const docTypeName = data?.documentType === 'invoice' ? '发票_Invoice' : '收据_Receipt';
        const docNumber = data?.documentNumber || timestamp;
        
        // 支持多订单模式的文件名
        let filename = `${docTypeName}_${docNumber}_300DPI.${format}`;
        
        // 如果是多订单模式，添加订单数量标识
        if (data?.multiOrderMode && data?.multiOrderData?.length > 1) {
            filename = `${docTypeName}_${data.multiOrderData.length}Orders_${docNumber}_300DPI.${format}`;
        }
        
        return filename;
    } catch (error) {
        DebugManager.log('ERROR', '文件名生成失败', error);
        return `Export_${Date.now()}.${format}`;
    }
}

/**
 * 提取并嵌入CSS样式
 * @function extractAndEmbedCSS - 提取页面CSS样式用于导出
 * @returns {string} 嵌入式CSS字符串
 */
function extractAndEmbedCSS() {
    try {
        DebugManager.startPerformance('CSS提取');
        
        // 获取页面中的所有样式表
        const styleSheets = Array.from(document.styleSheets);
        let cssText = '';

        // 提取内联样式
        const styleElements = document.querySelectorAll('style');
        styleElements.forEach(style => {
            if (style.textContent) {
                cssText += style.textContent + '\n';
            }
        });

        // 提取外部样式表（如果可访问）
        styleSheets.forEach(sheet => {
            try {
                if (sheet.cssRules) {
                    Array.from(sheet.cssRules).forEach(rule => {
                        cssText += rule.cssText + '\n';
                    });
                }
            } catch (e) {
                // 跨域样式表无法访问，跳过
                DebugManager.log('WARN', '无法访问样式表', { href: sheet.href });
            }
        });

        // 从主文档获取CSS变量值，确保完全同步 - 修正默认值
        const rootStyles = getComputedStyle(document.documentElement);
        const headerHeight = rootStyles.getPropertyValue('--header-height').trim() || '130px'; // 修正默认值为130px
        const footerHeight = rootStyles.getPropertyValue('--footer-height').trim() || '110px';
        const stampRightOffset = rootStyles.getPropertyValue('--stamp-right-offset').trim() || 'calc(5% + 15px)'; // 修正默认值
        const stampBottomOffset = rootStyles.getPropertyValue('--stamp-bottom-offset').trim() || '15%';
        const contentPadding = rootStyles.getPropertyValue('--content-padding').trim() || '20px';
        const a4WidthPx = rootStyles.getPropertyValue('--a4-width-px').trim() || '794px';
        const a4HeightPx = rootStyles.getPropertyValue('--a4-height-px').trim() || '1123px';

        DebugManager.log('DEBUG', 'CSS变量同步检查', {
            页眉高度: headerHeight,
            页脚高度: footerHeight,
            盖章右偏移: stampRightOffset,
            盖章底偏移: stampBottomOffset,
            内容边距: contentPadding,
            A4宽度: a4WidthPx,
            A4高度: a4HeightPx
        });

        // 添加导出专用样式
        cssText += `
            /* 导出模式专用样式 - 使用同步的CSS变量值 */
            .export-mode {
                transform: none !important;
                scale: none !important;
                zoom: 1 !important;
                margin: 0 !important;
                padding: 0 !important;
                box-shadow: none !important;
                border: none !important;
                width: ${a4WidthPx.replace('px', '')}px !important;
                min-height: ${a4HeightPx.replace('px', '')}px !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, "Microsoft YaHei", "PingFang SC", sans-serif !important;
                line-height: 1.6 !important;
                color: #333 !important;
                background: white !important;
            }

            /* 导出模式主容器样式 - 确保内容不与页脚重叠，关键：设置相对定位 */
            .export-mode #document-container {
                padding-top: 20px !important;
                padding-bottom: calc(${footerHeight.replace('px', '')}px + 15px) !important;
                padding-left: 30px !important;
                padding-right: 30px !important;
                box-sizing: border-box !important;
                /* 关键修复：强制设置相对定位，确保页脚能够正确相对于容器定位 */
                position: relative !important;
                min-height: ${a4HeightPx.replace('px', '')}px !important;
                width: 100% !important;
                background: white !important;
                /* 确保容器有明确的高度边界 */
                max-height: ${a4HeightPx.replace('px', '')}px !important;
                overflow: visible !important;
            }

            /* 完全隐藏占位符 */
            .export-mode .image-placeholder,
            .export-mode .header-placeholder,
            .export-mode .footer-placeholder,
            .export-mode .stamp-placeholder {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                width: 0 !important;
                height: 0 !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            
            /* 印章透明度和定位设置 - 使用同步的CSS变量值，确保最高层级，完全透明背景 */
            .export-mode .company-stamp {
                position: absolute !important;
                bottom: ${stampBottomOffset} !important;
                right: ${stampRightOffset} !important;
                z-index: ${ExportConfig.stamp.zIndex} !important; /* 使用配置的最高层级 */
                width: ${ExportConfig.stamp.width}px !important;
                height: ${ExportConfig.stamp.height}px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                overflow: visible !important;
                box-sizing: border-box !important;
                /* 强制透明背景，避免遮挡底层内容 */
                background: transparent !important;
                background-color: transparent !important;
                border: none !important;
                box-shadow: none !important;
            }

            /* 印章图片样式 - 确保完整显示不被裁切，真正的透明叠加 */
            .export-mode .company-stamp img {
                width: 100% !important;
                height: 100% !important;
                object-fit: contain !important;
                object-position: center !important;
                display: block !important;
                margin: 0 !important;
                padding: 0 !important;
                border: none !important;
                background: transparent !important;
                background-color: transparent !important;
                box-shadow: none !important;
                opacity: ${ExportConfig.stamp.opacity} !important; /* 使用配置的透明度 */
                /* 使用混合模式实现真正的透明叠加，添加兼容性处理 */
                mix-blend-mode: ${ExportConfig.stamp.mixBlendMode} !important;
                isolation: auto !important;
                /* 备用方案：如果混合模式不支持，使用更低的透明度 */
                filter: opacity(${ExportConfig.stamp.opacity}) !important;
                image-rendering: high-quality !important;
                image-rendering: -webkit-optimize-contrast !important;
            }

            /* 页脚强制定位修正 - 使用同步的CSS变量值，确保固定在A4页面底部边缘 */
            .export-mode .unified-document-footer,
            .export-mode .company-footer-image-container {
                position: absolute !important;
                bottom: 0 !important;
                left: 0 !important;
                right: 0 !important;
                height: ${footerHeight.replace('px', '')}px !important;
                z-index: 100 !important;
                width: 100% !important;
                background-color: white !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                padding: 5px !important;
                box-sizing: border-box !important;
                margin: 0 !important;
                /* 确保页脚不受其他样式影响 */
                transform: none !important;
                scale: none !important;
                zoom: 1 !important;
            }

            /* 页眉强制定位修正 - 使用同步的CSS变量值 */
            .export-mode .document-header,
            .export-mode .document-header-image-container {
                position: relative !important;
                width: 100% !important;
                height: ${headerHeight.replace('px', '')}px !important;
                margin-bottom: 15px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                overflow: hidden !important;
            }

            /* 页眉图片防变形处理 - 确保保持原始比例 */
            .export-mode .document-header-image-container,
            .export-mode .document-header {
                position: relative !important;
                width: 100% !important;
                height: 130px !important;
                margin-bottom: 15px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                overflow: hidden !important;
                background: white !important;
                box-sizing: border-box !important;
            }

            /* 导出模式下图片元素position重置 - 避免通用样式影响 */
            .export-mode img {
                position: static !important; /* 重置图片定位，避免通用样式干扰 */
            }

            .export-mode .document-header-image-container img,
            .export-mode .document-header img {
                height: 100% !important;
                width: auto !important;
                max-width: 100% !important;
                object-fit: contain !important;
                object-position: center !important;
                margin: 0 auto !important;
                display: block !important;
                image-rendering: high-quality !important;
                image-rendering: -webkit-optimize-contrast !important;
                border: none !important;
                background: transparent !important;
                padding: 0 !important;
                box-sizing: border-box !important;
                /* 强制防止拉伸变形 */
                min-width: 0 !important;
                min-height: 0 !important;
                flex-shrink: 0 !important;
            }

            /* 页脚图片样式 - 确保保持原始比例 */
            .export-mode .unified-document-footer img,
            .export-mode .company-footer-image-container img {
                height: 100% !important;
                width: auto !important;
                max-width: 100% !important;
                object-fit: contain !important;
                object-position: center !important;
                margin: 0 auto !important;
                display: block !important;
                image-rendering: high-quality !important;
                image-rendering: -webkit-optimize-contrast !important;
                border: none !important;
                background: transparent !important;
                padding: 0 !important;
                box-sizing: border-box !important;
            }
            
            /* 左右边距设置 - 扩展到所有主要内容区域 */
            .export-mode .items-table {
                margin-left: ${ExportConfig.margins.left}px !important;
                margin-right: ${ExportConfig.margins.right}px !important;
                width: calc(100% - ${ExportConfig.margins.left + ExportConfig.margins.right}px) !important;
            }

            .export-mode .notes-section,
            .export-mode .company-info,
            .export-mode .customer-info,
            .export-mode .payment-info,
            .export-mode .document-title,
            .export-mode .total-amount-container {
                padding-left: ${ExportConfig.margins.left}px !important;
                padding-right: ${ExportConfig.margins.right}px !important;
                box-sizing: border-box !important;
            }

            /* 特殊处理：备注区域需要额外边距 */
            .export-mode .notes-section {
                margin-left: ${ExportConfig.margins.left}px !important;
                margin-right: ${ExportConfig.margins.right}px !important;
                width: calc(100% - ${ExportConfig.margins.left + ExportConfig.margins.right}px) !important;
                padding-left: ${ExportConfig.margins.left}px !important;
                padding-right: ${ExportConfig.margins.right}px !important;
            }

            /* 特殊处理：确保表格和备注区域有足够的边距 */
            .export-mode div[style*="margin: 12px 0"],
            .export-mode div[style*="padding: 0 10px"] {
                padding-left: ${ExportConfig.margins.left + 10}px !important;
                padding-right: ${ExportConfig.margins.right + 10}px !important;
            }
            
            /* 文字排版优化 */
            .export-mode * {
                word-wrap: break-word !important;
                overflow-wrap: break-word !important;
                text-rendering: optimizeLegibility !important;
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
                box-sizing: border-box !important;
            }

            /* 表格排版优化 */
            .export-mode .items-table {
                width: 100% !important;
                border-collapse: collapse !important;
                border-spacing: 0 !important;
                table-layout: fixed !important;
                margin: 10px 0 !important;
                font-size: 12px !important;
                line-height: 1.4 !important;
            }

            .export-mode .items-table th,
            .export-mode .items-table td {
                border: 1px solid #333 !important;
                padding: 8px !important;
                text-align: left !important;
                vertical-align: top !important;
                word-wrap: break-word !important;
                overflow-wrap: break-word !important;
            }

            .export-mode .items-table th {
                background-color: #f5f5f5 !important;
                font-weight: bold !important;
                text-align: center !important;
            }

            /* 数量和价格列右对齐 */
            .export-mode .items-table td:nth-child(2),
            .export-mode .items-table td:nth-child(3),
            .export-mode .items-table td:nth-child(4) {
                text-align: right !important;
            }

            /* 总金额容器优化 - 统一所有公司样式，确保层级关系正确 */
            .export-mode .total-amount-container {
                display: inline-block !important;
                padding: 12px 18px !important;
                border: 2px solid #1e40af !important;
                border-radius: 6px !important;
                background-color: #ffffff !important;
                box-shadow: none !important;
                min-width: 200px !important;
                max-width: 100% !important;
                z-index: var(--z-index-total) !important; /* 使用CSS变量，确保层级一致性 */
                position: relative !important;
                margin: 15px 0 !important;
                text-align: center !important;
            }

            .export-mode .total-amount-container h3 {
                margin: 0 !important;
                color: #1e40af !important;
                font-size: 16px !important;
                font-weight: bold !important;
                line-height: 1.4 !important;
                text-shadow: none !important;
                white-space: nowrap !important;
            }

            /* 公司样式统一处理 - 确保GoMyHire和Sky Mirror样式一致 */
            .export-mode .company-info,
            .export-mode .company-details,
            .export-mode .company-name,
            .export-mode .company-address {
                font-family: inherit !important;
                color: #333 !important;
                line-height: 1.6 !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            /* 统一公司标题样式 */
            .export-mode .company-name,
            .export-mode h1,
            .export-mode h2 {
                font-size: 18px !important;
                font-weight: bold !important;
                color: #1e40af !important;
                text-align: center !important;
                margin-bottom: 10px !important;
            }

            /* 统一公司地址和联系信息样式 */
            .export-mode .company-address,
            .export-mode .company-contact {
                font-size: 14px !important;
                color: #666 !important;
                text-align: center !important;
                margin-bottom: 5px !important;
            }
        `;

        DebugManager.endPerformance('CSS提取');
        DebugManager.log('DEBUG', 'CSS样式提取完成', {
            样式长度: cssText.length,
            样式表数量: styleSheets.length,
            内联样式数量: styleElements.length
        });

        return cssText;
    } catch (error) {
        DebugManager.log('ERROR', 'CSS样式提取失败', error);
        return '';
    }
}

/**
 * 优化字体渲染
 * @function optimizeFontRendering - 为导出优化字体渲染质量
 * @param {HTMLElement} container - 容器元素
 */
function optimizeFontRendering(container) {
    try {
        DebugManager.startPerformance('字体优化');

        // 为所有文本元素应用高质量字体渲染
        const textElements = container.querySelectorAll('*');

        textElements.forEach(element => {
            // 应用字体渲染优化
            element.style.fontSmooth = 'always';
            element.style.webkitFontSmoothing = 'antialiased';
            element.style.mozOsxFontSmoothing = 'grayscale';
            element.style.textRendering = 'optimizeLegibility';

            // 确保中英文字体fallback
            if (element.style.fontFamily) {
                element.style.fontFamily = `${element.style.fontFamily}, "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "WenQuanYi Micro Hei", sans-serif`;
            } else {
                element.style.fontFamily = '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "WenQuanYi Micro Hei", sans-serif';
            }

            // 应用硬件加速
            element.style.webkitTransform = 'translateZ(0)';
            element.style.transform = 'translateZ(0)';
        });

        DebugManager.endPerformance('字体优化');
        DebugManager.log('DEBUG', `字体渲染优化完成，处理了${textElements.length}个元素`);

    } catch (error) {
        DebugManager.log('ERROR', '字体渲染优化失败', error);
    }
}

/**
 * 预处理图片
 * @function preprocessImages - 预处理页眉页脚图片，确保高质量
 * @param {HTMLElement} container - 容器元素
 */
async function preprocessImages(container) {
    try {
        DebugManager.startPerformance('图片预处理');

        const images = container.querySelectorAll('img');
        const loadPromises = [];

        images.forEach(img => {
            // 应用高质量样式
            img.style.imageRendering = 'high-quality';
            img.style.imageRendering = '-webkit-optimize-contrast';
            img.style.backfaceVisibility = 'hidden';
            img.style.transform = 'translateZ(0)';

            // 确保页眉图片完全填满130px区域
            if (img.closest('.document-header-image-container') || img.closest('.document-header')) {
                // 强制应用填充样式
                img.style.height = '100%';
                img.style.width = '100%'; // 修改为100%配合cover填充
                img.style.objectFit = 'cover'; // 完全填满130px区域，保持宽高比
                img.style.objectPosition = 'center';
                img.style.margin = '0';
                img.style.display = 'block';
                img.style.border = 'none';
                img.style.background = 'transparent';
                img.style.padding = '0';
                img.style.boxSizing = 'border-box';

                // 确保容器也正确设置
                const headerContainer = img.closest('.document-header-image-container') || img.closest('.document-header');
                if (headerContainer) {
                    headerContainer.style.display = 'flex';
                    headerContainer.style.alignItems = 'center';
                    headerContainer.style.justifyContent = 'center';
                    headerContainer.style.overflow = 'hidden';
                    headerContainer.style.boxSizing = 'border-box';
                }

                DebugManager.log('DEBUG', '页眉图片防变形处理完成', {
                    图片尺寸: `${img.naturalWidth}x${img.naturalHeight}`,
                    容器高度: headerContainer ? headerContainer.style.height : 'unknown'
                });
            }

            // 页脚图片处理 - 完全填满110px区域
            if (img.closest('.unified-document-footer') || img.closest('.company-footer-image-container')) {
                img.style.height = '100%';
                img.style.width = '100%'; // 修改为100%配合cover填充
                img.style.objectFit = 'cover'; // 完全填满110px区域，保持宽高比
                img.style.objectPosition = 'center';
                img.style.margin = '0';
                img.style.display = 'block';
                img.style.border = 'none';
                img.style.background = 'transparent';
                img.style.padding = '0';
                img.style.boxSizing = 'border-box';

                // 确保页脚容器也正确设置
                const footerContainer = img.closest('.unified-document-footer') || img.closest('.company-footer-image-container');
                if (footerContainer) {
                    footerContainer.style.display = 'flex';
                    footerContainer.style.alignItems = 'center';
                    footerContainer.style.justifyContent = 'center';
                    footerContainer.style.overflow = 'hidden';
                    footerContainer.style.boxSizing = 'border-box';
                }

                DebugManager.log('DEBUG', '页脚图片防变形处理完成', {
                    图片尺寸: `${img.naturalWidth}x${img.naturalHeight}`,
                    容器高度: footerContainer ? footerContainer.style.height : 'unknown'
                });
            }

            // 印章图片处理 - 确保完整显示不被裁切，实现真正的透明叠加
            if (img.closest('.company-stamp')) {
                // 确保印章图片完整显示，自动适应容器大小
                img.style.width = '100%';
                img.style.height = '100%';
                img.style.objectFit = 'contain'; // 保持原始比例，完整显示
                img.style.objectPosition = 'center';
                img.style.display = 'block';
                img.style.margin = '0';
                img.style.padding = '0';
                img.style.border = 'none';
                img.style.background = 'transparent';
                img.style.backgroundColor = 'transparent';
                img.style.boxShadow = 'none';
                img.style.opacity = ExportConfig.stamp.opacity; // 使用配置的透明度
                img.style.mixBlendMode = ExportConfig.stamp.mixBlendMode; // 使用混合模式
                img.style.isolation = 'auto';

                // 确保印章容器也正确设置
                const stampContainer = img.closest('.company-stamp');
                if (stampContainer) {
                    stampContainer.style.display = 'flex';
                    stampContainer.style.alignItems = 'center';
                    stampContainer.style.justifyContent = 'center';
                    stampContainer.style.overflow = 'visible'; // 允许内容可见，防止裁切
                    stampContainer.style.boxSizing = 'border-box';
                    stampContainer.style.background = 'transparent';
                    stampContainer.style.backgroundColor = 'transparent';
                    stampContainer.style.border = 'none';
                    stampContainer.style.boxShadow = 'none';
                }

                DebugManager.log('DEBUG', '印章图片防裁切处理完成', {
                    图片尺寸: `${img.naturalWidth}x${img.naturalHeight}`,
                    容器尺寸: `${ExportConfig.stamp.width}x${ExportConfig.stamp.height}px`,
                    透明度: ExportConfig.stamp.opacity
                });
            }

            // 确保图片加载完成
            if (!img.complete) {
                loadPromises.push(new Promise((resolve, reject) => {
                    img.onload = resolve;
                    img.onerror = reject;
                    // 设置超时
                    setTimeout(() => reject(new Error('图片加载超时')), 5000);
                }));
            }
        });

        // 等待所有图片加载完成
        if (loadPromises.length > 0) {
            try {
                await Promise.all(loadPromises);
                DebugManager.log('DEBUG', '所有图片预处理完成');
            } catch (error) {
                DebugManager.log('WARN', '部分图片加载失败，继续导出', error);
            }
        }

        DebugManager.endPerformance('图片预处理');
        DebugManager.log('DEBUG', `图片预处理完成，共处理${images.length}张图片`);
    } catch (error) {
        DebugManager.log('ERROR', '图片预处理失败', error);
    }
}

/**
 * 重新渲染内容以确保导出模式下的一致性
 * @function reRenderForExport - 在导出模式下重新渲染内容
 * @param {HTMLElement} container - 容器元素
 * @returns {Promise<void>} 重新渲染完成的Promise
 */
async function reRenderForExport(container) {
    try {
        DebugManager.log('DEBUG', '开始重新渲染内容以确保导出一致性');

        // 收集当前表单数据
        const data = FormDataService.collect();

        // 修复flex gap问题 - html2canvas不完全支持gap属性
        const FLEX_GAP_QUERY = '[style*="display: flex"][style*="gap"], .export-mode .flex-gap-fix';
        container.querySelectorAll(FLEX_GAP_QUERY).forEach(flexEl => {
            const computed = window.getComputedStyle(flexEl);
            const gapValue = computed.columnGap || computed.gap || flexEl.style.columnGap || flexEl.style.gap;
            if (!gapValue) return;

            // 去掉自身 gap，防止被 html2canvas 忽略后产生零间距
            flexEl.style.columnGap = '0px';
            flexEl.style.gap = '0px';

            // 仅处理横向主轴，纵向 flex-wrap 的情况极少且不影响主要排版
            const children = Array.from(flexEl.children);
            children.forEach((child, index) => {
                if (index !== children.length - 1) {
                    // 为除最后一个元素外添加右侧margin
                    child.style.marginRight = gapValue;
                }
            });
        });

        DebugManager.log('DEBUG', '导出模式下内容重新渲染完成', {
            文档类型: data.documentType,
            导出模式: true,
            内容长度: container.innerHTML.length
        });

        // 强制修正布局问题
        await fixLayoutIssues(container);

        // 验证DOM结构完整性
        await validateDOMStructure(container);

        // 等待DOM更新完成
        await new Promise(resolve => setTimeout(resolve, 200));

    } catch (error) {
        DebugManager.log('ERROR', '内容重新渲染失败', error);
        throw error;
    }
}

/**
 * 验证页脚定位是否正确
 * @function validateFooterPositioning - 验证页脚是否正确固定在A4页面底部
 * @param {HTMLElement} container - 容器元素
 * @param {NodeList} footers - 页脚元素列表
 * @returns {Promise<void>} 验证完成的Promise
 */
async function validateFooterPositioning(container, footers) {
    try {
        DebugManager.log('DEBUG', '开始验证页脚定位');

        const containerRect = container.getBoundingClientRect();
        const expectedFooterHeight = 110; // 固定110px

        footers.forEach((footer, index) => {
            const footerRect = footer.getBoundingClientRect();
            const computedStyle = window.getComputedStyle(footer);

            const positionInfo = {
                索引: index,
                position: computedStyle.position,
                bottom: computedStyle.bottom,
                height: computedStyle.height,
                实际高度: footerRect.height,
                距离容器底部: containerRect.bottom - footerRect.bottom,
                是否在底部: Math.abs(containerRect.bottom - footerRect.bottom) < 5, // 允许5px误差
                z_index: computedStyle.zIndex
            };

            DebugManager.log('DEBUG', `页脚${index + 1}定位信息`, positionInfo);

            // 如果页脚没有正确定位，强制修正
            if (!positionInfo.是否在底部 || computedStyle.position !== 'absolute') {
                DebugManager.log('WARN', `页脚${index + 1}定位异常，强制修正`);

                footer.style.setProperty('position', 'absolute', 'important');
                footer.style.setProperty('bottom', '0px', 'important');
                footer.style.setProperty('left', '0px', 'important');
                footer.style.setProperty('right', '0px', 'important');
                footer.style.setProperty('height', `${expectedFooterHeight}px`, 'important');
                footer.style.setProperty('z-index', '100', 'important');
                footer.style.setProperty('width', '100%', 'important');
            }
        });

        DebugManager.log('DEBUG', '页脚定位验证完成');
    } catch (error) {
        DebugManager.log('ERROR', '页脚定位验证失败', error);
    }
}

/**
 * 修正导出模式下的布局问题
 * @function fixLayoutIssues - 修正页脚和印章的定位问题
 * @param {HTMLElement} container - 容器元素
 * @returns {Promise<void>} 修正完成的Promise
 */
async function fixLayoutIssues(container) {
    try {
        DebugManager.log('DEBUG', '开始修正布局问题');

        // 从主文档获取最新的CSS变量值，确保同步 - 修正默认值
        const rootStyles = getComputedStyle(document.documentElement);
        const headerHeight = rootStyles.getPropertyValue('--header-height').trim() || '130px'; // 修正默认值为130px
        const footerHeight = rootStyles.getPropertyValue('--footer-height').trim() || '110px';
        const stampRightOffset = rootStyles.getPropertyValue('--stamp-right-offset').trim() || 'calc(5% + 15px)'; // 修正默认值
        const stampBottomOffset = rootStyles.getPropertyValue('--stamp-bottom-offset').trim() || '15%';

        // 修正页脚定位 - 使用同步的CSS变量值，确保固定在A4页面底部边缘
        const footers = container.querySelectorAll('.unified-document-footer, .company-footer-image-container');
        footers.forEach(footer => {
            footer.style.position = 'absolute';
            footer.style.bottom = '0px';
            footer.style.left = '0px';
            footer.style.right = '0px';
            footer.style.height = footerHeight.replace('px', '') + 'px';
            footer.style.zIndex = '100';
            footer.style.width = '100%';
            footer.style.backgroundColor = 'white';
            footer.style.display = 'flex';
            footer.style.alignItems = 'center';
            footer.style.justifyContent = 'center';
            footer.style.padding = '5px';
            footer.style.boxSizing = 'border-box';
            footer.style.margin = '0';
            // 确保页脚不受其他样式影响
            footer.style.transform = 'none';
            footer.style.scale = 'none';
            footer.style.zoom = '1';
        });

        // 修正印章定位 - 使用同步的CSS变量值，确保最高层级，完全透明背景
        const stamps = container.querySelectorAll('.company-stamp');
        stamps.forEach(stamp => {
            stamp.style.position = 'absolute';
            stamp.style.bottom = stampBottomOffset;
            stamp.style.right = stampRightOffset;
            stamp.style.zIndex = ExportConfig.stamp.zIndex.toString(); // 使用配置的最高层级
            stamp.style.width = `${ExportConfig.stamp.width}px`;
            stamp.style.height = `${ExportConfig.stamp.height}px`;
            // 确保印章容器能够正确显示图片
            stamp.style.display = 'flex';
            stamp.style.alignItems = 'center';
            stamp.style.justifyContent = 'center';
            stamp.style.overflow = 'visible';
            stamp.style.boxSizing = 'border-box';
            // 强制透明背景，避免遮挡底层内容
            stamp.style.background = 'transparent';
            stamp.style.backgroundColor = 'transparent';
            stamp.style.border = 'none';
            stamp.style.boxShadow = 'none';

            // 处理印章内的图片 - 实现真正的透明叠加
            const stampImg = stamp.querySelector('img');
            if (stampImg) {
                stampImg.style.width = '100%';
                stampImg.style.height = '100%';
                stampImg.style.objectFit = 'contain';
                stampImg.style.objectPosition = 'center';
                stampImg.style.display = 'block';
                stampImg.style.margin = '0';
                stampImg.style.padding = '0';
                stampImg.style.border = 'none';
                stampImg.style.background = 'transparent';
                stampImg.style.backgroundColor = 'transparent';
                stampImg.style.boxShadow = 'none';
                stampImg.style.opacity = ExportConfig.stamp.opacity; // 使用配置的透明度
                stampImg.style.mixBlendMode = ExportConfig.stamp.mixBlendMode; // 使用混合模式
                stampImg.style.isolation = 'auto';
            }
        });

        // 修正页眉定位 - 使用同步的CSS变量值
        const headers = container.querySelectorAll('.document-header, .document-header-image-container');
        headers.forEach(header => {
            header.style.position = 'relative';
            header.style.width = '100%';
            header.style.height = headerHeight.replace('px', '') + 'px';
            header.style.marginBottom = '15px';
        });

        // 修正表格和内容区域的边距 - 扩展到所有主要内容区域
        const tables = container.querySelectorAll('.items-table');
        tables.forEach(table => {
            table.style.marginLeft = `${ExportConfig.margins.left}px`;
            table.style.marginRight = `${ExportConfig.margins.right}px`;
            table.style.width = `calc(100% - ${ExportConfig.margins.left + ExportConfig.margins.right}px)`;
        });

        // 为所有主要内容区域添加边距
        const contentAreas = container.querySelectorAll('.notes-section, .company-info, .customer-info, .payment-info, .document-title');
        contentAreas.forEach(area => {
            area.style.paddingLeft = `${ExportConfig.margins.left}px`;
            area.style.paddingRight = `${ExportConfig.margins.right}px`;
            area.style.boxSizing = 'border-box';
        });

        // 特殊处理总金额容器 - 确保层级关系正确
        const totalContainers = container.querySelectorAll('.total-amount-container');
        totalContainers.forEach(total => {
            total.style.paddingLeft = `${ExportConfig.margins.left}px`;
            total.style.paddingRight = `${ExportConfig.margins.right}px`;
            total.style.boxSizing = 'border-box';
            total.style.zIndex = '200'; // 确保总金额在印章下层
            total.style.position = 'relative';
        });

        // 特殊处理备注区域
        const notesSection = container.querySelector('.notes-section');
        if (notesSection) {
            notesSection.style.marginLeft = `${ExportConfig.margins.left}px`;
            notesSection.style.marginRight = `${ExportConfig.margins.right}px`;
            notesSection.style.width = `calc(100% - ${ExportConfig.margins.left + ExportConfig.margins.right}px)`;
            notesSection.style.paddingLeft = `${ExportConfig.margins.left}px`;
            notesSection.style.paddingRight = `${ExportConfig.margins.right}px`;
        }

        // 确保容器尺寸正确
        const documentContainer = container.querySelector('#document-container') || container;
        if (documentContainer) {
            documentContainer.style.width = `${ExportConfig.a4.widthPx}px`;
            documentContainer.style.minHeight = `${ExportConfig.a4.heightPx}px`;
            documentContainer.style.position = 'relative';
        }

        // 验证页脚定位是否正确
        await validateFooterPositioning(container, footers);

        DebugManager.log('DEBUG', '布局问题修正完成', {
            页脚数量: footers.length,
            印章数量: stamps.length,
            页眉数量: headers.length
        });

    } catch (error) {
        DebugManager.log('ERROR', '布局修正失败', error);
    }
}

/**
 * 验证DOM结构完整性
 * @function validateDOMStructure - 验证导出前的DOM结构
 * @param {HTMLElement} container - 容器元素
 * @returns {Promise<void>} 验证完成的Promise
 */
async function validateDOMStructure(container) {
    try {
        DebugManager.log('DEBUG', '开始DOM结构验证');

        const validation = {
            容器尺寸: {
                宽度: container.offsetWidth,
                高度: container.offsetHeight,
                预期宽度: ExportConfig.a4.widthPx,
                预期高度: ExportConfig.a4.heightPx
            },
            关键元素: {
                页眉: !!container.querySelector('.document-header, .document-header-image-container'),
                页脚: !!container.querySelector('.unified-document-footer, .company-footer-image-container'),
                印章: !!container.querySelector('.company-stamp'),
                总金额: !!container.querySelector('.total-amount-container')
            },
            布局检查: {
                页脚位置: null,
                印章位置: null,
                总金额位置: null,
                层级关系: null,
                内容溢出: false
            }
        };

        // 检查页脚位置 - 使用同步的CSS变量值
        const footer = container.querySelector('.unified-document-footer, .company-footer-image-container');
        if (footer) {
            const rootStyles = getComputedStyle(document.documentElement);
            const footerHeight = rootStyles.getPropertyValue('--footer-height').trim() || '110px';
            const expectedFooterHeight = parseInt(footerHeight.replace('px', ''));

            const footerRect = footer.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();
            validation.布局检查.页脚位置 = {
                距离底部: containerRect.bottom - footerRect.bottom,
                高度: footerRect.height,
                预期高度: expectedFooterHeight,
                CSS变量值: footerHeight
            };
        }

        // 检查印章位置
        const stamp = container.querySelector('.company-stamp');
        if (stamp) {
            const stampRect = stamp.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();
            const stampStyle = window.getComputedStyle(stamp);
            validation.布局检查.印章位置 = {
                距离右边: containerRect.right - stampRect.right,
                距离底部: containerRect.bottom - stampRect.bottom,
                透明度: stampStyle.opacity,
                层级: stampStyle.zIndex
            };
        }

        // 检查总金额容器位置
        const totalContainer = container.querySelector('.total-amount-container');
        if (totalContainer) {
            const totalStyle = window.getComputedStyle(totalContainer);
            validation.布局检查.总金额位置 = {
                层级: totalStyle.zIndex,
                位置: totalStyle.position,
                背景色: totalStyle.backgroundColor
            };
        }

        // 检查层级关系 - 确保印章在总金额上层
        if (stamp && totalContainer) {
            const stampZIndex = parseInt(window.getComputedStyle(stamp).zIndex) || 0;
            const totalZIndex = parseInt(window.getComputedStyle(totalContainer).zIndex) || 0;
            validation.布局检查.层级关系 = {
                印章层级: stampZIndex,
                总金额层级: totalZIndex,
                层级正确: stampZIndex > totalZIndex,
                说明: stampZIndex > totalZIndex ? '✅ 印章在总金额上层' : '❌ 层级关系错误'
            };
        }

        // 检查内容溢出
        validation.布局检查.内容溢出 = container.scrollHeight > container.offsetHeight;

        DebugManager.log('DEBUG', 'DOM结构验证完成', validation);

        // 如果发现严重问题，尝试修正
        if (validation.布局检查.内容溢出) {
            DebugManager.log('WARN', '检测到内容溢出，尝试调整');
            container.style.height = `${container.scrollHeight}px`;
        }

        return validation;

    } catch (error) {
        DebugManager.log('ERROR', 'DOM结构验证失败', error);
        throw error;
    }
}

/**
 * 确保公司样式一致性
 * @function ensureCompanyStyleConsistency - 统一GoMyHire和Sky Mirror的样式处理
 * @param {HTMLElement} container - 容器元素
 * @returns {Promise<void>} 处理完成的Promise
 */
async function ensureCompanyStyleConsistency(container) {
    try {
        DebugManager.log('DEBUG', '开始确保公司样式一致性');

        // 获取当前公司信息
        const companySelector = document.getElementById('company-selector');
        const currentCompany = companySelector ? companySelector.value : 'gomyhire';

        // 统一处理公司信息显示
        const companyElements = container.querySelectorAll('.company-info, .company-details, .company-name, .company-address, .company-contact');
        companyElements.forEach(element => {
            // 统一字体和颜色
            element.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif';
            element.style.color = '#333';
            element.style.lineHeight = '1.6';
            element.style.textAlign = 'center';

            // 根据元素类型设置特定样式
            if (element.classList.contains('company-name') || element.tagName === 'H1' || element.tagName === 'H2') {
                element.style.fontSize = '18px';
                element.style.fontWeight = 'bold';
                element.style.color = '#1e40af';
                element.style.marginBottom = '10px';
            } else {
                element.style.fontSize = '14px';
                element.style.color = '#666';
                element.style.marginBottom = '5px';
            }
        });

        // 统一处理图片容器样式
        const imageContainers = container.querySelectorAll('.document-header-image-container, .company-footer-image-container, .company-stamp');
        imageContainers.forEach(container => {
            container.style.display = 'flex';
            container.style.alignItems = 'center';
            container.style.justifyContent = 'center';
            container.style.overflow = 'hidden';
        });

        // 统一处理图片样式 - 根据图片类型设置不同的填充方式
        const images = container.querySelectorAll('img');
        images.forEach(img => {
            img.style.imageRendering = 'high-quality';
            img.style.objectPosition = 'center';

            // 根据图片类型设置不同的object-fit和透明叠加效果
            if (img.closest('.document-header-image-container') || img.closest('.document-header')) {
                img.style.objectFit = 'cover'; // 页眉：完全填满130px区域
            } else if (img.closest('.unified-document-footer') || img.closest('.company-footer-image-container')) {
                img.style.objectFit = 'cover'; // 页脚：完全填满110px区域
            } else if (img.closest('.company-stamp')) {
                img.style.objectFit = 'contain'; // 印章：保持完整显示
                // 印章特殊处理：实现真正的透明叠加
                img.style.opacity = ExportConfig.stamp.opacity;
                img.style.mixBlendMode = ExportConfig.stamp.mixBlendMode;
                img.style.isolation = 'auto';
                img.style.background = 'transparent';
                img.style.backgroundColor = 'transparent';
                img.style.border = 'none';
                img.style.boxShadow = 'none';
            } else {
                img.style.objectFit = 'contain'; // 其他图片：保持完整显示
            }
        });

        DebugManager.log('DEBUG', '公司样式一致性处理完成', {
            当前公司: currentCompany,
            处理元素数量: companyElements.length + imageContainers.length + images.length
        });

    } catch (error) {
        DebugManager.log('ERROR', '公司样式一致性处理失败', error);
    }
}

/**
 * 确保布局一致性
 * @function ensureLayoutConsistency - 最终的布局一致性检查和修正
 * @param {HTMLElement} container - 容器元素
 * @returns {Promise<void>} 检查完成的Promise
 */
async function ensureLayoutConsistency(container) {
    try {
        DebugManager.log('DEBUG', '开始最终布局一致性检查');

        // 强制应用导出模式样式
        container.classList.add('export-mode');

        // 确保公司样式一致性
        await ensureCompanyStyleConsistency(container);

        // 再次修正关键元素定位
        await fixLayoutIssues(container);

        // 验证关键元素位置
        const layoutValidation = {
            页脚检查: false,
            印章检查: false,
            容器尺寸检查: false
        };

        // 检查页脚
        const footer = container.querySelector('.unified-document-footer, .company-footer-image-container');
        if (footer) {
            const footerStyle = window.getComputedStyle(footer);
            layoutValidation.页脚检查 = {
                position: footerStyle.position,
                bottom: footerStyle.bottom,
                height: footerStyle.height,
                zIndex: footerStyle.zIndex
            };
        }

        // 检查印章
        const stamp = container.querySelector('.company-stamp');
        if (stamp) {
            const stampStyle = window.getComputedStyle(stamp);
            layoutValidation.印章检查 = {
                position: stampStyle.position,
                bottom: stampStyle.bottom,
                right: stampStyle.right,
                opacity: stampStyle.opacity,
                zIndex: stampStyle.zIndex
            };
        }

        // 检查容器尺寸
        layoutValidation.容器尺寸检查 = {
            实际宽度: container.offsetWidth,
            实际高度: container.offsetHeight,
            预期宽度: ExportConfig.a4.widthPx,
            预期高度: ExportConfig.a4.heightPx,
            尺寸匹配: container.offsetWidth === ExportConfig.a4.widthPx
        };

        DebugManager.log('DEBUG', '布局一致性检查完成', layoutValidation);

        // 等待样式应用完成
        await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
        DebugManager.log('ERROR', '布局一致性检查失败', error);
    }
}

/**
 * 检查外部依赖库
 * @function checkExternalDependencies - 检查外部库的可用性
 * @returns {object} 依赖检查结果
 */
function checkExternalDependencies() {
    const dependencies = {
        html2canvas: typeof html2canvas !== 'undefined',
        jsPDF: typeof window.jspdf !== 'undefined'
    };

    DebugManager.log('DEBUG', '外部依赖检查', dependencies);
    return dependencies;
}
// #endregion

// #region 性能优化工具
/**
 * 性能优化管理器
 * @description 提供性能监控、内存管理和优化建议
 */
const PerformanceOptimizer = {
    // 性能阈值配置
    thresholds: {
        exportTime: 30000,      // 导出时间阈值：30秒
        memoryUsage: 0.8,       // 内存使用阈值：80%
        cssSize: 1024 * 1024,   // CSS大小阈值：1MB
        imageSize: 5 * 1024 * 1024  // 图片大小阈值：5MB
    },

    // 性能监控数据
    metrics: {
        exportCount: 0,
        totalExportTime: 0,
        averageExportTime: 0,
        memoryPeaks: [],
        errors: []
    },

    /**
     * 开始性能监控
     * @function startMonitoring - 开始监控导出性能
     * @param {string} operation - 操作名称
     * @returns {object} 监控会话对象
     */
    startMonitoring(operation) {
        const session = {
            operation,
            startTime: performance.now(),
            startMemory: this.getMemoryUsage(),
            id: Date.now() + Math.random()
        };

        DebugManager.log('DEBUG', `开始性能监控: ${operation}`, session);
        return session;
    },

    /**
     * 结束性能监控
     * @function endMonitoring - 结束性能监控并记录结果
     * @param {object} session - 监控会话对象
     * @returns {object} 性能报告
     */
    endMonitoring(session) {
        const endTime = performance.now();
        const endMemory = this.getMemoryUsage();
        const duration = endTime - session.startTime;
        const memoryDelta = endMemory.used - session.startMemory.used;

        const report = {
            operation: session.operation,
            duration,
            memoryDelta,
            startMemory: session.startMemory,
            endMemory,
            performance: this.evaluatePerformance(duration, memoryDelta)
        };

        // 更新统计数据
        this.metrics.exportCount++;
        this.metrics.totalExportTime += duration;
        this.metrics.averageExportTime = this.metrics.totalExportTime / this.metrics.exportCount;

        if (endMemory.used > this.metrics.memoryPeaks[this.metrics.memoryPeaks.length - 1]?.used || 0) {
            this.metrics.memoryPeaks.push(endMemory);
        }

        DebugManager.log('INFO', `性能监控完成: ${session.operation}`, report);
        return report;
    },

    /**
     * 获取内存使用情况
     * @function getMemoryUsage - 获取当前内存使用情况
     * @returns {object} 内存使用信息
     */
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
        return { used: 0, total: 0, limit: 0 };
    },

    /**
     * 评估性能表现
     * @function evaluatePerformance - 评估操作性能
     * @param {number} duration - 耗时（毫秒）
     * @param {number} memoryDelta - 内存变化（MB）
     * @returns {object} 性能评估结果
     */
    evaluatePerformance(duration, memoryDelta) {
        const timeScore = duration < this.thresholds.exportTime ? 'good' : 'poor';
        const memoryScore = Math.abs(memoryDelta) < 50 ? 'good' : 'poor';

        return {
            timeScore,
            memoryScore,
            overall: timeScore === 'good' && memoryScore === 'good' ? 'good' : 'needs_improvement',
            suggestions: this.generateSuggestions(duration, memoryDelta)
        };
    },

    /**
     * 生成优化建议
     * @function generateSuggestions - 根据性能数据生成优化建议
     * @param {number} duration - 耗时
     * @param {number} memoryDelta - 内存变化
     * @returns {array} 建议列表
     */
    generateSuggestions(duration, memoryDelta) {
        const suggestions = [];

        if (duration > this.thresholds.exportTime) {
            suggestions.push('导出时间过长，建议优化DOM结构或减少内容复杂度');
        }

        if (memoryDelta > 100) {
            suggestions.push('内存使用过多，建议清理临时对象或优化图片处理');
        }

        if (this.metrics.exportCount > 10 && this.metrics.averageExportTime > 15000) {
            suggestions.push('平均导出时间较长，建议检查系统性能或浏览器兼容性');
        }

        return suggestions;
    },

    /**
     * 清理内存
     * @function cleanup - 清理不必要的内存占用
     */
    cleanup() {
        try {
            // 清理性能监控历史数据（保留最近10次）
            if (this.metrics.memoryPeaks.length > 10) {
                this.metrics.memoryPeaks = this.metrics.memoryPeaks.slice(-10);
            }

            if (this.metrics.errors.length > 20) {
                this.metrics.errors = this.metrics.errors.slice(-20);
            }

            // 强制垃圾回收（如果可用）
            if (window.gc && typeof window.gc === 'function') {
                window.gc();
            }

            DebugManager.log('DEBUG', '内存清理完成');
        } catch (error) {
            DebugManager.log('WARN', '内存清理失败', error);
        }
    }
};
// #endregion

// #region 错误处理增强
/**
 * 错误处理管理器
 * @description 提供统一的错误处理、恢复机制和用户友好的错误提示
 */
const ErrorHandler = {
    // 错误类型定义
    errorTypes: {
        DEPENDENCY_MISSING: 'dependency_missing',
        CONTAINER_INVALID: 'container_invalid',
        EXPORT_TIMEOUT: 'export_timeout',
        MEMORY_OVERFLOW: 'memory_overflow',
        NETWORK_ERROR: 'network_error',
        UNKNOWN_ERROR: 'unknown_error'
    },

    // 错误恢复策略
    recoveryStrategies: {
        dependency_missing: '请检查html2canvas和jsPDF库是否正确加载',
        container_invalid: '请确保预览内容不为空且DOM结构完整',
        export_timeout: '导出超时，请尝试简化内容或刷新页面重试',
        memory_overflow: '内存不足，请关闭其他标签页或重启浏览器',
        network_error: '网络错误，请检查网络连接',
        unknown_error: '未知错误，请刷新页面重试'
    },

    /**
     * 处理导出错误
     * @function handleExportError - 统一处理导出过程中的错误
     * @param {Error} error - 错误对象
     * @param {string} operation - 操作类型
     * @param {object} context - 错误上下文
     * @returns {object} 错误处理结果
     */
    handleExportError(error, operation, context = {}) {
        const errorInfo = this.analyzeError(error, context);
        const recovery = this.getRecoveryStrategy(errorInfo.type);

        // 记录错误
        PerformanceOptimizer.metrics.errors.push({
            timestamp: new Date().toISOString(),
            operation,
            type: errorInfo.type,
            message: error.message,
            context
        });

        DebugManager.log('ERROR', `导出错误: ${operation}`, {
            错误类型: errorInfo.type,
            错误消息: error.message,
            恢复策略: recovery,
            上下文: context
        });

        // 显示用户友好的错误提示
        this.showUserFriendlyError(errorInfo, recovery);

        return {
            type: errorInfo.type,
            message: error.message,
            recovery,
            canRetry: this.canRetry(errorInfo.type)
        };
    },

    /**
     * 分析错误类型
     * @function analyzeError - 分析错误并确定类型
     * @param {Error} error - 错误对象
     * @param {object} context - 错误上下文
     * @returns {object} 错误分析结果
     */
    analyzeError(error, context) {
        const message = error.message.toLowerCase();

        if (message.includes('html2canvas') || message.includes('jspdf')) {
            return { type: this.errorTypes.DEPENDENCY_MISSING, severity: 'high' };
        }

        if (message.includes('timeout') || message.includes('超时')) {
            return { type: this.errorTypes.EXPORT_TIMEOUT, severity: 'medium' };
        }

        if (message.includes('memory') || message.includes('内存')) {
            return { type: this.errorTypes.MEMORY_OVERFLOW, severity: 'high' };
        }

        if (message.includes('container') || message.includes('容器')) {
            return { type: this.errorTypes.CONTAINER_INVALID, severity: 'medium' };
        }

        if (message.includes('network') || message.includes('网络')) {
            return { type: this.errorTypes.NETWORK_ERROR, severity: 'medium' };
        }

        return { type: this.errorTypes.UNKNOWN_ERROR, severity: 'low' };
    },

    /**
     * 获取恢复策略
     * @function getRecoveryStrategy - 获取错误恢复策略
     * @param {string} errorType - 错误类型
     * @returns {string} 恢复策略描述
     */
    getRecoveryStrategy(errorType) {
        return this.recoveryStrategies[errorType] || this.recoveryStrategies.unknown_error;
    },

    /**
     * 判断是否可以重试
     * @function canRetry - 判断错误是否可以重试
     * @param {string} errorType - 错误类型
     * @returns {boolean} 是否可以重试
     */
    canRetry(errorType) {
        const retryableErrors = [
            this.errorTypes.EXPORT_TIMEOUT,
            this.errorTypes.NETWORK_ERROR,
            this.errorTypes.UNKNOWN_ERROR
        ];
        return retryableErrors.includes(errorType);
    },

    /**
     * 显示用户友好的错误提示
     * @function showUserFriendlyError - 显示用户友好的错误提示
     * @param {object} errorInfo - 错误信息
     * @param {string} recovery - 恢复策略
     */
    showUserFriendlyError(errorInfo, recovery) {
        const title = '导出失败 / Export Failed';
        const message = `${recovery}\n\n如果问题持续存在，请尝试刷新页面或联系技术支持。`;

        // 使用更友好的提示方式
        if (typeof window !== 'undefined' && window.confirm) {
            const retry = window.confirm(`${title}\n\n${message}\n\n是否要重试？`);
            if (retry && this.canRetry(errorInfo.type)) {
                // 可以在这里实现重试逻辑
                DebugManager.log('INFO', '用户选择重试导出');
            }
        } else {
            console.error(`${title}: ${message}`);
        }
    }
};
// #endregion

// #region PDF导出功能
/**
 * 导出为PDF
 * @function exportToPDF - 使用现代化方案导出PDF
 * @param {HTMLElement} container - 容器元素
 */
async function exportToPDF(container) {
    try {
        DebugManager.startPerformance('PDF导出');

        // 检查html2canvas是否可用
        if (typeof html2canvas === 'undefined') {
            throw new Error('html2canvas库未加载，无法生成PDF');
        }

        // 检查jsPDF是否可用
        if (typeof window.jspdf === 'undefined') {
            throw new Error('jsPDF库未加载，无法生成PDF');
        }

        // 获取容器实际尺寸用于调试
        const containerRect = container.getBoundingClientRect();
        DebugManager.log('DEBUG', '导出前容器尺寸检查', {
            容器ID: container.id,
            实际尺寸: `${containerRect.width}x${containerRect.height}px`,
            配置尺寸: `${ExportConfig.a4.widthPx}x${ExportConfig.a4.heightPx}px`,
            缩放比例: ExportConfig.quality.scale,
            导出模式: container.classList.contains('export-mode')
        });

        // 等待所有自定义字体加载完成，避免字体替换导致排版差异
        if (document.fonts && document.fonts.ready) {
            try {
                await document.fonts.ready;
                DebugManager.log('DEBUG', '所有字体已加载完毕，开始生成PDF');
            } catch (fontErr) {
                DebugManager.log('WARN', '等待字体加载过程中出现问题，继续导出', fontErr);
            }
        }

        // 调试：在html2canvas调用前检查容器状态
        DebugManager.log('DEBUG', 'html2canvas调用前容器检查', {
            容器存在: !!container,
            容器可见: container.offsetWidth > 0 && container.offsetHeight > 0,
            容器尺寸: `${container.offsetWidth}x${container.offsetHeight}px`,
            容器内容长度: container.innerHTML.length,
            容器内容预览: container.innerHTML.substring(0, 200) + '...',
            子元素数量: container.children.length
        });

        let canvas;
        try {
            DebugManager.log('DEBUG', '开始调用html2canvas');

            // 使用高质量配置 - 修复尺寸设置确保A4标准
            const html2canvasOptions = {
                scale: ExportConfig.quality.scale, // 使用完整的3.125倍缩放达到300DPI
                useCORS: false, // 禁用CORS避免跨域问题
                allowTaint: true, // 允许污染确保兼容性
                backgroundColor: '#ffffff',
                logging: false, // 禁用内部日志避免干扰
                removeContainer: false,
                foreignObjectRendering: false, // 禁用以提高兼容性
                imageTimeout: 10000, // 减少超时时间
                // 强制使用A4标准尺寸，避免缩放容器影响
                width: ExportConfig.a4.widthPx,  // 强制794px
                height: ExportConfig.a4.heightPx, // 强制1123px
                // 兼容性优先的渲染选项
                pixelRatio: 1, // 固定为1避免异常设备像素比问题
                scrollX: 0,
                scrollY: 0,
                windowWidth: ExportConfig.a4.widthPx,  // 强制794px
                windowHeight: ExportConfig.a4.heightPx, // 强制1123px
                // 添加容错选项
                ignoreElements: (element) => {
                    // 忽略可能导致问题的元素
                    return element.classList.contains('ignore-export') ||
                           element.tagName === 'SCRIPT' ||
                           element.tagName === 'STYLE';
                }
            };

            DebugManager.log('DEBUG', 'html2canvas配置', html2canvasOptions);

            // 添加超时处理
            const html2canvasPromise = html2canvas(container, html2canvasOptions);

            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('html2canvas超时 - 15秒')), 15000);
            });

            canvas = await Promise.race([html2canvasPromise, timeoutPromise]);
            DebugManager.log('DEBUG', 'html2canvas调用成功完成');
        } catch (html2canvasError) {
            DebugManager.log('ERROR', 'html2canvas调用失败', {
                错误消息: html2canvasError.message,
                错误类型: html2canvasError.name,
                错误堆栈: html2canvasError.stack?.substring(0, 500)
            });
            throw new Error(`html2canvas处理失败: ${html2canvasError.message}`);
        }

        // 调试：检查生成的canvas
        DebugManager.log('DEBUG', 'html2canvas生成完成', {
            Canvas存在: !!canvas,
            Canvas尺寸: `${canvas.width}x${canvas.height}px`,
            Canvas为空: canvas.width === 0 || canvas.height === 0
        });

        // 创建PDF
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4',
            compress: false,
            precision: 16
        });

        // 获取PDF页面尺寸
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();

        // 计算图片在PDF中的尺寸（1:1映射）
        const imgWidth = pdfWidth;
        const imgHeight = pdfHeight;
        const x = 0;
        const y = 0;

        // 将Canvas转换为高质量图片数据
        const imgData = canvas.toDataURL('image/png', 1.0); // 无损PNG

        // 添加高质量图片到PDF
        pdf.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight, undefined, 'SLOW'); // 使用SLOW模式确保质量

        // 添加详细的调试日志
        DebugManager.log('DEBUG', 'PDF导出尺寸详情', {
            Canvas实际尺寸: `${canvas.width}x${canvas.height}px`,
            Canvas缩放比例: ExportConfig.quality.scale,
            PDF页面尺寸: `${pdfWidth}x${pdfHeight}mm`,
            图片在PDF中尺寸: `${imgWidth}x${imgHeight}mm`,
            图片位置: `(${x}, ${y})mm`,
            无缩放映射: '1:1直接映射到A4'
        });

        // 生成文件名
        const filename = generateExportFilename('pdf');

        // 保存PDF
        pdf.save(filename);

        DebugManager.endPerformance('PDF导出');
        DebugManager.log('INFO', 'PDF导出完成', {
            文件名: filename,
            Canvas尺寸: `${canvas.width}x${canvas.height}px`,
            PDF尺寸: `${imgWidth.toFixed(2)}x${imgHeight.toFixed(2)}mm`,
            分辨率: `${ExportConfig.dpi}DPI`,
            修复状态: '已移除二次缩放，使用1:1映射'
        });
    } catch (error) {
        DebugManager.log('ERROR', 'PDF导出失败', error);
        throw error;
    }
}
// #endregion

// #region 图片导出功能
/**
 * 导出为图片
 * @function exportToImage - 导出为PNG或JPEG图片
 * @param {HTMLElement} container - 容器元素
 * @param {string} format - 图片格式 ('png' 或 'jpeg')
 */
async function exportToImage(container, format) {
    try {
        DebugManager.startPerformance('图片导出');

        // 检查html2canvas是否可用
        if (typeof html2canvas === 'undefined') {
            throw new Error('html2canvas库未加载，无法生成图片');
        }

        // 获取容器实际尺寸用于调试
        const containerRect = container.getBoundingClientRect();
        DebugManager.log('DEBUG', '图片导出前容器尺寸检查', {
            容器ID: container.id,
            实际尺寸: `${containerRect.width}x${containerRect.height}px`,
            配置尺寸: `${ExportConfig.a4.widthPx}x${ExportConfig.a4.heightPx}px`,
            缩放比例: ExportConfig.quality.scale,
            导出格式: format,
            导出模式: container.classList.contains('export-mode')
        });

        // 等待字体加载完成，确保渲染一致
        if (document.fonts && document.fonts.ready) {
            try {
                await document.fonts.ready;
                DebugManager.log('DEBUG', '所有字体已加载完毕，开始生成图片');
            } catch (fontErr) {
                DebugManager.log('WARN', '等待字体加载过程中出现问题，继续导出', fontErr);
            }
        }

        // 调试：在html2canvas调用前检查容器状态
        DebugManager.log('DEBUG', 'html2canvas图片导出前容器检查', {
            容器存在: !!container,
            容器可见: container.offsetWidth > 0 && container.offsetHeight > 0,
            容器尺寸: `${container.offsetWidth}x${container.offsetHeight}px`,
            容器内容长度: container.innerHTML.length,
            容器内容预览: container.innerHTML.substring(0, 200) + '...',
            子元素数量: container.children.length
        });

        let canvas;
        try {
            DebugManager.log('DEBUG', '开始调用html2canvas进行图片导出');

            // 使用高质量配置 - 修复尺寸设置确保A4标准
            const html2canvasOptions = {
                scale: ExportConfig.quality.scale, // 使用完整的3.125倍缩放达到300DPI
                useCORS: false, // 禁用CORS避免跨域问题
                allowTaint: true, // 允许污染确保兼容性
                backgroundColor: '#ffffff',
                logging: false, // 禁用内部日志避免干扰
                removeContainer: false,
                foreignObjectRendering: false, // 禁用以提高兼容性
                imageTimeout: 10000, // 减少超时时间
                // 强制使用A4标准尺寸，避免缩放容器影响
                width: ExportConfig.a4.widthPx,  // 强制794px
                height: ExportConfig.a4.heightPx, // 强制1123px
                // 兼容性优先的渲染选项
                pixelRatio: 1, // 固定为1避免异常设备像素比问题
                scrollX: 0,
                scrollY: 0,
                windowWidth: ExportConfig.a4.widthPx,  // 强制794px
                windowHeight: ExportConfig.a4.heightPx, // 强制1123px
                // 添加容错选项
                ignoreElements: (element) => {
                    // 忽略可能导致问题的元素
                    return element.classList.contains('ignore-export') ||
                           element.tagName === 'SCRIPT' ||
                           element.tagName === 'STYLE';
                }
            };

            DebugManager.log('DEBUG', 'html2canvas配置', html2canvasOptions);

            // 添加超时处理
            const html2canvasPromise = html2canvas(container, html2canvasOptions);

            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('html2canvas超时 - 15秒')), 15000);
            });

            canvas = await Promise.race([html2canvasPromise, timeoutPromise]);
            DebugManager.log('DEBUG', 'html2canvas图片导出调用成功完成');
        } catch (html2canvasError) {
            DebugManager.log('ERROR', 'html2canvas图片导出调用失败', {
                错误消息: html2canvasError.message,
                错误类型: html2canvasError.name,
                错误堆栈: html2canvasError.stack?.substring(0, 500)
            });
            throw new Error(`html2canvas图片处理失败: ${html2canvasError.message}`);
        }

        // 调试：检查生成的canvas
        DebugManager.log('DEBUG', 'html2canvas图片生成完成', {
            Canvas存在: !!canvas,
            Canvas尺寸: `${canvas.width}x${canvas.height}px`,
            Canvas为空: canvas.width === 0 || canvas.height === 0
        });

        // 生成高质量图片数据
        let imageData;
        if (format === 'jpeg') {
            // 使用最高质量JPEG设置
            imageData = canvas.toDataURL('image/jpeg', ExportConfig.quality.jpegQuality);
        } else {
            // PNG无损压缩，确保最高质量
            imageData = canvas.toDataURL('image/png');
        }

        // 验证图片数据质量
        const imageSizeKB = Math.round(imageData.length / 1024);
        const expectedMinSize = (canvas.width * canvas.height * 3) / 1024; // 估算最小尺寸

        if (imageSizeKB < expectedMinSize * 0.1) {
            DebugManager.log('WARN', '图片数据可能质量过低', {
                实际大小: `${imageSizeKB}KB`,
                预期最小: `${Math.round(expectedMinSize * 0.1)}KB`,
                Canvas尺寸: `${canvas.width}x${canvas.height}px`
            });
        }

        // 生成文件名
        const filename = generateExportFilename(format);

        // 创建下载链接
        const link = document.createElement('a');
        link.download = filename;
        link.href = imageData;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        DebugManager.endPerformance('图片导出');
        DebugManager.log('INFO', `${format.toUpperCase()}图片导出完成`, {
            文件名: filename,
            Canvas尺寸: `${canvas.width}x${canvas.height}px`,
            数据大小: `${Math.round(imageData.length / 1024)}KB`,
            分辨率: `${ExportConfig.dpi}DPI`,
            修复状态: '已移除固定尺寸限制，使用容器实际尺寸',
            质量设置: format === 'jpeg' ? `JPEG质量${ExportConfig.quality.jpegQuality}` : 'PNG无损'
        });
    } catch (error) {
        DebugManager.log('ERROR', '图片导出失败', error);
        throw error;
    }
}
// #endregion

// #region ModernExportSystem核心模块
/**
 * 现代化高质量导出系统
 * @description 基于原生浏览器API的全新导出方案，支持300DPI高质量输出
 */
const ModernExportSystem = {
    // 导出配置（引用全局配置）
    config: ExportConfig,

    // 导出状态管理
    state: ExportConfig.state,

    /**
     * 初始化导出系统
     * @function init - 初始化现代化导出系统
     */
    init() {
        this.createExportUI();
        this.bindEvents();
        DebugManager.log('INFO', '现代化导出系统初始化完成');
    },

    /**
     * 创建导出UI界面
     * @function createExportUI - 创建简洁的导出界面，确保只在预览模组中创建
     */
    createExportUI() {
        // 只查找预览区域中的导出按钮容器，避免重复创建
        const previewSection = document.querySelector('.preview-section');
        if (!previewSection) {
            DebugManager.log('WARN', '未找到预览区域');
            return;
        }

        const exportButtonsContainer = previewSection.querySelector('.btn-group');
        if (!exportButtonsContainer) {
            DebugManager.log('WARN', '未找到预览区域中的导出按钮容器');
            return;
        }

        // 检查是否已经存在现代化导出UI，避免重复创建
        if (exportButtonsContainer.querySelector('.modern-export-ui')) {
            DebugManager.log('INFO', '现代化导出UI已存在，跳过创建');
            return;
        }

        // 清理现有导出按钮（如果存在）
        const existingButtons = exportButtonsContainer.querySelectorAll('button[data-export-btn]');
        existingButtons.forEach(btn => btn.remove());

        // 不再动态创建导出按钮，使用HTML中已有的简化按钮组

        DebugManager.log('INFO', '现代化导出UI已创建（仅在预览区域）');
    },

    /**
     * 绑定事件处理
     * @function bindEvents - 绑定导出按钮事件
     */
    bindEvents() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('export-btn')) {
                const format = e.target.dataset.format;
                if (format) {
                    this.startExport(format);
                }
            }
        });
    },

    /**
     * 导出策略管理器
     * @class ExportStrategyManager - 管理不同的导出策略
     */
    ExportStrategyManager: {
        /**
         * 获取导出策略
         * @function getStrategy - 根据参数获取合适的导出策略
         * @param {boolean} useIsolation - 是否使用DOM隔离
         * @returns {Object} 导出策略对象
         */
        getStrategy(useIsolation) {
            return useIsolation && typeof ExportIsolationSystem !== 'undefined'
                ? new IsolatedExportStrategy()
                : new TraditionalExportStrategy();
        }
    },

    /**
     * 开始导出流程（重构版本 - 使用策略模式）
     * @function startExport - 开始导出流程
     * @param {string} format - 导出格式 ('pdf', 'png', 'jpeg')
     * @param {boolean} useIsolation - 是否使用DOM隔离模式（默认true）
     */
    async startExport(format, useIsolation = true) {
        if (this.state.isExporting) {
            DebugManager.log('WARN', '导出正在进行中，请稍候');
            return;
        }

        // 开始性能监控
        const performanceSession = PerformanceOptimizer.startMonitoring(`${format.toUpperCase()}导出`);

        try {
            this.state.isExporting = true;
            DebugManager.log('INFO', `开始${format.toUpperCase()}导出`, { useIsolation });

            // 预检查：验证依赖和环境
            await this.preExportValidation(format);

            // 获取导出策略
            const strategy = this.ExportStrategyManager.getStrategy(useIsolation);

            // 执行导出
            await strategy.execute(format, this);

            DebugManager.log('INFO', `${format.toUpperCase()}导出完成`);

        } catch (error) {
            // 使用增强的错误处理
            const errorResult = ErrorHandler.handleExportError(error, `${format}导出`, {
                format,
                useIsolation
            });

            // 如果可以重试，提供重试选项
            if (errorResult.canRetry) {
                DebugManager.log('INFO', '错误可重试，等待用户决定');
            }

        } finally {
            // 结束性能监控
            PerformanceOptimizer.endMonitoring(performanceSession);

            // 清理导出样式
            ExportStyleManager.removeExportStyles();

            // 清理内存
            PerformanceOptimizer.cleanup();

            this.state.isExporting = false;
        }
    }
};

/**
 * 导出策略基类
 * @class ExportStrategy - 导出策略的基类
 */
class ExportStrategy {
    /**
     * 执行导出
     * @function execute - 执行导出策略
     * @param {string} format - 导出格式
     * @param {Object} context - 导出上下文
     * @returns {Promise<void>}
     */
    async execute(format, context) {
        throw new Error('子类必须实现execute方法');
    }
}

/**
 * DOM隔离导出策略
 * @class IsolatedExportStrategy - 使用DOM隔离的导出策略
 */
class IsolatedExportStrategy extends ExportStrategy {
    async execute(format, context) {
        DebugManager.log('INFO', '使用DOM隔离模式导出');

        let isolationSystem = null;
        try {
            // 初始化隔离系统
            isolationSystem = new ExportIsolationSystem();
            const isolatedContainer = await isolationSystem.initialize();

            // 验证隔离容器
            const validation = isolationSystem.domManager.validateA4Size();
            if (!validation.valid) {
                DebugManager.log('WARN', 'DOM隔离A4尺寸验证失败，回退到传统模式', validation);
                // 回退到传统策略
                const fallbackStrategy = new TraditionalExportStrategy();
                return await fallbackStrategy.execute(format, context);
            }

            DebugManager.log('SUCCESS', 'DOM隔离模式初始化成功', validation);

            // 使用隔离容器进行导出
            await context.performIsolatedExport(format, isolatedContainer);

        } catch (isolationError) {
            DebugManager.log('ERROR', 'DOM隔离模式初始化失败，回退到传统模式', isolationError);
            // 回退到传统策略
            const fallbackStrategy = new TraditionalExportStrategy();
            return await fallbackStrategy.execute(format, context);
        } finally {
            // 清理隔离系统
            if (isolationSystem) {
                isolationSystem.cleanup();
            }
        }
    }
}

/**
 * 传统导出策略
 * @class TraditionalExportStrategy - 使用传统模式的导出策略
 */
class TraditionalExportStrategy extends ExportStrategy {
    async execute(format, context) {
        DebugManager.log('INFO', '使用传统模式导出');

        let previewContainer = null;
        let originalContent = null;
        let originalClassName = null;

        try {
            // 准备导出环境
            const exportData = await this.prepareExportEnvironment(context);
            previewContainer = exportData.previewContainer;
            originalContent = exportData.originalContent;
            originalClassName = exportData.originalClassName;

            // 设置导出内容
            await this.setupExportContent(exportData, context);

            // 执行导出
            await context.performExportWithTimeout(format, previewContainer);

        } finally {
            // 恢复预览环境
            await this.restorePreviewEnvironment({
                previewContainer,
                originalContent,
                originalClassName
            }, context);
        }
    }

    /**
     * 准备导出环境
     * @function prepareExportEnvironment - 准备传统模式的导出环境
     * @param {Object} context - 导出上下文
     * @returns {Object} 导出数据
     */
    async prepareExportEnvironment(context) {
        // 修复：确保预览内容与表单数据完全同步
        DebugManager.log('DEBUG', '开始预览同步检查');

        // 强制刷新DOM缓存，确保数据一致性
        if (typeof refreshDOMCache === 'function') {
            refreshDOMCache();
        }

        // 更新预览并等待完成
        await context.ensurePreviewSync();

        // 获取导出容器
        const container = document.getElementById('document-container');
        if (!container || container.innerHTML.trim().length === 0) {
            throw new Error('预览内容为空，请先填写表单内容');
        }

        // 验证预览内容与表单数据的一致性
        await context.validateContentConsistency(container);

        // 获取预览容器
        const previewContainer = document.getElementById('document-container');
        const documentPreview = document.getElementById('document-preview');
        if (!previewContainer || !documentPreview) {
            throw new Error('预览容器不存在，无法导出');
        }

        // 临时保存原始内容、类名和样式
        const originalContent = previewContainer.innerHTML;
        const originalClassName = previewContainer.className;

        return {
            previewContainer,
            documentPreview,
            originalContent,
            originalClassName
        };
    }

    /**
     * 设置导出内容
     * @function setupExportContent - 设置导出模式的内容
     * @param {Object} exportData - 导出数据
     * @param {Object} context - 导出上下文
     */
    async setupExportContent(exportData, context) {
        const { previewContainer, documentPreview } = exportData;

        // 收集当前表单数据
        const formDataForExport = FormDataService.collect();

        // 渲染导出模式 HTML
        let exportHtml = '';
        if (formDataForExport.documentType === 'invoice') {
            if (typeof InvoiceTemplate !== 'undefined' && InvoiceTemplate.render) {
                exportHtml = InvoiceTemplate.render(formDataForExport, true);
            }
        } else {
            if (typeof ReceiptTemplate !== 'undefined' && ReceiptTemplate.render) {
                exportHtml = ReceiptTemplate.render(formDataForExport, true);
            }
        }

        // 嵌入CSS样式到导出HTML
        const embeddedCSS = extractAndEmbedCSS();
        exportHtml = `
            <style>${embeddedCSS}</style>
            <div id="document-container">
                ${exportHtml}
            </div>
        `;

        // 临时移除预览容器的缩放，确保导出时使用原始尺寸
        documentPreview.style.cssText += '; transform: none !important; --preview-scale-factor: 1 !important; scale: 1 !important; zoom: 1 !important;';
        documentPreview.style.transformOrigin = 'top center';

        // 使用正式的导出样式管理器
        ExportStyleManager.applyExportStyles(documentPreview);

        // 强制重新计算样式，确保变换被移除
        documentPreview.offsetHeight;

        // 验证变换是否被成功移除
        const computedTransform = window.getComputedStyle(documentPreview).transform;
        if (computedTransform !== 'none' && computedTransform !== 'matrix(1, 0, 0, 1, 0, 0)') {
            DebugManager.log('WARN', '变换移除可能不完全', {
                computedTransform: computedTransform,
                expectedTransform: 'none'
            });
        } else {
            DebugManager.log('DEBUG', '变换成功移除', {
                computedTransform: computedTransform
            });
        }

        // 设置导出模式内容和样式
        previewContainer.innerHTML = exportHtml;
        previewContainer.className = exportData.originalClassName + ' export-mode';

        // 字体优化 & 图片预处理
        optimizeFontRendering(previewContainer);
        await preprocessImages(previewContainer);

        // 最终布局一致性检查和修正
        await ensureLayoutConsistency(previewContainer);

        DebugManager.log('DEBUG', '传统模式导出内容设置完成', {
            容器ID: previewContainer.id,
            HTML长度: exportHtml.length,
            容器尺寸: `${previewContainer.offsetWidth}x${previewContainer.offsetHeight}px`,
            子元素数量: previewContainer.children.length,
            容器可见: previewContainer.offsetWidth > 0 && previewContainer.offsetHeight > 0,
            导出模式类: previewContainer.classList.contains('export-mode')
        });
    }

    /**
     * 恢复预览环境
     * @function restorePreviewEnvironment - 恢复预览模式的环境
     * @param {Object} restoreData - 恢复数据
     * @param {Object} context - 导出上下文
     */
    async restorePreviewEnvironment(restoreData, context) {
        const { previewContainer, originalContent, originalClassName } = restoreData;

        // 恢复预览容器的原始内容
        if (previewContainer && originalContent && originalClassName) {
            try {
                previewContainer.innerHTML = originalContent;
                previewContainer.className = originalClassName;
            } catch (restoreError) {
                DebugManager.log('ERROR', '恢复预览内容失败', restoreError);
            }
        }

        // 额外的恢复检查
        try {
            const container = document.getElementById('document-container');
            if (container && container.classList.contains('export-mode')) {
                container.classList.remove('export-mode');
                await context.restorePreviewContent(container);
            }

            // 恢复预览容器的缩放
            const documentPreview = document.getElementById('document-preview');
            if (documentPreview) {
                documentPreview.style.transform = ''; // 恢复CSS中的缩放设置
                DebugManager.log('DEBUG', '预览容器缩放已恢复');
            }
        } catch (restoreErr) {
            DebugManager.log('WARN', '导出后恢复预览时发生异常', restoreErr);
        }
    }
}

// 将策略类添加到全局作用域
window.ExportStrategy = ExportStrategy;
window.IsolatedExportStrategy = IsolatedExportStrategy;
window.TraditionalExportStrategy = TraditionalExportStrategy;

// #region 原有的导出方法（保持向后兼容）

// 扩展现有的ModernExportSystem
Object.assign(ModernExportSystem, {
    /**
     * 执行隔离模式导出
     * @function performIsolatedExport - 使用DOM隔离模式执行导出
     * @param {string} format - 导出格式
     * @param {HTMLElement} isolatedContainer - 隔离容器
     * @returns {Promise<void>}
     */
    async performIsolatedExport(format, isolatedContainer) {
        try {
            DebugManager.log('INFO', '开始隔离模式导出', { format });

            // 验证隔离容器状态
            if (!isolatedContainer || !isolatedContainer.parentNode) {
                throw new Error('隔离容器无效或未正确添加到DOM');
            }

            // 将隔离容器移动到可见位置进行导出
            isolatedContainer.style.position = 'static';
            isolatedContainer.style.left = 'auto';
            isolatedContainer.style.top = 'auto';

            // 强制重新计算样式
            isolatedContainer.offsetHeight;

            // 字体优化 & 图片预处理
            optimizeFontRendering(isolatedContainer);
            await preprocessImages(isolatedContainer);

            // 最终布局一致性检查和修正
            await ensureLayoutConsistency(isolatedContainer);

            // 验证最终尺寸
            const finalRect = isolatedContainer.getBoundingClientRect();
            DebugManager.log('DEBUG', '隔离容器最终尺寸验证', {
                实际尺寸: `${finalRect.width}x${finalRect.height}`,
                期望尺寸: '794x1123',
                尺寸匹配: Math.abs(finalRect.width - 794) < 5 && Math.abs(finalRect.height - 1123) < 5
            });

            // 导出（带超时保护）
            await this.performExportWithTimeout(format, isolatedContainer);

            DebugManager.log('SUCCESS', `隔离模式${format.toUpperCase()}导出完成`);

        } catch (error) {
            DebugManager.log('ERROR', '隔离模式导出失败', error);
            throw error;
        }
    },

    /**
     * 预导出验证
     * @function preExportValidation - 导出前的环境和依赖验证
     * @param {string} format - 导出格式
     */
    async preExportValidation(format) {
        // 检查依赖库
        const dependencies = checkExternalDependencies();

        if (!dependencies.html2canvas) {
            throw new Error('html2canvas库未加载，无法进行导出');
        }

        if (format === 'pdf' && !dependencies.jsPDF) {
            throw new Error('jsPDF库未加载，无法导出PDF');
        }

        // 检查内存使用情况
        const memory = PerformanceOptimizer.getMemoryUsage();
        if (memory.used > memory.limit * 0.9) {
            DebugManager.log('WARN', '内存使用率过高，可能影响导出性能', memory);
        }

        // 增强的浏览器兼容性检查
        const canvas = document.createElement('canvas');
        if (!canvas.getContext) {
            throw new Error('浏览器不支持Canvas，无法进行导出');
        }

        const ctx = canvas.getContext('2d');
        if (!ctx) {
            throw new Error('无法获取Canvas 2D上下文，导出功能不可用');
        }

        // 检查高DPI支持并修正异常值
        let devicePixelRatio = window.devicePixelRatio || 1;
        if (devicePixelRatio < 1 || devicePixelRatio > 4) {
            DebugManager.log('WARN', '设备像素比异常，使用默认值', {
                原始值: devicePixelRatio,
                修正值: 1
            });
            devicePixelRatio = 1; // 修正为标准值
        }

        // 检查必要的API支持
        const requiredAPIs = {
            'toDataURL': typeof canvas.toDataURL === 'function',
            'getImageData': typeof ctx.getImageData === 'function',
            'drawImage': typeof ctx.drawImage === 'function',
            'Promise': typeof Promise !== 'undefined',
            'fetch': typeof fetch !== 'undefined'
        };

        const unsupportedAPIs = Object.entries(requiredAPIs)
            .filter(([api, supported]) => !supported)
            .map(([api]) => api);

        if (unsupportedAPIs.length > 0) {
            throw new Error(`浏览器不支持必要的API: ${unsupportedAPIs.join(', ')}`);
        }

        DebugManager.log('DEBUG', '预导出验证通过', { format, dependencies, memory });
    },

    /**
     * 带超时保护的导出执行
     * @function performExportWithTimeout - 执行导出并提供超时保护
     * @param {string} format - 导出格式
     * @param {HTMLElement} container - 容器元素
     */
    async performExportWithTimeout(format, container) {
        const timeout = ExportConfig.timeout || 60000; // 默认60秒超时

        const exportPromise = format === 'pdf' ?
            exportToPDF(container) :
            exportToImage(container, format);

        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`导出超时：操作超过${timeout/1000}秒未完成`));
            }, timeout);
        });

        return Promise.race([exportPromise, timeoutPromise]);
    },

    /**
     * 确保预览同步
     * @function ensurePreviewSync - 确保预览内容与表单数据完全同步
     * @returns {Promise<void>} 同步完成的Promise
     */
    async ensurePreviewSync() {
        return new Promise((resolve) => {
            // 检查PreviewManager状态
            if (typeof PreviewManager !== 'undefined' && PreviewManager.isUpdating) {
                DebugManager.log('DEBUG', '等待预览更新完成...');

                // 等待预览更新完成
                const checkInterval = setInterval(() => {
                    if (!PreviewManager.isUpdating) {
                        clearInterval(checkInterval);
                        DebugManager.log('DEBUG', '预览更新已完成');
                        resolve();
                    }
                }, 50);

                // 超时保护
                setTimeout(() => {
                    clearInterval(checkInterval);
                    DebugManager.log('WARN', '预览同步等待超时，继续导出');
                    resolve();
                }, 2000);
            } else {
                // 直接解析，不强制更新预览以避免循环调用
                DebugManager.log('DEBUG', '跳过预览更新，直接继续导出');
                resolve();
            }
        });
    },

    /**
     * 验证内容完整性（增强版）
     * @function validateContentConsistency - 验证预览内容与表单数据的一致性
     * @param {HTMLElement} container - 预览容器
     * @returns {Promise<void>} 验证完成的Promise
     */
    async validateContentConsistency(container) {
        try {
            // 收集当前表单数据
            const formData = FormDataService.collect();

            // 分析预览容器内容
            const previewContent = {
                customerName: container.querySelector('.customer-name')?.textContent || '',
                itemsCount: container.querySelectorAll('.item-row').length,
                totalAmount: container.querySelector('.total-amount-container')?.textContent || '',
                documentNumber: container.querySelector('.document-number')?.textContent || '',
                hasImages: {
                    header: !!container.querySelector('.document-header-image-container img'),
                    footer: !!container.querySelector('.company-footer-image-container img'),
                    stamp: !!container.querySelector('.company-stamp img')
                },
                textContent: container.textContent.trim().length
            };

            // 增强的一致性检查
            const consistencyChecks = {
                项目数量匹配: (formData.items?.length || 0) === previewContent.itemsCount,
                总金额存在: !!previewContent.totalAmount,
                客户名称匹配: formData.customerName === previewContent.customerName,
                单据号码存在: !!previewContent.documentNumber,
                内容不为空: previewContent.textContent > 100, // 至少100字符
                图片状态检查: true // 图片存在性检查
            };

            DebugManager.log('DEBUG', '增强内容一致性验证', {
                表单数据: {
                    客户名称: formData.customerName,
                    项目数量: formData.items?.length || 0,
                    总金额: formData.total,
                    单据号码: formData.documentNumber
                },
                预览内容: previewContent,
                一致性检查: consistencyChecks
            });

            // 检查关键不一致问题（放宽验证条件）
            const criticalIssues = [];
            const warnings = [];

            if (!consistencyChecks.项目数量匹配) {
                warnings.push('项目数量不匹配');
            }
            if (!consistencyChecks.内容不为空) {
                criticalIssues.push('预览内容过少');
            }
            if (!consistencyChecks.总金额存在) {
                warnings.push('总金额缺失');
            }

            // 记录警告但不阻止导出
            if (warnings.length > 0) {
                DebugManager.log('WARN', '检测到内容不一致（警告级别）', warnings);
            }

            // 只有严重问题才阻止导出
            if (criticalIssues.length > 0) {
                DebugManager.log('ERROR', '检测到严重内容问题，尝试修复', criticalIssues);
                if (typeof updatePreview === 'function') {
                    updatePreview();
                    await new Promise(resolve => setTimeout(resolve, 300));

                    // 重新检查内容长度
                    const newContent = container.textContent.trim().length;
                    if (newContent < 50) { // 降低阈值从100到50
                        DebugManager.log('WARN', '预览内容仍然较少，但允许继续导出', {
                            原内容长度: previewContent.textContent,
                            新内容长度: newContent
                        });
                    }
                }
            }

        } catch (error) {
            DebugManager.log('ERROR', '内容一致性验证失败', error);
            // 验证失败时记录错误但不阻止导出（降级处理）
            DebugManager.log('WARN', '内容验证失败，但允许继续导出（降级模式）', {
                错误信息: error.message,
                容器内容长度: container.textContent.trim().length,
                容器HTML长度: container.innerHTML.length
            });

            // 只有在完全没有内容时才阻止导出
            if (container.textContent.trim().length < 10) {
                throw new Error(`预览内容过少，无法导出: ${error.message}`);
            }
        }
    },

    /**
     * 恢复预览模式的内容
     * @function restorePreviewContent - 导出完成后恢复预览模式的内容
     * @param {HTMLElement} container - 容器元素
     * @returns {Promise<void>} 恢复完成的Promise
     */
    async restorePreviewContent(container) {
        try {
            DebugManager.log('DEBUG', '开始恢复预览模式内容');

            // 收集当前表单数据
            const data = FormDataService.collect();

            // 使用预览模式重新渲染内容
            let html = '';
            if (data.documentType === 'invoice') {
                if (typeof InvoiceTemplate !== 'undefined' && InvoiceTemplate.render) {
                    html = InvoiceTemplate.render(data, false); // isExport = false
                }
            } else {
                if (typeof ReceiptTemplate !== 'undefined' && ReceiptTemplate.render) {
                    html = ReceiptTemplate.render(data, false); // isExport = false
                }
            }

            // 更新容器内容
            container.innerHTML = html;

            DebugManager.log('DEBUG', '预览模式内容恢复完成', {
                文档类型: data.documentType,
                导出模式: false,
                内容长度: html.length
            });

            // 等待DOM更新完成
            await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
            DebugManager.log('ERROR', '预览模式内容恢复失败', error);
            // 恢复失败时，尝试调用updatePreview
            try {
                if (typeof updatePreview === 'function') {
                    updatePreview();
                }
            } catch (fallbackError) {
                DebugManager.log('ERROR', '预览恢复备用方案也失败', fallbackError);
            }
        }
    }
});
// #endregion

// #region 调试工具
/**
 * 调试图片导出功能
 * @function debugImageExport - 调试图片导出功能，提供详细的诊断信息
 */
function debugImageExport() {
    console.log('🔍 开始图片导出调试...');

    const debugInfo = {
        html2canvas可用: typeof html2canvas !== 'undefined',
        html2canvas版本: typeof html2canvas !== 'undefined' ? html2canvas.version || '未知' : '未加载',
        jsPDF可用: typeof window.jspdf !== 'undefined',
        浏览器: navigator.userAgent,
        文档就绪状态: document.readyState,
        容器状态: {
            'document-container': !!document.getElementById('document-container'),
            'document-preview': !!document.getElementById('document-preview'),
            'container内容长度': document.getElementById('document-container')?.innerHTML?.length || 0,
            'preview内容长度': document.getElementById('document-preview')?.innerHTML?.length || 0
        },
        表单数据: (() => {
            try {
                return FormDataService.collect();
            } catch (e) {
                return `获取失败: ${e.message}`;
            }
        })(),
        导出配置: ExportConfig,
        ModernExportSystem状态: {
            可用: typeof ModernExportSystem !== 'undefined',
            正在导出: ModernExportSystem?.state?.isExporting || false
        },
        CSS变量检查: {
            'A4宽度': getComputedStyle(document.documentElement).getPropertyValue('--a4-width-px'),
            'A4高度': getComputedStyle(document.documentElement).getPropertyValue('--a4-height-px'),
            '预览缩放因子': getComputedStyle(document.documentElement).getPropertyValue('--preview-scale-factor')
        },
        修复状态: '✅ 已修复导出内容缩小问题：移除固定尺寸限制，添加导出模式缩放重置'
    };

    console.table(debugInfo);
    console.log('🔧 导出尺寸修复状态: 已移除固定尺寸限制，添加导出模式缩放重置');
    console.log('📏 修复详情:');
    console.log('  1. 添加了 .export-mode 类的缩放重置 CSS');
    console.log('  2. 移除了 html2canvas 的固定 width/height 限制');
    console.log('  3. 简化了 PDF 导出逻辑，使用 1:1 映射');
    console.log('  4. 增强了调试日志，便于问题定位');
    console.log('  5. 印章透明度设置为0.9');
    console.log('  6. 添加了10px左右边距');
    alert('调试信息已输出到浏览器控制台，请查看详细信息。\n\n✅ 修复状态: 已解决导出内容缩小问题');

    return debugInfo;
}
// #endregion

// 兼容性函数已移除 - 请直接使用 ModernExportSystem.startExport(format)

/**
 * 基础功能测试函数
 * @function testBasicFunctionality - 测试基础功能可用性
 */
function testBasicFunctionality() {
    console.log('⚙️ 测试基础功能...');

    try {
        const results = {
            exportConfigAvailable: typeof ExportConfig !== 'undefined',
            debugManagerAvailable: typeof DebugManager !== 'undefined',
            modernExportSystemAvailable: typeof ModernExportSystem !== 'undefined',
            generateExportFilenameFunction: typeof generateExportFilename === 'function',
            extractAndEmbedCSSFunction: typeof extractAndEmbedCSS === 'function',
            optimizeFontRenderingFunction: typeof optimizeFontRendering === 'function',
            preprocessImagesFunction: typeof preprocessImages === 'function',
            exportToPDFFunction: typeof exportToPDF === 'function',
            exportToImageFunction: typeof exportToImage === 'function',
            debugImageExportFunction: typeof debugImageExport === 'function',
            html2canvasAvailable: typeof html2canvas !== 'undefined',
            jsPDFAvailable: typeof window.jspdf !== 'undefined'
        };

        console.log('✅ 基础功能测试完成:', results);
        return results;

    } catch (error) {
        console.error('❌ 基础功能测试失败:', error);
        return { error: error.message };
    }
}
// #endregion

// #region 模块导出和全局绑定
// 将主要函数绑定到window对象，确保兼容性
if (typeof window !== 'undefined') {
    // 核心导出功能
    window.ModernExportSystem = ModernExportSystem;
    window.ExportConfig = ExportConfig;
    window.DebugManager = DebugManager;

    // 表单数据服务
    window.FormDataService = FormDataService;

    // 性能和错误处理
    window.PerformanceOptimizer = PerformanceOptimizer;
    window.ErrorHandler = ErrorHandler;

    // 工具函数
    window.generateExportFilename = generateExportFilename;
    window.extractAndEmbedCSS = extractAndEmbedCSS;
    window.optimizeFontRendering = optimizeFontRendering;
    window.preprocessImages = preprocessImages;
    window.reRenderForExport = reRenderForExport;

    // 导出函数
    window.exportToPDF = exportToPDF;
    window.exportToImage = exportToImage;

    // 兼容性函数已移除 - 请直接使用 ModernExportSystem.startExport(format)

    // 调试工具
    window.debugImageExport = debugImageExport;
    window.testBasicFunctionality = testBasicFunctionality;
    window.checkExternalDependencies = checkExternalDependencies;

    console.log('✅ 导出组件模块已加载完成');
    console.log('📦 可用功能:', {
        '导出系统': 'ModernExportSystem',
        '配置管理': 'ExportConfig',
        '调试工具': 'DebugManager',
        '性能优化': 'PerformanceOptimizer',
        '错误处理': 'ErrorHandler',
        'PDF导出': 'exportToPDF',
        '图片导出': 'exportToImage',
        '调试功能': 'debugImageExport'
    });
}
// #endregion
