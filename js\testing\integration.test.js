/**
 * 集成测试
 * @file integration.test.js - 模块间集成测试
 * @description 测试模块之间的协作和数据流
 */

// 测试模块加载和初始化集成
describe('模块加载和初始化集成测试', () => {
    it('所有核心模块应该正确加载', () => {
        const requiredModules = [
            'AppConfig',
            'CurrencyConfig', 
            'CompanyInfo',
            'ExportConfig',
            'TemplateConfig',
            'DOMCache',
            'SafeDOM',
            'DebugManager'
        ];
        
        requiredModules.forEach(moduleName => {
            Assert.ok(window[moduleName], `${moduleName}模块应该存在`);
        });
    });
    
    it('模块之间的依赖关系应该正确', () => {
        // DebugManager应该能够使用AppConfig的配置
        if (window.AppConfig && window.DebugManager) {
            const originalDebugMode = window.AppConfig.debugMode;
            
            window.AppConfig.debugMode = true;
            window.DebugManager.init({
                enabled: window.AppConfig.debugMode,
                logLevel: window.AppConfig.logLevel
            });
            
            Assert.equal(window.DebugManager.enabled, true, 'DebugManager应该使用AppConfig的调试模式');
            
            // 恢复原始状态
            window.AppConfig.debugMode = originalDebugMode;
        }
    });
    
    it('DOMCache应该能够与SafeDOM协作', () => {
        if (window.DOMCache && window.SafeDOM) {
            // 创建测试元素
            const testElement = document.createElement('div');
            testElement.id = 'integration-test-element';
            document.body.appendChild(testElement);
            
            try {
                // DOMCache获取元素
                const cachedElement = window.DOMCache.get('documentType');
                
                // SafeDOM获取同一元素
                const safeElement = window.SafeDOM.get('document-type');
                
                // 两者应该能够协同工作（即使元素不存在也不应该报错）
                Assert.type(window.DOMCache.getCacheStats, 'function', 'DOMCache应该提供统计功能');
                Assert.type(window.SafeDOM.getValue, 'function', 'SafeDOM应该提供安全访问功能');
                
            } finally {
                // 清理测试元素
                if (testElement.parentNode) {
                    testElement.parentNode.removeChild(testElement);
                }
            }
        }
    });
});

// 测试配置系统集成
describe('配置系统集成测试', () => {
    it('AppConfig更新应该影响其他模块', () => {
        if (window.AppConfig && window.DebugManager) {
            const originalConfig = {
                debugMode: window.AppConfig.debugMode,
                logLevel: window.AppConfig.logLevel
            };
            
            try {
                // 更新配置
                window.AppConfig.updateConfig({
                    debugMode: true,
                    logLevel: 'DEBUG'
                });
                
                // 重新初始化DebugManager
                window.DebugManager.init({
                    enabled: window.AppConfig.debugMode,
                    logLevel: window.AppConfig.logLevel
                });
                
                Assert.equal(window.AppConfig.debugMode, true, '配置应该更新');
                Assert.equal(window.DebugManager.enabled, true, 'DebugManager应该响应配置变化');
                
            } finally {
                // 恢复原始配置
                window.AppConfig.updateConfig(originalConfig);
            }
        }
    });
    
    it('货币配置应该与公司配置协作', () => {
        if (window.CurrencyConfig && window.CompanyInfo && window.AppConfig) {
            const originalCurrency = window.AppConfig.currentCurrency;
            const originalCompany = window.AppConfig.currentCompany;
            
            try {
                // 设置马来西亚公司和货币
                window.AppConfig.currentCompany = 'gomyhire';
                window.AppConfig.currentCurrency = 'MYR';
                
                const company = window.CompanyInfo[window.AppConfig.currentCompany];
                const currency = window.CurrencyConfig[window.AppConfig.currentCurrency];
                
                Assert.ok(company, '应该找到对应的公司信息');
                Assert.ok(currency, '应该找到对应的货币信息');
                Assert.equal(currency.symbol, 'RM', 'MYR货币符号应该是RM');
                
            } finally {
                // 恢复原始配置
                window.AppConfig.currentCurrency = originalCurrency;
                window.AppConfig.currentCompany = originalCompany;
            }
        }
    });
});

// 测试导出配置集成
describe('导出配置集成测试', () => {
    it('ExportConfig应该与TemplateConfig协作', () => {
        if (window.ExportConfig && window.TemplateConfig) {
            const exportConfig = window.ExportConfig;
            const templateConfig = window.TemplateConfig;
            
            // 验证A4尺寸配置
            Assert.equal(exportConfig.a4.widthPx, 794, 'A4宽度配置正确');
            Assert.equal(exportConfig.a4.heightPx, 1123, 'A4高度配置正确');
            
            // 验证图片尺寸与模板配置兼容
            Assert.ok(exportConfig.images.header.height > 0, '页眉高度应该大于0');
            Assert.ok(exportConfig.images.footer.height > 0, '页脚高度应该大于0');
            
            // 验证模板配置的合理性
            Assert.ok(templateConfig.invoice.showCompanyInfo !== templateConfig.receipt.showCompanyInfo, 
                '发票和收据的公司信息显示配置应该不同');
        }
    });
    
    it('导出质量配置应该一致', () => {
        if (window.ExportConfig) {
            const config = window.ExportConfig;
            
            // 验证DPI配置一致性
            Assert.equal(config.a4.dpi, config.quality.dpi, 'A4和质量配置的DPI应该一致');
            
            // 验证图片尺寸合理性
            const headerRatio = config.images.header.width / config.images.header.height;
            const footerRatio = config.images.footer.width / config.images.footer.height;
            
            Assert.ok(headerRatio > 1, '页眉应该是横向的');
            Assert.ok(footerRatio > 1, '页脚应该是横向的');
            
            // 验证印章是正方形
            Assert.equal(config.images.stamp.width, config.images.stamp.height, '印章应该是正方形');
        }
    });
});

// 测试错误处理集成
describe('错误处理集成测试', () => {
    it('DebugManager应该能够记录其他模块的错误', () => {
        if (window.DebugManager) {
            const originalLogHistory = window.DebugManager.logHistory.length;
            
            // 模拟一个错误
            const testError = new Error('集成测试错误');
            window.DebugManager.logError(testError, '集成测试上下文');
            
            Assert.ok(window.DebugManager.logHistory.length > originalLogHistory, 
                '应该记录新的错误日志');
            
            const lastLog = window.DebugManager.logHistory[window.DebugManager.logHistory.length - 1];
            Assert.equal(lastLog.level, 'ERROR', '日志级别应该是ERROR');
            Assert.ok(lastLog.message.includes('集成测试上下文'), '应该包含错误上下文');
        }
    });
    
    it('SafeDOM应该优雅处理不存在的元素', () => {
        if (window.SafeDOM && window.DebugManager) {
            const originalLogCount = window.DebugManager.logHistory.length;
            
            // 尝试访问不存在的元素
            const result = window.SafeDOM.get('non-existent-element-12345');
            
            Assert.equal(result, null, '不存在的元素应该返回null');
            
            // 不应该产生错误日志（SafeDOM应该优雅处理）
            const newLogCount = window.DebugManager.logHistory.length;
            const errorLogs = window.DebugManager.logHistory.slice(originalLogCount)
                .filter(log => log.level === 'ERROR');
            
            Assert.equal(errorLogs.length, 0, 'SafeDOM不应该产生错误日志');
        }
    });
});

// 测试性能监控集成
describe('性能监控集成测试', () => {
    it('DebugManager性能监控应该正常工作', async () => {
        if (window.DebugManager) {
            const operationName = 'integration-test-operation';
            
            // 开始性能监控
            const id = window.DebugManager.startPerformance(operationName);
            Assert.type(id, 'string', '性能监控ID应该是字符串');
            
            // 模拟一些异步操作
            await new Promise(resolve => setTimeout(resolve, 50));
            
            // 结束性能监控
            const metric = window.DebugManager.endPerformance(id);
            
            Assert.ok(metric, '应该返回性能指标');
            Assert.type(metric.duration, 'number', '持续时间应该是数字');
            Assert.ok(metric.duration >= 45, '持续时间应该大约50ms');
            Assert.equal(metric.operation, operationName, '操作名称应该正确');
        }
    });
    
    it('系统状态应该反映模块状态', () => {
        if (window.DebugManager) {
            const systemStatus = window.DebugManager.getSystemStatus();
            
            Assert.type(systemStatus, 'object', '系统状态应该是对象');
            Assert.ok(systemStatus.浏览器信息, '应该包含浏览器信息');
            Assert.ok(systemStatus.页面信息, '应该包含页面信息');
            
            // 验证内存信息（如果支持）
            if (systemStatus.内存使用 && systemStatus.内存使用.used !== undefined) {
                Assert.type(systemStatus.内存使用.used, 'number', '内存使用量应该是数字');
                Assert.ok(systemStatus.内存使用.used >= 0, '内存使用量应该大于等于0');
            }
        }
    });
});

// 测试本地存储集成
describe('本地存储集成测试', () => {
    it('AppConfig本地存储应该正常工作', () => {
        if (window.AppConfig) {
            const originalConfig = {
                currentCompany: window.AppConfig.currentCompany,
                currentCurrency: window.AppConfig.currentCurrency,
                debugMode: window.AppConfig.debugMode
            };
            
            try {
                // 设置测试配置
                const testConfig = {
                    currentCompany: 'test-company-integration',
                    currentCurrency: 'EUR',
                    debugMode: true
                };
                
                window.AppConfig.updateConfig(testConfig);
                
                // 验证配置已更新
                Assert.equal(window.AppConfig.currentCompany, testConfig.currentCompany, 
                    '公司配置应该更新');
                Assert.equal(window.AppConfig.currentCurrency, testConfig.currentCurrency, 
                    '货币配置应该更新');
                Assert.equal(window.AppConfig.debugMode, testConfig.debugMode, 
                    '调试模式应该更新');
                
                // 验证本地存储
                const savedConfig = localStorage.getItem('smartoffice_config');
                Assert.ok(savedConfig, '配置应该保存到本地存储');
                
                const parsedConfig = JSON.parse(savedConfig);
                Assert.equal(parsedConfig.currentCompany, testConfig.currentCompany, 
                    '本地存储的公司配置应该正确');
                
            } finally {
                // 恢复原始配置
                window.AppConfig.updateConfig(originalConfig);
            }
        }
    });
});

// 测试模块通信
describe('模块通信测试', () => {
    it('模块应该能够通过全局对象通信', () => {
        // 验证所有模块都正确暴露到全局作用域
        const globalModules = [
            'AppConfig', 'CurrencyConfig', 'CompanyInfo', 
            'ExportConfig', 'TemplateConfig', 'DOMCache', 
            'SafeDOM', 'DebugManager'
        ];
        
        globalModules.forEach(moduleName => {
            Assert.ok(window[moduleName], `${moduleName}应该在全局作用域中可用`);
            Assert.type(window[moduleName], 'object', `${moduleName}应该是对象`);
        });
    });
    
    it('配置变更应该能够通知其他模块', () => {
        if (window.AppConfig && window.DebugManager) {
            // 记录初始状态
            const initialLogCount = window.DebugManager.logHistory.length;
            
            // 更新配置并触发日志
            window.AppConfig.debugMode = true;
            window.DebugManager.log('INFO', '配置变更测试', {
                debugMode: window.AppConfig.debugMode
            });
            
            // 验证日志已记录
            Assert.ok(window.DebugManager.logHistory.length > initialLogCount, 
                '应该记录配置变更日志');
            
            const lastLog = window.DebugManager.logHistory[window.DebugManager.logHistory.length - 1];
            Assert.equal(lastLog.level, 'INFO', '日志级别应该正确');
            Assert.ok(lastLog.data && lastLog.data.debugMode === true, 
                '日志数据应该包含配置信息');
        }
    });
});

// 测试数据一致性
describe('数据一致性测试', () => {
    it('货币和公司数据应该保持一致', () => {
        if (window.CurrencyConfig && window.CompanyInfo) {
            // 验证所有公司都有对应的默认货币
            for (const [companyKey, company] of Object.entries(window.CompanyInfo)) {
                Assert.ok(company.name, `公司 ${companyKey} 应该有名称`);
                Assert.ok(company.address, `公司 ${companyKey} 应该有地址`);
                
                // 验证公司信息的完整性
                const requiredFields = ['name', 'fullName', 'address', 'phone', 'email'];
                requiredFields.forEach(field => {
                    Assert.ok(company[field], `公司 ${companyKey} 应该有 ${field} 字段`);
                });
            }
            
            // 验证所有货币都有完整的配置
            for (const [currencyKey, currency] of Object.entries(window.CurrencyConfig)) {
                Assert.ok(currency.symbol, `货币 ${currencyKey} 应该有符号`);
                Assert.ok(currency.name, `货币 ${currencyKey} 应该有名称`);
                Assert.equal(currency.code, currencyKey, `货币代码应该与键名一致`);
                Assert.type(currency.decimals, 'number', `货币 ${currencyKey} 的小数位应该是数字`);
            }
        }
    });
    
    it('导出配置应该保持内部一致性', () => {
        if (window.ExportConfig) {
            const config = window.ExportConfig;
            
            // 验证A4尺寸的宽高比
            const aspectRatio = config.a4.widthPx / config.a4.heightPx;
            const expectedRatio = 210 / 297; // A4标准比例
            const tolerance = 0.01;
            
            Assert.ok(Math.abs(aspectRatio - expectedRatio) < tolerance, 
                'A4尺寸比例应该符合标准');
            
            // 验证图片尺寸不超过A4尺寸
            Assert.ok(config.images.header.width <= config.a4.widthPx, 
                '页眉宽度不应超过A4宽度');
            Assert.ok(config.images.footer.width <= config.a4.widthPx, 
                '页脚宽度不应超过A4宽度');
            
            // 验证印章尺寸合理
            Assert.ok(config.images.stamp.width <= config.a4.widthPx / 4, 
                '印章宽度应该合理');
            Assert.ok(config.images.stamp.height <= config.a4.heightPx / 4, 
                '印章高度应该合理');
        }
    });
});
