/**
 * @file DOM重组导出管理器
 * @description 负责在导出时创建独立的DOM副本，完全隔离预览缩放和导出模式
 * @version 1.0
 * @date 2024-12-21
 * <AUTHOR> Assistant
 */

/**
 * @class ExportDOMManager
 * @description DOM重组导出管理器 - 彻底解决CSS缩放变换问题
 */
class ExportDOMManager {
    constructor() {
        this.isolatedContainer = null;
        this.isolatedStyleSheet = null;
        this.originalContainer = null;
        this.isActive = false;
        
        // 调试管理器
        this.debugManager = window.DebugManager || console;
        
        // 配置参数
        this.config = {
            containerClass: 'export-isolated-container',
            contentClass: 'export-isolated-content',
            headerClass: 'export-header-container',
            footerClass: 'export-footer-container',
            stampClass: 'export-stamp-container',
            tableClass: 'export-table',
            totalClass: 'export-total-container',
            styleSheetPath: 'styles/export-isolated.css',
            a4Width: 794,
            a4Height: 1123
        };
    }

    /**
     * @function createIsolatedDOM - 创建隔离的DOM结构
     * @description 复制原始DOM并应用导出专用样式
     * @returns {Promise<HTMLElement>} 隔离的容器元素
     */
    async createIsolatedDOM() {
        try {
            this.debugManager.log('INFO', 'ExportDOMManager: 开始创建隔离DOM');
            
            // 获取原始容器
            this.originalContainer = document.getElementById('document-container');
            if (!this.originalContainer) {
                throw new Error('原始文档容器不存在');
            }

            // 创建隔离容器
            this.isolatedContainer = document.createElement('div');
            this.isolatedContainer.id = 'export-isolated-container';
            this.isolatedContainer.className = this.config.containerClass;
            
            // 设置固定尺寸
            this.isolatedContainer.style.cssText = `
                width: ${this.config.a4Width}px !important;
                height: ${this.config.a4Height}px !important;
                position: relative !important;
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                transform: none !important;
                scale: 1 !important;
                zoom: 1 !important;
                overflow: visible !important;
                box-sizing: border-box !important;
            `;

            // 加载专用样式表
            await this.loadIsolatedStyleSheet();

            // 复制内容结构
            await this.cloneContentStructure();

            // 应用专用样式类
            this.applyIsolatedStyles();

            // 添加到页面（隐藏状态）
            this.isolatedContainer.style.position = 'absolute';
            this.isolatedContainer.style.left = '-9999px';
            this.isolatedContainer.style.top = '-9999px';
            document.body.appendChild(this.isolatedContainer);

            this.isActive = true;
            this.debugManager.log('SUCCESS', 'ExportDOMManager: 隔离DOM创建成功');
            
            return this.isolatedContainer;

        } catch (error) {
            this.debugManager.log('ERROR', 'ExportDOMManager: 创建隔离DOM失败', error);
            throw error;
        }
    }

    /**
     * @function loadIsolatedStyleSheet - 加载导出专用样式表
     * @description 动态加载export-isolated.css
     * @returns {Promise<void>}
     */
    async loadIsolatedStyleSheet() {
        return new Promise((resolve, reject) => {
            // 检查是否已经加载
            const existingLink = document.querySelector(`link[href*="export-isolated.css"]`);
            if (existingLink) {
                resolve();
                return;
            }

            // 创建样式表链接
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = this.config.styleSheetPath;
            link.id = 'export-isolated-styles';
            
            link.onload = () => {
                this.isolatedStyleSheet = link;
                this.debugManager.log('INFO', 'ExportDOMManager: 专用样式表加载成功');
                resolve();
            };
            
            link.onerror = () => {
                this.debugManager.log('ERROR', 'ExportDOMManager: 专用样式表加载失败');
                reject(new Error('无法加载导出专用样式表'));
            };

            document.head.appendChild(link);
        });
    }

    /**
     * @function cloneContentStructure - 复制内容结构
     * @description 深度复制原始DOM结构并清理不需要的元素
     * @returns {void}
     */
    async cloneContentStructure() {
        try {
            // 获取预览容器
            const previewContainer = this.originalContainer.querySelector('#document-preview');
            if (!previewContainer) {
                throw new Error('预览容器不存在');
            }

            // 深度克隆预览容器内容
            const clonedContent = previewContainer.cloneNode(true);
            clonedContent.id = 'export-isolated-content';
            clonedContent.className = this.config.contentClass;

            // 清理克隆内容
            this.cleanClonedContent(clonedContent);

            // 添加到隔离容器
            this.isolatedContainer.appendChild(clonedContent);

            this.debugManager.log('INFO', 'ExportDOMManager: 内容结构复制完成');

        } catch (error) {
            this.debugManager.log('ERROR', 'ExportDOMManager: 复制内容结构失败', error);
            throw error;
        }
    }

    /**
     * @function cleanClonedContent - 清理克隆的内容
     * @description 移除不需要的元素和属性
     * @param {HTMLElement} content - 克隆的内容元素
     * @returns {void}
     */
    cleanClonedContent(content) {
        // 移除所有内联样式中的transform相关属性
        const allElements = content.querySelectorAll('*');
        allElements.forEach(element => {
            if (element.style) {
                element.style.removeProperty('transform');
                element.style.removeProperty('scale');
                element.style.removeProperty('zoom');
            }
        });

        // 移除响应式相关的类
        const responsiveClasses = ['mobile-view', 'tablet-view', 'desktop-view'];
        responsiveClasses.forEach(className => {
            const elements = content.querySelectorAll(`.${className}`);
            elements.forEach(el => el.classList.remove(className));
        });

        // 移除预览模式相关的类
        content.classList.remove('preview-mode', 'scaled-preview');
        
        this.debugManager.log('INFO', 'ExportDOMManager: 内容清理完成');
    }

    /**
     * @function applyIsolatedStyles - 应用隔离样式
     * @description 为克隆的元素应用导出专用样式类
     * @returns {void}
     */
    applyIsolatedStyles() {
        try {
            const content = this.isolatedContainer.querySelector('#export-isolated-content');
            if (!content) return;

            // 应用页眉样式
            const header = content.querySelector('.document-header-image-container, .document-header');
            if (header) {
                header.className = this.config.headerClass;
            }

            // 应用页脚样式
            const footer = content.querySelector('.unified-document-footer, .company-footer-image-container');
            if (footer) {
                footer.className = this.config.footerClass;
            }

            // 应用印章样式
            const stamp = content.querySelector('.company-stamp');
            if (stamp) {
                stamp.className = this.config.stampClass;
            }

            // 应用表格样式
            const tables = content.querySelectorAll('.items-table, table');
            tables.forEach(table => {
                table.className = this.config.tableClass;
            });

            // 应用总金额容器样式
            const totalContainers = content.querySelectorAll('.total-amount-container');
            totalContainers.forEach(container => {
                container.className = this.config.totalClass;
            });

            // 应用文本样式
            this.applyTextStyles(content);

            this.debugManager.log('INFO', 'ExportDOMManager: 隔离样式应用完成');

        } catch (error) {
            this.debugManager.log('ERROR', 'ExportDOMManager: 应用隔离样式失败', error);
        }
    }

    /**
     * @function applyTextStyles - 应用文本样式
     * @description 为文本元素应用导出专用样式类
     * @param {HTMLElement} content - 内容容器
     * @returns {void}
     */
    applyTextStyles(content) {
        // 标题
        const titles = content.querySelectorAll('h1, .document-title');
        titles.forEach(title => title.classList.add('export-title'));

        // 副标题
        const subtitles = content.querySelectorAll('h2, h3, .section-title');
        subtitles.forEach(subtitle => subtitle.classList.add('export-subtitle'));

        // 普通文本
        const texts = content.querySelectorAll('p, span, div:not([class])');
        texts.forEach(text => {
            if (text.textContent.trim() && !text.querySelector('*')) {
                text.classList.add('export-text');
            }
        });

        // 备注
        const notes = content.querySelectorAll('.notes, .remarks, .comments');
        notes.forEach(note => note.classList.add('export-notes'));
    }

    /**
     * @function getIsolatedContainer - 获取隔离容器
     * @description 返回当前的隔离容器元素
     * @returns {HTMLElement|null} 隔离容器元素
     */
    getIsolatedContainer() {
        return this.isolatedContainer;
    }

    /**
     * @function cleanup - 清理资源
     * @description 移除隔离DOM和样式表
     * @returns {void}
     */
    cleanup() {
        try {
            // 移除隔离容器
            if (this.isolatedContainer && this.isolatedContainer.parentNode) {
                this.isolatedContainer.parentNode.removeChild(this.isolatedContainer);
            }

            // 重置状态
            this.isolatedContainer = null;
            this.originalContainer = null;
            this.isActive = false;

            this.debugManager.log('INFO', 'ExportDOMManager: 资源清理完成');

        } catch (error) {
            this.debugManager.log('ERROR', 'ExportDOMManager: 清理资源失败', error);
        }
    }

    /**
     * @function isReady - 检查是否准备就绪
     * @description 检查隔离DOM是否已创建并准备就绪
     * @returns {boolean} 是否准备就绪
     */
    isReady() {
        return this.isActive && this.isolatedContainer && this.isolatedContainer.parentNode;
    }

    /**
     * @function validateA4Size - 验证A4尺寸
     * @description 验证隔离容器是否为正确的A4尺寸
     * @returns {Object} 验证结果
     */
    validateA4Size() {
        if (!this.isolatedContainer) {
            return { valid: false, error: '隔离容器不存在' };
        }

        const rect = this.isolatedContainer.getBoundingClientRect();
        const widthMatch = Math.abs(rect.width - this.config.a4Width) < 5;
        const heightMatch = Math.abs(rect.height - this.config.a4Height) < 5;

        return {
            valid: widthMatch && heightMatch,
            actualSize: { width: rect.width, height: rect.height },
            expectedSize: { width: this.config.a4Width, height: this.config.a4Height },
            widthMatch: widthMatch,
            heightMatch: heightMatch
        };
    }
}

    /**
     * @function syncFormData - 同步表单数据
     * @description 将原始表单数据同步到隔离DOM中
     * @returns {void}
     */
    syncFormData() {
        try {
            if (!this.originalContainer || !this.isolatedContainer) {
                throw new Error('容器不存在，无法同步数据');
            }

            // 收集原始表单数据
            const formData = this.collectFormData();

            // 应用到隔离DOM
            this.applyFormDataToIsolated(formData);

            this.debugManager.log('INFO', 'ExportDOMManager: 表单数据同步完成');

        } catch (error) {
            this.debugManager.log('ERROR', 'ExportDOMManager: 同步表单数据失败', error);
        }
    }

    /**
     * @function collectFormData - 收集表单数据
     * @description 从原始DOM收集所有表单数据
     * @returns {Object} 表单数据对象
     */
    collectFormData() {
        const data = {};

        // 收集输入字段
        const inputs = this.originalContainer.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            if (input.id || input.name) {
                const key = input.id || input.name;
                data[key] = input.value;
            }
        });

        // 收集动态内容
        const dynamicElements = this.originalContainer.querySelectorAll('[data-dynamic]');
        dynamicElements.forEach(element => {
            const key = element.getAttribute('data-dynamic');
            data[key] = element.textContent || element.innerHTML;
        });

        return data;
    }

    /**
     * @function applyFormDataToIsolated - 应用表单数据到隔离DOM
     * @description 将收集的表单数据应用到隔离DOM中
     * @param {Object} formData - 表单数据
     * @returns {void}
     */
    applyFormDataToIsolated(formData) {
        Object.entries(formData).forEach(([key, value]) => {
            // 查找对应的元素
            let targetElement = this.isolatedContainer.querySelector(`#${key}`);
            if (!targetElement) {
                targetElement = this.isolatedContainer.querySelector(`[name="${key}"]`);
            }
            if (!targetElement) {
                targetElement = this.isolatedContainer.querySelector(`[data-dynamic="${key}"]`);
            }

            if (targetElement) {
                if (targetElement.tagName === 'INPUT' || targetElement.tagName === 'TEXTAREA') {
                    targetElement.value = value;
                } else {
                    targetElement.textContent = value;
                }
            }
        });
    }

    /**
     * @function optimizeForExport - 优化导出设置
     * @description 为导出进行最终优化设置
     * @returns {void}
     */
    optimizeForExport() {
        if (!this.isolatedContainer) return;

        // 强制设置容器样式
        this.isolatedContainer.style.cssText += `
            position: static !important;
            left: auto !important;
            top: auto !important;
            margin: 0 !important;
            padding: 0 !important;
            transform: none !important;
            scale: 1 !important;
            zoom: 1 !important;
            width: ${this.config.a4Width}px !important;
            height: ${this.config.a4Height}px !important;
            background: white !important;
            overflow: visible !important;
        `;

        // 强制重新计算样式
        this.isolatedContainer.offsetHeight;

        this.debugManager.log('INFO', 'ExportDOMManager: 导出优化完成');
    }
}

/**
 * @class ExportIsolationSystem
 * @description 导出隔离系统控制器 - 统一管理导出隔离流程
 */
class ExportIsolationSystem {
    constructor() {
        this.domManager = new ExportDOMManager();
        this.isInitialized = false;
        this.debugManager = window.DebugManager || console;
    }

    /**
     * @function initialize - 初始化隔离系统
     * @description 创建隔离DOM并准备导出环境
     * @returns {Promise<HTMLElement>} 隔离容器
     */
    async initialize() {
        try {
            this.debugManager.log('INFO', 'ExportIsolationSystem: 开始初始化');

            // 创建隔离DOM
            const isolatedContainer = await this.domManager.createIsolatedDOM();

            // 同步表单数据
            this.domManager.syncFormData();

            // 优化导出设置
            this.domManager.optimizeForExport();

            // 验证A4尺寸
            const validation = this.domManager.validateA4Size();
            if (!validation.valid) {
                this.debugManager.log('WARN', 'ExportIsolationSystem: A4尺寸验证失败', validation);
            }

            this.isInitialized = true;
            this.debugManager.log('SUCCESS', 'ExportIsolationSystem: 初始化完成');

            return isolatedContainer;

        } catch (error) {
            this.debugManager.log('ERROR', 'ExportIsolationSystem: 初始化失败', error);
            throw error;
        }
    }

    /**
     * @function getExportContainer - 获取导出容器
     * @description 返回准备好的导出容器
     * @returns {HTMLElement|null} 导出容器
     */
    getExportContainer() {
        return this.domManager.getIsolatedContainer();
    }

    /**
     * @function cleanup - 清理系统
     * @description 清理所有资源
     * @returns {void}
     */
    cleanup() {
        this.domManager.cleanup();
        this.isInitialized = false;
        this.debugManager.log('INFO', 'ExportIsolationSystem: 系统清理完成');
    }

    /**
     * @function isReady - 检查系统状态
     * @description 检查系统是否准备就绪
     * @returns {boolean} 是否准备就绪
     */
    isReady() {
        return this.isInitialized && this.domManager.isReady();
    }
}

// 导出类
window.ExportDOMManager = ExportDOMManager;
window.ExportIsolationSystem = ExportIsolationSystem;
