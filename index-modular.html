<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发票收据生成器 - 模块化版本</title>
    
    <!-- 外部依赖库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"
            onerror="console.warn('html2canvas CDN加载失败，PDF导出功能可能受限')"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"
            onerror="console.warn('jsPDF CDN加载失败，PDF导出功能可能受限')"></script>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/export.css">
    <link rel="stylesheet" href="styles/export-isolated.css">
    
    <!-- 核心模块 - 最高优先级 -->
    <script src="js/core/app-config.js" defer></script>
    <script src="js/core/dom-cache.js" defer></script>
    <script src="js/core/debug-manager.js" defer></script>
    
    <!-- 现有的图片管理模块 -->
    <script src="images-logo.js" defer onerror="console.warn('标志图片管理模块加载失败')"></script>
    <script src="images-header.js" defer onerror="console.warn('页眉图片管理模块加载失败')"></script>
    <script src="images-footer.js" defer onerror="console.warn('页脚图片管理模块加载失败')"></script>
    <script src="images-stamp.js" defer onerror="console.warn('印章图片管理模块加载失败')"></script>
    
    <!-- 现有的导出模块 -->
    <script src="export-dom-manager.js" defer onerror="console.warn('DOM重组导出管理器加载失败')"></script>
    <script src="export-components.js" defer onerror="console.warn('导出组件模块加载失败')"></script>
    
    <!-- 现有的预览模块 -->
    <script src="preview-module.js" defer></script>
    
    <!-- 应用初始化脚本 -->
    <script defer>
        // 等待所有模块加载完成后初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 开始初始化模块化应用...');
            
            // 检查核心模块是否加载
            const coreModules = {
                'AppConfig': typeof window.AppConfig !== 'undefined',
                'DOMCache': typeof window.DOMCache !== 'undefined',
                'DebugManager': typeof window.DebugManager !== 'undefined'
            };
            
            console.log('📦 核心模块加载状态:', coreModules);
            
            // 检查是否所有核心模块都已加载
            const allCoreLoaded = Object.values(coreModules).every(loaded => loaded);
            
            if (allCoreLoaded) {
                console.log('✅ 所有核心模块已加载，开始初始化应用');
                
                // 初始化调试管理器
                if (window.DebugManager) {
                    window.DebugManager.init({
                        enabled: window.AppConfig?.debugMode || false,
                        logLevel: window.AppConfig?.logLevel || 'INFO'
                    });
                }
                
                // 初始化DOM缓存
                if (window.DOMCache) {
                    window.DOMCache.initCache();
                }
                
                // 显示模块化状态
                displayModularizationStatus();
                
            } else {
                console.error('❌ 核心模块加载不完整:', coreModules);
            }
        });
        
        /**
         * 显示模块化状态
         */
        function displayModularizationStatus() {
            const statusContainer = document.getElementById('modularization-status');
            if (!statusContainer) return;
            
            const modules = {
                '核心配置': typeof window.AppConfig !== 'undefined',
                'DOM缓存': typeof window.DOMCache !== 'undefined',
                '调试管理': typeof window.DebugManager !== 'undefined',
                '图片管理': typeof window.ImageBase64 !== 'undefined',
                '导出系统': typeof window.ModernExportSystem !== 'undefined',
                '预览系统': typeof window.PreviewModule !== 'undefined'
            };
            
            let statusHTML = '<h3>模块化状态</h3><ul>';
            for (const [name, loaded] of Object.entries(modules)) {
                const status = loaded ? '✅ 已加载' : '❌ 未加载';
                statusHTML += `<li><strong>${name}</strong>: ${status}</li>`;
            }
            statusHTML += '</ul>';
            
            // 添加统计信息
            const loadedCount = Object.values(modules).filter(Boolean).length;
            const totalCount = Object.keys(modules).length;
            const loadRate = Math.round((loadedCount / totalCount) * 100);
            
            statusHTML += `<p><strong>加载率</strong>: ${loadedCount}/${totalCount} (${loadRate}%)</p>`;
            
            statusContainer.innerHTML = statusHTML;
        }
    </script>
</head>
<body>
    <div class="container">
        <header>
            <h1>发票收据生成器 - 模块化版本</h1>
            <p>演示模块化重构后的系统架构</p>
        </header>
        
        <main>
            <!-- 模块化状态显示 -->
            <section class="card">
                <div id="modularization-status">
                    <p>正在加载模块...</p>
                </div>
            </section>
            
            <!-- 原有的表单内容将在这里 -->
            <section class="card">
                <h2>表单区域</h2>
                <p>这里将包含原有的表单内容，现在使用模块化的JavaScript管理。</p>
                
                <!-- 基础表单元素用于测试DOM缓存 -->
                <div class="form-group">
                    <label for="document-type">文档类型:</label>
                    <select id="document-type">
                        <option value="receipt">收据</option>
                        <option value="invoice">发票</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="document-number">单据号码:</label>
                    <input type="text" id="document-number" placeholder="自动生成">
                </div>
                
                <div class="form-group">
                    <label for="customer-name">客户名称:</label>
                    <input type="text" id="customer-name" placeholder="请输入客户名称">
                </div>
                
                <button onclick="testModularSystem()">测试模块化系统</button>
            </section>
            
            <!-- 预览区域 -->
            <section class="card">
                <h2>预览区域</h2>
                <div id="document-preview">
                    <div id="document-container">
                        <p>预览内容将在这里显示</p>
                    </div>
                </div>
            </section>
        </main>
        
        <footer>
            <p>模块化重构演示版本 - 2024</p>
        </footer>
    </div>
    
    <script>
        /**
         * 测试模块化系统
         */
        function testModularSystem() {
            console.log('🧪 开始测试模块化系统...');
            
            // 测试配置模块
            if (window.AppConfig) {
                console.log('📋 当前配置:', {
                    公司: window.AppConfig.currentCompany,
                    货币: window.AppConfig.currentCurrency,
                    调试模式: window.AppConfig.debugMode
                });
            }
            
            // 测试DOM缓存
            if (window.DOMCache) {
                const stats = window.DOMCache.getCacheStats();
                console.log('🗄️ DOM缓存统计:', stats);
                
                // 测试获取元素
                const docType = window.DOMCache.get('documentType');
                console.log('📄 文档类型元素:', docType);
            }
            
            // 测试调试管理器
            if (window.DebugManager) {
                window.DebugManager.log('INFO', '模块化系统测试', {
                    测试时间: new Date().toISOString(),
                    浏览器: navigator.userAgent
                });
                
                const systemStatus = window.DebugManager.getSystemStatus();
                console.log('🔧 系统状态:', systemStatus);
            }
            
            alert('模块化系统测试完成，请查看控制台输出！');
        }
    </script>
</body>
</html>
