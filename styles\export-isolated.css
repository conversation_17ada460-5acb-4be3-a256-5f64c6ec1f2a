/**
 * @file 导出专用隔离样式表
 * @description 完全独立的导出模式样式，不依赖响应式缩放
 * @version 1.0
 * @date 2024-12-21
 * @purpose 彻底解决CSS缩放变换问题，确保导出时使用真正的A4尺寸
 */

/* #region CSS变量定义 - 导出专用 */
:root {
    /* A4尺寸 - 固定值，不受响应式影响 */
    --export-a4-width: 794px;
    --export-a4-height: 1123px;
    
    /* 图片尺寸标准 */
    --export-header-height: 130px;
    --export-footer-height: 110px;
    --export-stamp-size: 96px;
    
    /* 间距标准 */
    --export-content-padding: 30px;
    --export-content-margin: 20px;
    
    /* 层级标准 */
    --export-z-header: 10;
    --export-z-footer: 100;
    --export-z-stamp: 300;
    --export-z-total: 200;
    
    /* 字体标准 */
    --export-font-family: 'Roboto', 'Noto Sans SC', sans-serif;
    --export-font-size: 14px;
    --export-line-height: 1.5;
    
    /* 颜色标准 */
    --export-text-color: #333333;
    --export-primary-color: #1e40af;
    --export-border-color: #e5e7eb;
    --export-background-color: #ffffff;
}
/* #endregion */

/* #region 导出容器基础样式 */
.export-isolated-container {
    /* 固定A4尺寸 - 绝对不缩放 */
    width: var(--export-a4-width) !important;
    height: var(--export-a4-height) !important;
    min-width: var(--export-a4-width) !important;
    min-height: var(--export-a4-height) !important;
    max-width: var(--export-a4-width) !important;
    max-height: var(--export-a4-height) !important;
    
    /* 强制移除所有变换 */
    transform: none !important;
    scale: 1 !important;
    zoom: 1 !important;
    
    /* 布局设置 */
    position: relative !important;
    margin: 0 !important;
    padding: 0 !important;
    background: var(--export-background-color) !important;
    overflow: visible !important;
    
    /* 字体设置 */
    font-family: var(--export-font-family) !important;
    font-size: var(--export-font-size) !important;
    line-height: var(--export-line-height) !important;
    color: var(--export-text-color) !important;
    
    /* 盒模型 */
    box-sizing: border-box !important;
    display: block !important;
}

.export-isolated-content {
    position: relative !important;
    width: 100% !important;
    height: 100% !important;
    padding: var(--export-content-margin) var(--export-content-padding) calc(var(--export-footer-height) + 15px) !important;
    box-sizing: border-box !important;
    display: flex !important;
    flex-direction: column !important;
}
/* #endregion */

/* #region 页眉样式 - 导出专用 */
.export-header-container {
    position: relative !important;
    width: 100% !important;
    height: var(--export-header-height) !important;
    margin: 0 0 15px 0 !important;
    padding: 5px !important;
    background: var(--export-background-color) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    z-index: var(--export-z-header) !important;
}

.export-header-container img {
    width: calc(100% - 10px) !important;
    height: calc(var(--export-header-height) - 10px) !important;
    object-fit: contain !important;
    object-position: center !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: transparent !important;
}
/* #endregion */

/* #region 页脚样式 - 导出专用 */
.export-footer-container {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    height: var(--export-footer-height) !important;
    margin: 0 !important;
    padding: 5px !important;
    background: var(--export-background-color) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    z-index: var(--export-z-footer) !important;
}

.export-footer-container img {
    width: calc(100% - 10px) !important;
    height: calc(var(--export-footer-height) - 10px) !important;
    object-fit: contain !important;
    object-position: center !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: transparent !important;
}
/* #endregion */

/* #region 印章样式 - 导出专用 */
.export-stamp-container {
    position: absolute !important;
    bottom: 15% !important;
    right: calc(5% + 15px) !important;
    width: var(--export-stamp-size) !important;
    height: var(--export-stamp-size) !important;
    margin: 0 !important;
    padding: 0 !important;
    background: transparent !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    z-index: var(--export-z-stamp) !important;
    overflow: visible !important;
}

.export-stamp-container img {
    width: var(--export-stamp-size) !important;
    height: var(--export-stamp-size) !important;
    object-fit: contain !important;
    object-position: center !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: transparent !important;
    opacity: 0.9 !important;
    mix-blend-mode: multiply !important;
}
/* #endregion */

/* #region 表格样式 - 导出专用 */
.export-table {
    width: 100% !important;
    margin: 15px 0 !important;
    border-collapse: collapse !important;
    font-family: var(--export-font-family) !important;
    font-size: var(--export-font-size) !important;
    color: var(--export-text-color) !important;
    background: var(--export-background-color) !important;
}

.export-table th,
.export-table td {
    padding: 8px 12px !important;
    border: 1px solid var(--export-border-color) !important;
    text-align: left !important;
    vertical-align: middle !important;
    font-size: var(--export-font-size) !important;
    line-height: var(--export-line-height) !important;
}

.export-table th {
    background: #f8f9fa !important;
    font-weight: 600 !important;
    color: var(--export-text-color) !important;
}

.export-table .amount-column {
    text-align: right !important;
    font-weight: 500 !important;
}
/* #endregion */

/* #region 总金额容器 - 导出专用 */
.export-total-container {
    position: relative !important;
    display: inline-block !important;
    margin: 15px var(--export-content-padding) !important;
    padding: 12px 18px !important;
    background: var(--export-background-color) !important;
    border: 2px solid var(--export-primary-color) !important;
    border-radius: 6px !important;
    font-family: var(--export-font-family) !important;
    font-size: var(--export-font-size) !important;
    font-weight: 600 !important;
    color: var(--export-primary-color) !important;
    text-align: center !important;
    box-sizing: border-box !important;
    z-index: var(--export-z-total) !important;
    min-width: 200px !important;
}
/* #endregion */

/* #region 文本样式 - 导出专用 */
.export-title {
    font-family: var(--export-font-family) !important;
    font-size: 20px !important;
    font-weight: 700 !important;
    color: var(--export-text-color) !important;
    text-align: center !important;
    margin: 0 0 20px 0 !important;
    line-height: 1.3 !important;
}

.export-subtitle {
    font-family: var(--export-font-family) !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: var(--export-text-color) !important;
    margin: 15px 0 10px 0 !important;
    line-height: 1.4 !important;
}

.export-text {
    font-family: var(--export-font-family) !important;
    font-size: var(--export-font-size) !important;
    font-weight: 400 !important;
    color: var(--export-text-color) !important;
    line-height: var(--export-line-height) !important;
    margin: 5px 0 !important;
}

.export-notes {
    font-family: var(--export-font-family) !important;
    font-size: 12px !important;
    color: #666666 !important;
    line-height: 1.4 !important;
    margin: 15px 0 !important;
    padding: 10px !important;
    background: #f8f9fa !important;
    border-radius: 4px !important;
}
/* #endregion */

/* #region 强制覆盖所有可能的响应式规则 */
@media screen {
    .export-isolated-container,
    .export-isolated-container * {
        transform: none !important;
        scale: 1 !important;
        zoom: 1 !important;
    }
}

@media (max-width: 1024px) {
    .export-isolated-container,
    .export-isolated-container * {
        transform: none !important;
        scale: 1 !important;
        zoom: 1 !important;
    }
}

@media (orientation: landscape) {
    .export-isolated-container,
    .export-isolated-container * {
        transform: none !important;
        scale: 1 !important;
        zoom: 1 !important;
    }
}

@media (max-width: 768px) {
    .export-isolated-container,
    .export-isolated-container * {
        transform: none !important;
        scale: 1 !important;
        zoom: 1 !important;
    }
}
/* #endregion */
