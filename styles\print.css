/**
 * @file 打印样式文件
 * @description 打印专用样式，修复object-fit冲突，确保打印时图片保持正确比例
 * @version 5.0
 * @date 2024-12-21
 */

/* #region 打印专用样式 - Print2PDF方案 */
@media print {
    /* 隐藏不需要打印的元素 */
    .form-section,
    .preview-header,
    .btn,
    .btn-group,
    #export-method-selector,
    #export-method-info,
    .preview-status-indicator,
    .container > h1,
    .grid,
    .empty-preview-message,
    .preview-zoom-controls,
    .ai-fill-container {
        display: none !important;
    }

    /* 重置页面布局为打印优化 */
    body {
        margin: 0;
        padding: 0;
        background: white;
        font-size: 12pt;
        line-height: 1.4;
        color: black;
    }

    /* 预览容器打印样式 */
    .preview-section {
        display: block !important;
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        background: white !important;
    }

    #preview-container {
        display: block !important;
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
        transform: none !important;
        overflow: visible !important;
    }

    /* A4文档容器打印样式 */
    #document-preview {
        display: block !important;
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 auto !important;
        padding: 0 !important;
        transform: none !important;
        box-shadow: none !important;
        background: white !important;
        page-break-after: always;
    }

    #document-container {
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        /* 统一边距设置 - 与预览和导出模式保持一致，使用CSS变量确保对称 */
        padding-top: 20px !important;
        padding-bottom: 125px !important; /* 为页脚预留空间 */
        padding-left: var(--content-margin-left) !important;   /* 使用CSS变量，确保与其他模式一致 */
        padding-right: var(--content-margin-right) !important;  /* 使用CSS变量，确保与其他模式一致 */
        background: white !important;
        overflow: visible !important;
        box-sizing: border-box !important;
    }

    /* 隐藏空白图片占位符 */
    .image-placeholder,
    .header-placeholder,
    .footer-placeholder,
    .stamp-placeholder {
        display: none !important;
    }

    /* 确保图片正确显示 - 完全填满指定区域 */
    .document-header-image-container img {
        object-fit: cover !important; /* 页眉：完全填满130px区域，保持宽高比 */
        object-position: center !important;
    }

    .company-footer-image-container img,
    .unified-document-footer.company-footer-image-container img {
        object-fit: cover !important; /* 页脚：完全填满110px区域，保持宽高比 */
        object-position: center !important;
        
        /* 强制防止拉伸变形 - 关键修复 */
        min-width: 0 !important;
        min-height: 0 !important;
        flex-shrink: 0 !important;
        
        /* 打印时强制最高质量渲染 - 兼容多浏览器 */
        image-rendering: -webkit-optimize-contrast !important; /* Safari/Chrome/Edge */
        image-rendering: crisp-edges !important; /* Firefox/Chrome */
        image-rendering: high-quality !important; /* 通用 */
        
        /* 防止打印时图片模糊和压缩 */
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        -webkit-backface-visibility: hidden !important;
        backface-visibility: hidden !important;
        
        /* 确保无损打印 */
        -webkit-font-smoothing: antialiased !important;
        -moz-osx-font-smoothing: grayscale !important;
    }

    /* 印章图片单独处理 - 保持比例 */
    .company-stamp img {
        object-fit: contain !important; /* 印章也保持比例 */
        object-position: center !important;
        opacity: 0.9 !important;
        
        /* 防拉伸变形属性 */
        min-width: 0 !important;
        min-height: 0 !important;
        flex-shrink: 0 !important;
        
        /* 打印时强制最高质量渲染 */
        image-rendering: -webkit-optimize-contrast !important;
        image-rendering: crisp-edges !important;
        image-rendering: high-quality !important;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* 印章容器打印样式 - 统一使用CSS变量，完全透明背景 */
    .company-stamp {
        overflow: visible !important; /* 关键：防止印章被裁切 */
        position: absolute !important;
        bottom: var(--stamp-bottom-offset) !important;
        right: var(--stamp-right-offset) !important;
        z-index: var(--z-index-stamp) !important;
        width: 96px !important;
        height: 96px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        box-sizing: border-box !important;
        /* 强制透明背景，避免遮挡底层内容 */
        background: transparent !important;
        background-color: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    .company-stamp img {
        width: 100% !important;
        height: 100% !important;
        object-fit: contain !important;
        object-position: center !important;
        opacity: 0.75 !important; /* 调整为0.75，更好的透明效果 */
        /* 强制透明背景，去除白色背景遮挡 */
        background: transparent !important;
        background-color: transparent !important;
        border: none !important;
        box-shadow: none !important;
        /* 使用混合模式实现真正的透明叠加 */
        mix-blend-mode: multiply !important; /* 正片叠底模式，实现真实印章效果 */
        isolation: auto !important; /* 允许混合模式生效 */
    }

    /* 页眉图片打印专用 - 完全填满130px高度 */
    .document-header-image-container img {
        height: 100% !important; /* 填充130px容器高度 */
        width: 100% !important; /* 宽度100%配合cover填充 */
        object-fit: cover !important; /* 完全填满130px区域，保持宽高比 */
        object-position: center !important; /* 居中对齐 */
    }

    /* 页脚图片打印专用 - 完全填满110px高度 */
    .unified-document-footer.company-footer-image-container img {
        height: 100% !important; /* 完全填充110px高度的页脚容器 */
        width: 100% !important; /* 宽度100%配合cover填充 */
        object-fit: cover !important; /* 完全填满110px区域，保持宽高比 */
        object-position: center !important; /* 居中对齐 */
    }

    /* 总金额样式优化 */
    .total-amount-container {
        background: white !important;
        color: var(--primary-color) !important;
        border: 2px solid var(--primary-color) !important;
        padding: 12px 18px !important;
        margin: 15px 0 !important; /* 容器已有30px边距，避免重复设置 */
        border-radius: 6px !important;
        font-weight: bold !important;
        z-index: var(--z-index-total) !important;
        position: relative !important;
        text-align: center !important;
        display: inline-block !important;
        min-width: 200px !important;
    }

    .total-amount-container h3 {
        margin: 0 !important;
        font-size: 16px !important;
        font-weight: bold !important;
        line-height: 1.4 !important;
        color: var(--primary-color) !important;
        text-shadow: none !important;
        white-space: nowrap !important;
    }

    /* 项目表格打印样式 - 统一使用CSS变量 */
    .items-table {
        width: calc(100% - var(--content-margin-left) - var(--content-margin-right)) !important;
        margin-left: var(--content-margin-left) !important;
        margin-right: var(--content-margin-right) !important;
        border-collapse: collapse !important;
        margin-top: 10px !important;
        margin-bottom: 15px !important;
        font-size: var(--base-font-size) !important;
        table-layout: fixed !important;
    }

    .items-table th,
    .items-table td {
        border: 1px solid #333 !important;
        padding: 8px !important;
        text-align: left !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        vertical-align: top !important;
        line-height: 1.4 !important;
    }

    /* 数字列右对齐 - 与其他模式保持一致 */
    .items-table td:nth-child(2),
    .items-table td:nth-child(3),
    .items-table td:nth-child(4) {
        text-align: right !important;
    }

    .items-table th {
        background: #f5f5f5 !important;
        font-weight: bold !important;
    }

    /* 内容区域打印边距 - 容器已有30px边距，避免重复设置 */
    .company-info,
    .customer-info,
    .payment-info,
    .document-title,
    .notes-section {
        padding-left: 0 !important; /* 容器已有30px边距，避免重复 */
        padding-right: 0 !important; /* 容器已有30px边距，避免重复 */
        box-sizing: border-box !important;
    }

    .notes-section {
        margin-left: 0 !important; /* 容器已有30px边距，避免重复 */
        margin-right: 0 !important; /* 容器已有30px边距，避免重复 */
        width: 100% !important;
    }

    /* 分页控制 */
    .page-break {
        page-break-before: always;
    }

    .no-page-break {
        page-break-inside: avoid;
    }

    /* 页眉页脚打印样式 - 修正为110px高度 */
    .document-header,
    .document-header-image-container {
        position: relative !important;
        width: 100% !important;
        height: var(--header-height) !important;
        margin-bottom: 15px !important;
        text-align: center !important;
        background: white !important; /* 确保白色背景 */
        box-shadow: none !important; /* 移除阴影 */
        border: none !important; /* 移除边框 */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        overflow: hidden !important;
    }

    /* 打印时确保页眉容器没有灰色背景 */
    .document-header {
        background-color: white !important;
        background-image: none !important;
    }

    .document-footer,
    .unified-document-footer,
    .company-footer-image-container {
        position: absolute !important;
        bottom: 0 !important; /* 修正为底部边缘，与其他模式一致 */
        left: 0 !important;
        right: 0 !important;
        text-align: center !important;
        height: var(--footer-height) !important; /* 使用CSS变量 */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background: white !important;
        z-index: var(--z-index-footer) !important; /* 添加层级 */
    }

    /* 打印时隐藏调试元素 */
    #preview-container::before,
    #document-preview::after {
        display: none !important;
    }

    /* 打印颜色保持 */
    * {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* 打印字体优化 */
    body, * {
        font-family: 'Times New Roman', 'SimSun', serif !important;
    }

    /* 打印时禁用动画和过渡 */
    *, *::before, *::after {
        animation: none !important;
        transition: none !important;
    }
}
/* #endregion */
