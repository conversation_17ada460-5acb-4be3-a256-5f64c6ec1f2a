/**
 * @file 响应式样式文件
 * @description 移动端、平板设备、触摸设备优化
 * @version 5.0
 * @date 2024-12-21
 */

/* #region 平板设备 */
@media (max-width: 1024px) {
    .container {
        padding: 15px;
    }

    .grid {
        gap: 20px;
    }

    #document-preview {
        --preview-scale-factor: 0.65;
    }

    #preview-container {
        /* 重新计算平板设备的容器尺寸 */
        min-height: calc(var(--a4-height-px) * 0.65 + 80px);
        min-width: calc(var(--a4-width-px) * 0.65 + 60px);
    }

    /* 平板设备下的AI填充组件优化 */
    .ai-fill-container {
        padding: 10px;
        margin-bottom: 15px;
    }

    .ai-fill-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    /* 平板设备下的按钮组优化 */
    .btn-group {
        flex-wrap: wrap;
        gap: 8px;
    }

    .export-btn {
        min-width: 100px;
        font-size: 13px;
    }
}
/* #endregion */

/* #region 小屏幕设备 */
@media (max-width: 768px) {
    .container {
        padding: 8px;
    }

    .grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-section,
    .preview-section {
        padding: 15px;
        border-radius: 6px;
    }

    /* 表单组优化 - 垂直堆叠布局 */
    .form-group {
        margin-bottom: 16px;
    }

    .form-group label {
        font-size: 15px;
        margin-bottom: 6px;
        font-weight: 500;
        color: #374151;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px 14px;
        font-size: 16px; /* 防止iOS缩放 */
        border: 1.5px solid #d1d5db;
        border-radius: 8px;
        background-color: #ffffff;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        background-color: #ffffff;
    }

    /* 表单行优化 - 改为垂直堆叠 */
    .form-row {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    /* 按钮组优化 */
    .btn-group {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-top: 20px;
    }

    .btn {
        width: 100%;
        padding: 14px 20px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 8px;
        min-height: 48px; /* 增大触摸目标 */
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
    }

    /* 预览相关优化 */
    #document-preview {
        --preview-scale-factor: 0.5;
    }

    #preview-container {
        min-height: calc(var(--a4-height-px) * 0.5 + 80px);
        min-width: calc(var(--a4-width-px) * 0.5 + 60px);
        padding: 12px;
    }

    /* 表格优化 - 移动端友好 */
    .items-table {
        font-size: 14px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-top: 10px;
        margin-bottom: 14px;
    }

    .items-table th,
    .items-table td {
        padding: 10px 8px;
        border: 1px solid #e5e7eb;
        vertical-align: middle;
    }

    .items-table th {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        font-size: 12px;
        font-weight: 600;
        color: #374151;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        border-bottom: 2px solid #d1d5db;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .items-table tbody tr:hover {
        background-color: #f9fafb;
    }

    .items-table tbody tr:nth-child(even) {
        background-color: #fafbfc;
    }

    .items-table input {
        font-size: 14px;
        padding: 8px 6px;
        border: 1px solid transparent;
        background: transparent;
        min-height: 36px;
        border-radius: 4px;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .items-table input:hover {
        border-color: #d1d5db;
        background-color: rgba(255, 255, 255, 0.8);
    }

    .items-table input:focus {
        outline: none;
        border-color: #3b82f6;
        background: #ffffff;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        transform: scale(1.01);
    }

    .items-table input::placeholder {
        color: #9ca3af;
        font-size: 13px;
    }

    .items-table .item-amount {
        background-color: #f0fdf4;
        color: #059669;
        font-weight: 600;
        border-radius: 4px;
        padding: 6px;
        font-family: 'Courier New', monospace;
    }

    /* 小屏幕表格列宽调整 */
    .items-table th:nth-child(1),
    .items-table td:nth-child(1) {
        width: 60px; /* 订单列缩小 */
    }

    .items-table th:nth-child(2),
    .items-table td:nth-child(2) {
        min-width: 150px; /* 项目描述最小宽度 */
    }

    .items-table th:nth-child(3),
    .items-table td:nth-child(3) {
        width: 60px; /* 数量列 */
    }

    .items-table th:nth-child(4),
    .items-table td:nth-child(4) {
        width: 90px; /* 单价列 */
    }

    .items-table th:nth-child(5),
    .items-table td:nth-child(5) {
        width: 90px; /* 金额列 */
    }

    .items-table th:nth-child(6),
    .items-table td:nth-child(6) {
        width: 80px; /* 操作列 */
    }

    /* 删除按钮小屏幕优化 */
    .items-table .btn-danger {
        padding: 6px 10px;
        font-size: 11px;
        min-height: 30px;
        border-radius: 4px;
    }

    /* AI填充区域优化 */
    .ai-fill-container {
        padding: 12px;
        margin-bottom: 16px;
        border-radius: 8px;
        background: #f8fafc;
        border: 1px solid #e2e8f0;
    }

    .ai-fill-header {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .ai-fill-title {
        font-size: 14px;
        font-weight: 600;
        color: #475569;
    }

    #ai-text-input {
        font-size: 15px;
        min-height: 90px;
        padding: 12px;
        border-radius: 6px;
        resize: vertical;
    }

    .ai-image-input {
        padding: 12px;
        font-size: 15px;
        min-height: 48px;
        border-radius: 6px;
    }

    /* 多订单管理器优化 */
    .order-manager {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .order-tabs {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .order-tab {
        padding: 10px 16px;
        font-size: 14px;
        border-radius: 6px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .order-controls {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .order-controls select {
        padding: 10px;
        font-size: 15px;
        border-radius: 6px;
        min-height: 44px;
    }

    .current-order-info {
        padding: 12px;
        border-radius: 6px;
        background: #ffffff;
        border: 1px solid #e5e7eb;
    }

    .current-order-info span {
        display: block;
        margin-bottom: 6px;
        font-size: 14px;
    }

    /* 预览控制优化 */
    .preview-controls {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .export-method-selector {
        margin: 0;
        padding: 10px;
        font-size: 15px;
        border-radius: 6px;
        min-height: 44px;
    }

    /* 总金额显示优化 */
    .total-amount-display {
        font-size: 18px;
        padding: 16px;
        border-radius: 8px;
        margin-top: 12px;
    }

    /* 备注区域优化 */
    .form-group textarea {
        min-height: 100px;
        resize: vertical;
    }

    /* 标题优化 */
    .section-title {
        font-size: 18px;
        margin-bottom: 16px;
        color: #1f2937;
    }
}
/* #endregion */

/* #region 移动设备 */
@media (max-width: 480px) {
    .container {
        padding: 6px;
    }

    .form-section,
    .preview-section {
        padding: 12px;
        margin-bottom: 12px;
    }

    /* 表单组进一步优化 */
    .form-group {
        margin-bottom: 14px;
    }

    .form-group label {
        font-size: 14px;
        margin-bottom: 5px;
        font-weight: 600;
        color: #374151;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 14px 12px;
        border: 1.5px solid #d1d5db;
        border-radius: 8px;
        min-height: 48px;
        box-sizing: border-box;
    }

    .form-group textarea {
        min-height: 96px;
        resize: vertical;
    }

    /* 按钮进一步优化 */
    .btn {
        font-size: 16px;
        padding: 14px 16px;
        min-height: 50px;
        border-radius: 8px;
        font-weight: 600;
    }

    .btn-sm {
        font-size: 14px;
        padding: 10px 14px;
        min-height: 42px;
    }

    /* 预览相关 */
    #document-preview {
        --preview-scale-factor: 0.4;
    }

    #preview-container {
        min-height: calc(var(--a4-height-px) * 0.4 + 80px);
        min-width: calc(var(--a4-width-px) * 0.4 + 60px);
        padding: 8px;
    }

    /* 标题优化 */
    h1 {
        font-size: 22px;
        margin-bottom: 20px;
    }

    h2 {
        font-size: 18px;
        margin-bottom: 14px;
    }

    .section-title {
        font-size: 16px;
        margin-bottom: 14px;
    }

    /* 表格进一步优化 - 移动设备 */
    .items-table {
        font-size: 13px;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-top: 8px;
        margin-bottom: 12px;
    }

    .items-table th,
    .items-table td {
        padding: 8px 4px;
        border: 1px solid #e5e7eb;
        vertical-align: middle;
    }

    .items-table th {
        font-size: 11px;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        color: #374151;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.2px;
        border-bottom: 2px solid #d1d5db;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .items-table tbody tr:hover {
        background-color: #f9fafb;
    }

    .items-table input {
        font-size: 14px;
        padding: 6px 4px;
        min-height: 32px;
        border: 1px solid transparent;
        border-radius: 4px;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .items-table input:hover {
        border-color: #d1d5db;
        background-color: rgba(255, 255, 255, 0.9);
    }

    .items-table input:focus {
        outline: none;
        border-color: #3b82f6;
        background: #ffffff;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }

    .items-table input::placeholder {
        color: #9ca3af;
        font-size: 12px;
    }

    .items-table .item-amount {
        background-color: #f0fdf4;
        color: #059669;
        font-weight: 600;
        border-radius: 3px;
        padding: 4px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
    }

    /* 移动设备表格列宽进一步调整 */
    .items-table th:nth-child(1),
    .items-table td:nth-child(1) {
        width: 50px; /* 订单列更小 */
        font-size: 11px;
    }

    .items-table th:nth-child(2),
    .items-table td:nth-child(2) {
        min-width: 120px; /* 项目描述紧凑 */
    }

    .items-table th:nth-child(3),
    .items-table td:nth-child(3) {
        width: 50px; /* 数量列 */
    }

    .items-table th:nth-child(4),
    .items-table td:nth-child(4) {
        width: 70px; /* 单价列 */
    }

    .items-table th:nth-child(5),
    .items-table td:nth-child(5) {
        width: 70px; /* 金额列 */
    }

    .items-table th:nth-child(6),
    .items-table td:nth-child(6) {
        width: 60px; /* 操作列 */
    }

    /* 删除按钮移动设备优化 */
    .items-table .btn-danger {
        padding: 4px 8px;
        font-size: 10px;
        min-height: 28px;
        border-radius: 3px;
        white-space: nowrap;
    }

    /* 数字输入框移动端优化 */
    .items-table input[type="number"] {
        text-align: right;
        font-family: 'Courier New', monospace;
        font-size: 13px;
    }

    .items-table .item-quantity {
        text-align: center;
        font-size: 13px;
    }

    /* AI填充区域进一步优化 */
    .ai-fill-container {
        padding: 10px;
        margin-bottom: 14px;
        border-radius: 6px;
    }

    .ai-fill-title {
        font-size: 13px;
    }

    #ai-text-input {
        font-size: 15px;
        min-height: 80px;
        padding: 10px;
    }

    .ai-image-input {
        padding: 10px;
        font-size: 15px;
        min-height: 44px;
    }

    /* 多订单管理器进一步优化 */
    .order-tab {
        padding: 8px 12px;
        font-size: 13px;
        min-height: 40px;
    }

    .order-controls select {
        padding: 8px;
        font-size: 14px;
        min-height: 40px;
    }

    .current-order-info {
        padding: 10px;
        font-size: 13px;
    }

    .current-order-info span {
        margin-bottom: 4px;
    }

    /* 总金额显示优化 */
    .total-amount-display {
        font-size: 16px;
        padding: 14px;
        border-radius: 6px;
    }

    .total-amount-container {
        min-width: 140px;
        padding: 10px 14px;
        font-size: 14px;
    }

    .total-amount-container h3 {
        font-size: 13px;
    }

    /* 预览控制优化 */
    .export-method-selector {
        padding: 8px;
        font-size: 14px;
        min-height: 40px;
    }

    .export-btn {
        min-width: 100px;
        font-size: 14px;
    }

    /* 移动设备下的内容边距调整 */
    :root {
        --content-margin-left: 8px;
        --content-margin-right: 8px;
    }

    /* 移动设备下的预览容器标识 */
    #preview-container::before {
        font-size: 9px;
        padding: 1px 5px;
    }

    /* 表单行在移动设备上的间距优化 */
    .form-row {
        gap: 12px;
    }

    /* 按钮组间距优化 */
    .btn-group {
        gap: 10px;
        margin-top: 16px;
    }
}
/* #endregion */

/* #region 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    /* 按钮触摸优化 */
    .btn {
        min-height: 48px;
        font-size: 16px;
        padding: 14px 20px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.2s ease;
    }

    .btn-sm {
        min-height: 44px;
        font-size: 14px;
        padding: 12px 16px;
    }

    /* 表单控件触摸优化 */
    input, select, textarea {
        min-height: 48px;
        font-size: 16px;
        padding: 14px 12px;
        border-radius: 8px;
        border: 1.5px solid #d1d5db;
        transition: all 0.2s ease;
    }

    input:focus, select:focus, textarea:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* 表格输入框触摸优化 */
    .items-table input {
        min-height: 44px;
        padding: 12px 10px;
        font-size: 16px; /* 防止iOS缩放 */
        border: 1px solid transparent;
        border-radius: 6px;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .items-table input:hover {
        border-color: #d1d5db;
        background-color: rgba(255, 255, 255, 0.9);
    }

    .items-table input:focus {
        outline: none;
        border-color: #3b82f6;
        background: #ffffff;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        transform: scale(1.02);
    }

    .items-table input::placeholder {
        color: #9ca3af;
        font-size: 14px;
    }

    /* 按钮组触摸优化 */
    .btn-group {
        gap: 14px;
        margin-top: 20px;
    }

    /* 表格触摸优化 */
    .items-table th,
    .items-table td {
        padding: 14px 12px;
        border: 1px solid #e5e7eb;
        vertical-align: middle;
    }

    .items-table th {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        font-weight: 600;
        font-size: 13px;
        color: #374151;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        border-bottom: 2px solid #d1d5db;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .items-table tbody tr:hover {
        background-color: #f9fafb;
    }

    .items-table .item-amount {
        background-color: #f0fdf4;
        color: #059669;
        font-weight: 600;
        border-radius: 4px;
        padding: 8px;
        font-family: 'Courier New', monospace;
    }

    /* 触摸设备删除按钮优化 */
    .items-table .btn-danger {
        min-height: 44px;
        padding: 12px 16px;
        font-size: 14px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .items-table .btn-danger:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
    }

    .items-table .btn-danger:active {
        transform: scale(0.98);
    }

    /* 触摸设备数字输入框优化 */
    .items-table input[type="number"] {
        text-align: right;
        font-family: 'Courier New', monospace;
        font-size: 16px;
    }

    .items-table .item-quantity {
        text-align: center;
        font-size: 16px;
    }

    /* AI填充区域触摸优化 */
    .ai-image-input {
        min-height: 48px;
        padding: 14px 12px;
        font-size: 16px;
        border-radius: 8px;
    }

    #ai-text-input {
        min-height: 100px;
        padding: 14px 12px;
        font-size: 16px;
        border-radius: 8px;
        resize: vertical;
    }

    /* 订单标签触摸优化 */
    .order-tab {
        min-height: 48px;
        padding: 14px 18px;
        font-size: 15px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .order-tab:hover {
        background-color: #f3f4f6;
    }

    .order-tab.active {
        background-color: #3b82f6;
        color: white;
    }

    /* 选择框触摸优化 */
    select {
        min-height: 48px;
        padding: 14px 12px;
        font-size: 16px;
        border-radius: 8px;
        background-color: #ffffff;
        cursor: pointer;
    }

    /* 预览缩放控制触摸优化 */
    .zoom-btn {
        width: 48px;
        height: 48px;
        font-size: 18px;
        border-radius: 8px;
        border: 1.5px solid #d1d5db;
        background: #ffffff;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .zoom-btn:hover {
        background-color: #f9fafb;
        border-color: #9ca3af;
    }

    /* 导出按钮触摸优化 */
    .export-btn {
        min-height: 48px;
        min-width: 120px;
        font-size: 15px;
        font-weight: 600;
        border-radius: 8px;
    }

    /* 标签和文本触摸优化 */
    label {
        font-size: 15px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;
        display: block;
    }

    /* 总金额显示触摸优化 */
    .total-amount-display {
        padding: 16px;
        font-size: 18px;
        border-radius: 8px;
        margin-top: 14px;
    }

    /* 多订单控制触摸优化 */
    .order-controls select {
        min-height: 48px;
        padding: 14px 12px;
        font-size: 15px;
        border-radius: 8px;
    }

    .current-order-info {
        padding: 14px;
        border-radius: 8px;
        background: #ffffff;
        border: 1px solid #e5e7eb;
    }

    /* AI状态显示触摸优化 */
    .ai-status {
        padding: 12px 16px;
        border-radius: 8px;
        font-size: 14px;
        margin-top: 12px;
    }
}
/* #endregion */

/* #region 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* 高分辨率屏幕下的图片优化 */
    img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }

    /* 高分辨率屏幕下的字体优化 */
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}
/* #endregion */

/* #region 横屏模式优化 */
@media (orientation: landscape) and (max-width: 1024px) {
    .grid {
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    #document-preview {
        --preview-scale-factor: 0.6;
    }

    #preview-container {
        min-height: calc(var(--a4-height-px) * 0.6 + 60px);
        min-width: calc(var(--a4-width-px) * 0.6 + 40px);
    }
}
/* #endregion */

/* #region 竖屏模式优化 */
@media (orientation: portrait) and (max-width: 768px) {
    .grid {
        grid-template-columns: 1fr;
    }

    .preview-section {
        order: -1; /* 在移动设备竖屏模式下，预览区域显示在表单上方 */
    }

    /* 竖屏模式下的表单进一步优化 */
    .form-section {
        padding: 16px;
    }

    .form-group {
        margin-bottom: 18px;
    }

    /* 竖屏模式下的按钮优化 */
    .btn-group {
        flex-direction: column;
        gap: 12px;
    }

    .btn {
        width: 100%;
        min-height: 50px;
        font-size: 16px;
    }
}
/* #endregion */

/* #region 移动端表单专项优化 */
@media (max-width: 768px) {
    /* 防止表单元素在移动设备上的常见问题 */

    /* 防止输入框在iOS上的缩放 */
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    input[type="number"],
    input[type="date"],
    select,
    textarea {
        font-size: 16px !important;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    /* 移动端输入框样式统一 */
    input, select, textarea {
        background-color: #ffffff;
        border: 1.5px solid #d1d5db;
        border-radius: 8px;
        padding: 14px 12px;
        width: 100%;
        box-sizing: border-box;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }

    /* 焦点状态优化 */
    input:focus, select:focus, textarea:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        outline: none;
    }

    /* 选择框箭头自定义 */
    select {
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 12px center;
        background-size: 16px;
        padding-right: 40px;
    }

    /* 表单标签优化 */
    .form-group label {
        font-size: 15px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;
        display: block;
        line-height: 1.4;
    }

    /* 表单验证状态 */
    input:invalid {
        border-color: #ef4444;
    }

    input:valid {
        border-color: #10b981;
    }

    /* 占位符文本优化 */
    ::placeholder {
        color: #9ca3af;
        opacity: 1;
        font-size: 15px;
    }

    /* 移动端表格滚动优化 */
    .items-table {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        background: white;
    }

    /* 移动端表格容器优化 */
    .items-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        border-radius: 8px;
        margin: 12px 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* 移动端表格头部固定 */
    .items-table thead {
        position: sticky;
        top: 0;
        z-index: 20;
    }

    /* 移动端表格行交互优化 */
    .items-table tbody tr {
        transition: all 0.2s ease;
    }

    .items-table tbody tr:active {
        background-color: #e5e7eb;
        transform: scale(0.99);
    }

    /* 移动端表格输入框特殊处理 */
    .items-table input[type="text"] {
        font-size: 16px !important; /* 防止iOS缩放 */
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    .items-table input[type="number"] {
        font-size: 16px !important; /* 防止iOS缩放 */
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        text-align: right;
    }

    /* 移动端表格按钮优化 */
    .items-table .btn {
        min-height: 44px;
        font-size: 14px;
        padding: 10px 12px;
        border-radius: 6px;
        white-space: nowrap;
    }

    /* 移动端表格响应式字体 */
    .items-table {
        font-size: clamp(12px, 3vw, 14px);
    }

    .items-table th {
        font-size: clamp(10px, 2.5vw, 12px);
    }

    .items-table input {
        font-size: clamp(14px, 3.5vw, 16px);
    }

    /* 移动端按钮状态优化 */
    .btn:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    /* 移动端AI填充面板优化 */
    .ai-fill-container {
        border-radius: 8px;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 1px solid #e2e8f0;
        padding: 14px;
    }

    /* 移动端多订单管理优化 */
    .multi-order-container {
        border-radius: 8px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        padding: 14px;
        margin-bottom: 18px;
    }

    /* 移动端总金额容器优化 */
    .total-amount-container {
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
}
/* #endregion */

/* #region 可访问性优化 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation: none !important;
        transition: none !important;
    }

    .preview-refreshing {
        animation: none !important;
    }

    .loading {
        animation: none !important;
    }
}

@media (prefers-color-scheme: dark) {
    /* 暗色模式下保持文档预览为白色背景 */
    #document-preview,
    #document-container {
        background: white !important;
        color: black !important;
    }
}
/* #endregion */

/* #region 打印设备响应式 */
@media print and (max-width: 210mm) {
    #document-container {
        padding: 10mm 15mm !important;
    }

    .items-table {
        font-size: 10pt !important;
    }

    .total-amount-container {
        font-size: 12pt !important;
        padding: 6px 10px !important;
    }
}
/* #endregion */
