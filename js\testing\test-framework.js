/**
 * 轻量级测试框架
 * @file test-framework.js - 简单的JavaScript测试框架
 * @description 为模块化代码提供单元测试和集成测试功能
 */

/**
 * 测试框架
 * @namespace TestFramework - 轻量级测试框架
 */
const TestFramework = {
    // 测试配置
    config: {
        timeout: 5000,          // 默认超时时间
        autoRun: false,         // 是否自动运行测试
        stopOnFailure: false,   // 失败时是否停止
        verbose: true           // 详细输出
    },
    
    // 测试数据
    data: {
        suites: [],             // 测试套件
        results: [],            // 测试结果
        stats: {
            total: 0,
            passed: 0,
            failed: 0,
            skipped: 0,
            duration: 0
        }
    },
    
    // 当前测试上下文
    currentSuite: null,
    currentTest: null,
    
    /**
     * 创建测试套件
     * @function describe - 创建测试套件
     * @param {string} name - 套件名称
     * @param {Function} fn - 套件函数
     */
    describe(name, fn) {
        const suite = {
            name,
            tests: [],
            beforeEach: null,
            afterEach: null,
            beforeAll: null,
            afterAll: null,
            startTime: null,
            endTime: null
        };
        
        this.data.suites.push(suite);
        this.currentSuite = suite;
        
        try {
            fn();
        } catch (error) {
            console.error(`测试套件 "${name}" 初始化失败:`, error);
        }
        
        this.currentSuite = null;
    },
    
    /**
     * 创建测试用例
     * @function it - 创建测试用例
     * @param {string} name - 测试名称
     * @param {Function} fn - 测试函数
     */
    it(name, fn) {
        if (!this.currentSuite) {
            throw new Error('测试用例必须在describe块内定义');
        }
        
        const test = {
            name,
            fn,
            status: 'pending',
            error: null,
            duration: 0,
            startTime: null,
            endTime: null
        };
        
        this.currentSuite.tests.push(test);
    },
    
    /**
     * 设置每个测试前的钩子
     * @function beforeEach - 每个测试前执行
     * @param {Function} fn - 钩子函数
     */
    beforeEach(fn) {
        if (this.currentSuite) {
            this.currentSuite.beforeEach = fn;
        }
    },
    
    /**
     * 设置每个测试后的钩子
     * @function afterEach - 每个测试后执行
     * @param {Function} fn - 钩子函数
     */
    afterEach(fn) {
        if (this.currentSuite) {
            this.currentSuite.afterEach = fn;
        }
    },
    
    /**
     * 设置套件开始前的钩子
     * @function beforeAll - 套件开始前执行
     * @param {Function} fn - 钩子函数
     */
    beforeAll(fn) {
        if (this.currentSuite) {
            this.currentSuite.beforeAll = fn;
        }
    },
    
    /**
     * 设置套件结束后的钩子
     * @function afterAll - 套件结束后执行
     * @param {Function} fn - 钩子函数
     */
    afterAll(fn) {
        if (this.currentSuite) {
            this.currentSuite.afterAll = fn;
        }
    },
    
    /**
     * 运行所有测试
     * @function runTests - 运行所有测试套件
     * @returns {Promise<Object>} 测试结果
     */
    async runTests() {
        console.log('🧪 开始运行测试...');
        const startTime = performance.now();
        
        // 重置统计
        this.data.stats = {
            total: 0,
            passed: 0,
            failed: 0,
            skipped: 0,
            duration: 0
        };
        
        this.data.results = [];
        
        // 运行每个测试套件
        for (const suite of this.data.suites) {
            await this.runSuite(suite);
        }
        
        const endTime = performance.now();
        this.data.stats.duration = endTime - startTime;
        
        // 输出结果
        this.printResults();
        
        return this.data.stats;
    },
    
    /**
     * 运行测试套件
     * @function runSuite - 运行单个测试套件
     * @param {Object} suite - 测试套件
     */
    async runSuite(suite) {
        console.log(`\n📦 运行测试套件: ${suite.name}`);
        suite.startTime = performance.now();
        
        try {
            // 运行beforeAll钩子
            if (suite.beforeAll) {
                await suite.beforeAll();
            }
            
            // 运行每个测试
            for (const test of suite.tests) {
                await this.runTest(suite, test);
                
                if (this.config.stopOnFailure && test.status === 'failed') {
                    break;
                }
            }
            
            // 运行afterAll钩子
            if (suite.afterAll) {
                await suite.afterAll();
            }
            
        } catch (error) {
            console.error(`测试套件 "${suite.name}" 执行失败:`, error);
        }
        
        suite.endTime = performance.now();
    },
    
    /**
     * 运行单个测试
     * @function runTest - 运行单个测试用例
     * @param {Object} suite - 所属套件
     * @param {Object} test - 测试用例
     */
    async runTest(suite, test) {
        this.currentTest = test;
        test.startTime = performance.now();
        
        try {
            // 运行beforeEach钩子
            if (suite.beforeEach) {
                await suite.beforeEach();
            }
            
            // 运行测试
            await Promise.race([
                test.fn(),
                this.createTimeout(this.config.timeout)
            ]);
            
            test.status = 'passed';
            this.data.stats.passed++;
            
            if (this.config.verbose) {
                console.log(`  ✅ ${test.name}`);
            }
            
        } catch (error) {
            test.status = 'failed';
            test.error = error;
            this.data.stats.failed++;
            
            console.error(`  ❌ ${test.name}`);
            console.error(`     ${error.message}`);
            
        } finally {
            // 运行afterEach钩子
            if (suite.afterEach) {
                try {
                    await suite.afterEach();
                } catch (error) {
                    console.warn(`afterEach钩子执行失败:`, error);
                }
            }
            
            test.endTime = performance.now();
            test.duration = test.endTime - test.startTime;
            this.data.stats.total++;
            
            this.data.results.push({
                suite: suite.name,
                test: test.name,
                status: test.status,
                duration: test.duration,
                error: test.error
            });
        }
        
        this.currentTest = null;
    },
    
    /**
     * 创建超时Promise
     * @function createTimeout - 创建超时Promise
     * @param {number} ms - 超时时间
     * @returns {Promise} 超时Promise
     */
    createTimeout(ms) {
        return new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`测试超时 (${ms}ms)`));
            }, ms);
        });
    },
    
    /**
     * 打印测试结果
     * @function printResults - 打印测试结果摘要
     */
    printResults() {
        const { total, passed, failed, skipped, duration } = this.data.stats;
        
        console.log('\n📊 测试结果摘要:');
        console.log(`总计: ${total} 个测试`);
        console.log(`✅ 通过: ${passed} 个`);
        console.log(`❌ 失败: ${failed} 个`);
        console.log(`⏭️ 跳过: ${skipped} 个`);
        console.log(`⏱️ 耗时: ${duration.toFixed(2)}ms`);
        
        const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
        console.log(`📈 成功率: ${successRate}%`);
        
        if (failed > 0) {
            console.log('\n❌ 失败的测试:');
            this.data.results
                .filter(result => result.status === 'failed')
                .forEach(result => {
                    console.log(`  ${result.suite} > ${result.test}`);
                    if (result.error) {
                        console.log(`    ${result.error.message}`);
                    }
                });
        }
    },
    
    /**
     * 生成测试报告
     * @function generateReport - 生成详细的测试报告
     * @returns {Object} 测试报告
     */
    generateReport() {
        return {
            timestamp: new Date().toISOString(),
            stats: { ...this.data.stats },
            suites: this.data.suites.map(suite => ({
                name: suite.name,
                tests: suite.tests.map(test => ({
                    name: test.name,
                    status: test.status,
                    duration: test.duration,
                    error: test.error ? test.error.message : null
                }))
            })),
            results: [...this.data.results]
        };
    },
    
    /**
     * 导出测试报告
     * @function exportReport - 导出测试报告
     * @param {string} format - 导出格式
     */
    exportReport(format = 'json') {
        const report = this.generateReport();
        let content = '';
        let filename = '';
        let mimeType = '';
        
        switch (format.toLowerCase()) {
            case 'json':
                content = JSON.stringify(report, null, 2);
                filename = `test-report-${new Date().toISOString().split('T')[0]}.json`;
                mimeType = 'application/json';
                break;
                
            case 'html':
                content = this.generateHTMLReport(report);
                filename = `test-report-${new Date().toISOString().split('T')[0]}.html`;
                mimeType = 'text/html';
                break;
                
            default:
                console.warn('不支持的导出格式:', format);
                return;
        }
        
        // 创建下载
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        console.log(`📄 测试报告已导出: ${filename}`);
    },
    
    /**
     * 生成HTML格式报告
     * @function generateHTMLReport - 生成HTML测试报告
     * @param {Object} report - 测试报告数据
     * @returns {string} HTML内容
     */
    generateHTMLReport(report) {
        const successRate = report.stats.total > 0 
            ? ((report.stats.passed / report.stats.total) * 100).toFixed(1) 
            : 0;
            
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>测试报告</title>
                <meta charset="UTF-8">
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .summary { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 30px; }
                    .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; }
                    .stat { text-align: center; padding: 15px; background: white; border-radius: 5px; }
                    .stat-value { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
                    .passed { color: #28a745; }
                    .failed { color: #dc3545; }
                    .suite { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                    .test { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; }
                    .test.passed { border-left-color: #28a745; }
                    .test.failed { border-left-color: #dc3545; }
                    .error { color: #dc3545; font-size: 12px; margin-top: 5px; }
                </style>
            </head>
            <body>
                <h1>测试报告</h1>
                <div class="summary">
                    <h2>摘要</h2>
                    <div class="stats">
                        <div class="stat">
                            <div class="stat-value">${report.stats.total}</div>
                            <div>总测试数</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value passed">${report.stats.passed}</div>
                            <div>通过</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value failed">${report.stats.failed}</div>
                            <div>失败</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">${successRate}%</div>
                            <div>成功率</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">${report.stats.duration.toFixed(0)}ms</div>
                            <div>总耗时</div>
                        </div>
                    </div>
                </div>
                
                <h2>详细结果</h2>
                ${report.suites.map(suite => `
                    <div class="suite">
                        <h3>${suite.name}</h3>
                        ${suite.tests.map(test => `
                            <div class="test ${test.status}">
                                <strong>${test.name}</strong>
                                <span style="float: right;">${test.duration.toFixed(2)}ms</span>
                                ${test.error ? `<div class="error">错误: ${test.error}</div>` : ''}
                            </div>
                        `).join('')}
                    </div>
                `).join('')}
                
                <footer style="margin-top: 40px; text-align: center; color: #666;">
                    <p>报告生成时间: ${report.timestamp}</p>
                </footer>
            </body>
            </html>
        `;
    }
};

/**
 * 断言工具
 * @namespace Assert - 测试断言工具
 */
const Assert = {
    /**
     * 断言相等
     * @function equal - 断言两个值相等
     * @param {*} actual - 实际值
     * @param {*} expected - 期望值
     * @param {string} message - 错误消息
     */
    equal(actual, expected, message = '') {
        if (actual !== expected) {
            throw new Error(`断言失败: ${message}\n期望: ${expected}\n实际: ${actual}`);
        }
    },
    
    /**
     * 断言深度相等
     * @function deepEqual - 断言对象深度相等
     * @param {*} actual - 实际值
     * @param {*} expected - 期望值
     * @param {string} message - 错误消息
     */
    deepEqual(actual, expected, message = '') {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
            throw new Error(`深度断言失败: ${message}\n期望: ${JSON.stringify(expected)}\n实际: ${JSON.stringify(actual)}`);
        }
    },
    
    /**
     * 断言为真
     * @function ok - 断言值为真
     * @param {*} value - 要检查的值
     * @param {string} message - 错误消息
     */
    ok(value, message = '') {
        if (!value) {
            throw new Error(`断言失败: ${message}\n期望真值，但得到: ${value}`);
        }
    },
    
    /**
     * 断言抛出异常
     * @function throws - 断言函数抛出异常
     * @param {Function} fn - 要测试的函数
     * @param {string} message - 错误消息
     */
    throws(fn, message = '') {
        try {
            fn();
            throw new Error(`断言失败: ${message}\n期望抛出异常，但函数正常执行`);
        } catch (error) {
            // 期望的异常
        }
    },
    
    /**
     * 断言类型
     * @function type - 断言值的类型
     * @param {*} value - 要检查的值
     * @param {string} expectedType - 期望的类型
     * @param {string} message - 错误消息
     */
    type(value, expectedType, message = '') {
        const actualType = typeof value;
        if (actualType !== expectedType) {
            throw new Error(`类型断言失败: ${message}\n期望类型: ${expectedType}\n实际类型: ${actualType}`);
        }
    },
    
    /**
     * 断言包含
     * @function includes - 断言数组或字符串包含某个值
     * @param {Array|string} container - 容器
     * @param {*} value - 要查找的值
     * @param {string} message - 错误消息
     */
    includes(container, value, message = '') {
        if (!container.includes(value)) {
            throw new Error(`包含断言失败: ${message}\n容器中未找到: ${value}`);
        }
    }
};

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.TestFramework = TestFramework;
    window.Assert = Assert;
    
    // 简化的全局函数
    window.describe = TestFramework.describe.bind(TestFramework);
    window.it = TestFramework.it.bind(TestFramework);
    window.beforeEach = TestFramework.beforeEach.bind(TestFramework);
    window.afterEach = TestFramework.afterEach.bind(TestFramework);
    window.beforeAll = TestFramework.beforeAll.bind(TestFramework);
    window.afterAll = TestFramework.afterAll.bind(TestFramework);
    
    console.log('✅ 测试框架已加载');
}

// ES6模块导出
export { TestFramework, Assert };
