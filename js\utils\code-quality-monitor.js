/**
 * 代码质量监控模块
 * @file code-quality-monitor.js - 自动化代码质量检查和监控
 * @description 提供实时的代码质量监控、性能分析和问题检测功能
 */

/**
 * 代码质量监控器
 * @namespace CodeQualityMonitor - 代码质量监控和分析系统
 */
const CodeQualityMonitor = {
    // 监控配置
    config: {
        enabled: true,
        autoCheck: true,
        checkInterval: 30000, // 30秒检查一次
        reportInterval: 300000, // 5分钟生成一次报告
        maxIssues: 100, // 最大问题数量
        severityLevels: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']
    },
    
    // 监控数据
    data: {
        issues: [],
        metrics: {},
        reports: [],
        lastCheck: null,
        checkCount: 0
    },
    
    // 定时器
    timers: {
        checker: null,
        reporter: null
    },
    
    /**
     * 初始化代码质量监控
     * @function init - 初始化监控系统
     * @param {Object} options - 配置选项
     */
    init(options = {}) {
        // 合并配置
        Object.assign(this.config, options);
        
        if (this.config.enabled) {
            this.startMonitoring();
            console.log('✅ 代码质量监控已启动');
        }
    },
    
    /**
     * 开始监控
     * @function startMonitoring - 开始自动监控
     */
    startMonitoring() {
        // 立即执行一次检查
        this.runQualityCheck();
        
        // 设置定期检查
        if (this.config.autoCheck) {
            this.timers.checker = setInterval(() => {
                this.runQualityCheck();
            }, this.config.checkInterval);
        }
        
        // 设置定期报告
        this.timers.reporter = setInterval(() => {
            this.generateReport();
        }, this.config.reportInterval);
    },
    
    /**
     * 停止监控
     * @function stopMonitoring - 停止自动监控
     */
    stopMonitoring() {
        if (this.timers.checker) {
            clearInterval(this.timers.checker);
            this.timers.checker = null;
        }
        
        if (this.timers.reporter) {
            clearInterval(this.timers.reporter);
            this.timers.reporter = null;
        }
        
        console.log('⏹️ 代码质量监控已停止');
    },
    
    /**
     * 运行质量检查
     * @function runQualityCheck - 执行完整的质量检查
     */
    runQualityCheck() {
        const startTime = performance.now();
        
        try {
            // 清理旧问题
            this.data.issues = [];
            
            // 执行各项检查
            this.checkPerformance();
            this.checkMemoryUsage();
            this.checkErrorRate();
            this.checkDOMHealth();
            this.checkModuleIntegrity();
            this.checkSecurityIssues();
            
            // 更新统计
            this.data.lastCheck = new Date().toISOString();
            this.data.checkCount++;
            
            const duration = performance.now() - startTime;
            this.addMetric('checkDuration', duration);
            
            console.log(`🔍 质量检查完成 (${duration.toFixed(2)}ms)`, {
                问题数量: this.data.issues.length,
                检查次数: this.data.checkCount
            });
            
        } catch (error) {
            this.addIssue('CRITICAL', 'SYSTEM', '质量检查执行失败', error.message);
            console.error('❌ 质量检查失败:', error);
        }
    },
    
    /**
     * 检查性能指标
     * @function checkPerformance - 检查应用性能
     */
    checkPerformance() {
        // 检查页面加载性能
        if (performance.timing) {
            const timing = performance.timing;
            const loadTime = timing.loadEventEnd - timing.navigationStart;
            
            if (loadTime > 5000) {
                this.addIssue('HIGH', 'PERFORMANCE', '页面加载时间过长', `${loadTime}ms`);
            }
            
            this.addMetric('pageLoadTime', loadTime);
        }
        
        // 检查导出性能
        if (window.PerformanceOptimizer && window.PerformanceOptimizer.metrics) {
            const exportMetrics = window.PerformanceOptimizer.metrics;
            
            if (exportMetrics.averageExportTime > 30000) {
                this.addIssue('MEDIUM', 'PERFORMANCE', '导出平均时间过长', 
                    `${exportMetrics.averageExportTime}ms`);
            }
        }
        
        // 检查内存泄漏
        this.checkMemoryLeaks();
    },
    
    /**
     * 检查内存使用情况
     * @function checkMemoryUsage - 检查内存使用
     */
    checkMemoryUsage() {
        if (performance.memory) {
            const memory = performance.memory;
            const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
            const totalMB = Math.round(memory.totalJSHeapSize / 1024 / 1024);
            const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
            
            const usagePercent = (usedMB / limitMB) * 100;
            
            if (usagePercent > 80) {
                this.addIssue('HIGH', 'MEMORY', '内存使用率过高', `${usagePercent.toFixed(1)}%`);
            } else if (usagePercent > 60) {
                this.addIssue('MEDIUM', 'MEMORY', '内存使用率较高', `${usagePercent.toFixed(1)}%`);
            }
            
            this.addMetric('memoryUsage', {
                used: usedMB,
                total: totalMB,
                limit: limitMB,
                percent: usagePercent
            });
        }
    },
    
    /**
     * 检查错误率
     * @function checkErrorRate - 检查应用错误率
     */
    checkErrorRate() {
        if (window.ErrorManager && window.ErrorManager.errors) {
            const errors = window.ErrorManager.errors;
            const recentErrors = errors.filter(error => {
                const errorTime = new Date(error.timestamp);
                const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
                return errorTime > fiveMinutesAgo;
            });
            
            if (recentErrors.length > 5) {
                this.addIssue('HIGH', 'ERROR', '错误率过高', `${recentErrors.length} 个错误/5分钟`);
            }
            
            this.addMetric('errorRate', {
                total: errors.length,
                recent: recentErrors.length
            });
        }
    },
    
    /**
     * 检查DOM健康状况
     * @function checkDOMHealth - 检查DOM状态
     */
    checkDOMHealth() {
        // 检查DOM元素数量
        const elementCount = document.querySelectorAll('*').length;
        if (elementCount > 5000) {
            this.addIssue('MEDIUM', 'DOM', 'DOM元素过多', `${elementCount} 个元素`);
        }
        
        // 检查DOM缓存有效性
        if (window.DOMCache) {
            const validation = window.DOMCache.validateCache();
            if (validation.invalid.length > 0) {
                this.addIssue('LOW', 'DOM', 'DOM缓存失效', 
                    `${validation.invalid.length} 个失效元素`);
            }
        }
        
        // 检查内存泄漏风险
        const listeners = this.countEventListeners();
        if (listeners > 100) {
            this.addIssue('MEDIUM', 'DOM', '事件监听器过多', `${listeners} 个监听器`);
        }
        
        this.addMetric('domHealth', {
            elementCount,
            eventListeners: listeners
        });
    },
    
    /**
     * 检查模块完整性
     * @function checkModuleIntegrity - 检查模块加载状态
     */
    checkModuleIntegrity() {
        const requiredModules = [
            'AppConfig', 'DOMCache', 'DebugManager', 'ModernExportSystem'
        ];
        
        const missingModules = requiredModules.filter(module => 
            typeof window[module] === 'undefined'
        );
        
        if (missingModules.length > 0) {
            this.addIssue('CRITICAL', 'MODULE', '核心模块缺失', 
                missingModules.join(', '));
        }
        
        this.addMetric('moduleIntegrity', {
            required: requiredModules.length,
            loaded: requiredModules.length - missingModules.length,
            missing: missingModules
        });
    },
    
    /**
     * 检查安全问题
     * @function checkSecurityIssues - 检查安全相关问题
     */
    checkSecurityIssues() {
        // 检查XSS风险
        const userInputs = document.querySelectorAll('input, textarea');
        userInputs.forEach(input => {
            if (input.value.includes('<script>')) {
                this.addIssue('CRITICAL', 'SECURITY', 'XSS风险检测', input.id || 'unknown');
            }
        });
        
        // 检查API密钥暴露
        if (window.AppConfig && window.AppConfig.geminiApiKey) {
            if (window.AppConfig.geminiApiKey.length > 0) {
                // 检查是否在控制台中暴露
                this.addIssue('LOW', 'SECURITY', 'API密钥已配置', '请确保密钥安全');
            }
        }
    },
    
    /**
     * 添加问题
     * @function addIssue - 添加质量问题
     * @param {string} severity - 严重程度
     * @param {string} category - 问题类别
     * @param {string} title - 问题标题
     * @param {string} details - 问题详情
     */
    addIssue(severity, category, title, details) {
        const issue = {
            id: `issue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            severity,
            category,
            title,
            details,
            timestamp: new Date().toISOString(),
            resolved: false
        };
        
        this.data.issues.push(issue);
        
        // 限制问题数量
        if (this.data.issues.length > this.config.maxIssues) {
            this.data.issues = this.data.issues.slice(-this.config.maxIssues);
        }
    },
    
    /**
     * 添加指标
     * @function addMetric - 添加性能指标
     * @param {string} name - 指标名称
     * @param {*} value - 指标值
     */
    addMetric(name, value) {
        if (!this.data.metrics[name]) {
            this.data.metrics[name] = [];
        }
        
        this.data.metrics[name].push({
            value,
            timestamp: new Date().toISOString()
        });
        
        // 保留最近100个数据点
        if (this.data.metrics[name].length > 100) {
            this.data.metrics[name] = this.data.metrics[name].slice(-100);
        }
    },
    
    /**
     * 生成质量报告
     * @function generateReport - 生成质量报告
     * @returns {Object} 质量报告
     */
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: this.generateSummary(),
            issues: this.categorizeIssues(),
            metrics: this.summarizeMetrics(),
            recommendations: this.generateRecommendations()
        };
        
        this.data.reports.push(report);
        
        // 保留最近10个报告
        if (this.data.reports.length > 10) {
            this.data.reports = this.data.reports.slice(-10);
        }
        
        console.log('📊 质量报告已生成:', report.summary);
        return report;
    },
    
    /**
     * 生成摘要
     * @function generateSummary - 生成质量摘要
     * @returns {Object} 质量摘要
     */
    generateSummary() {
        const totalIssues = this.data.issues.length;
        const criticalIssues = this.data.issues.filter(i => i.severity === 'CRITICAL').length;
        const highIssues = this.data.issues.filter(i => i.severity === 'HIGH').length;
        
        let healthScore = 100;
        healthScore -= criticalIssues * 20;
        healthScore -= highIssues * 10;
        healthScore -= (totalIssues - criticalIssues - highIssues) * 2;
        healthScore = Math.max(0, healthScore);
        
        return {
            healthScore,
            totalIssues,
            criticalIssues,
            highIssues,
            checkCount: this.data.checkCount,
            lastCheck: this.data.lastCheck
        };
    },
    
    /**
     * 分类问题
     * @function categorizeIssues - 按类别分组问题
     * @returns {Object} 分类后的问题
     */
    categorizeIssues() {
        const categories = {};
        
        this.data.issues.forEach(issue => {
            if (!categories[issue.category]) {
                categories[issue.category] = [];
            }
            categories[issue.category].push(issue);
        });
        
        return categories;
    },
    
    /**
     * 汇总指标
     * @function summarizeMetrics - 汇总性能指标
     * @returns {Object} 指标摘要
     */
    summarizeMetrics() {
        const summary = {};
        
        for (const [name, values] of Object.entries(this.data.metrics)) {
            if (values.length > 0) {
                const latest = values[values.length - 1].value;
                summary[name] = {
                    latest,
                    count: values.length,
                    trend: this.calculateTrend(values)
                };
            }
        }
        
        return summary;
    },
    
    /**
     * 计算趋势
     * @function calculateTrend - 计算指标趋势
     * @param {Array} values - 指标值数组
     * @returns {string} 趋势方向
     */
    calculateTrend(values) {
        if (values.length < 2) return 'stable';
        
        const recent = values.slice(-5);
        const older = values.slice(-10, -5);
        
        if (recent.length === 0 || older.length === 0) return 'stable';
        
        const recentAvg = recent.reduce((sum, v) => sum + (typeof v.value === 'number' ? v.value : 0), 0) / recent.length;
        const olderAvg = older.reduce((sum, v) => sum + (typeof v.value === 'number' ? v.value : 0), 0) / older.length;
        
        const change = ((recentAvg - olderAvg) / olderAvg) * 100;
        
        if (change > 10) return 'increasing';
        if (change < -10) return 'decreasing';
        return 'stable';
    },
    
    /**
     * 生成建议
     * @function generateRecommendations - 生成改进建议
     * @returns {Array} 建议列表
     */
    generateRecommendations() {
        const recommendations = [];
        
        const criticalIssues = this.data.issues.filter(i => i.severity === 'CRITICAL');
        if (criticalIssues.length > 0) {
            recommendations.push('立即处理关键问题，确保系统稳定性');
        }
        
        const memoryIssues = this.data.issues.filter(i => i.category === 'MEMORY');
        if (memoryIssues.length > 0) {
            recommendations.push('优化内存使用，考虑实施垃圾回收策略');
        }
        
        const performanceIssues = this.data.issues.filter(i => i.category === 'PERFORMANCE');
        if (performanceIssues.length > 0) {
            recommendations.push('优化性能瓶颈，提升用户体验');
        }
        
        return recommendations;
    },
    
    /**
     * 检查内存泄漏
     * @function checkMemoryLeaks - 检查潜在的内存泄漏
     */
    checkMemoryLeaks() {
        // 检查全局变量数量
        const globalVars = Object.keys(window).length;
        if (globalVars > 500) {
            this.addIssue('MEDIUM', 'MEMORY', '全局变量过多', `${globalVars} 个变量`);
        }
        
        // 检查定时器数量
        const timers = this.countActiveTimers();
        if (timers > 20) {
            this.addIssue('MEDIUM', 'MEMORY', '活跃定时器过多', `${timers} 个定时器`);
        }
    },
    
    /**
     * 统计事件监听器
     * @function countEventListeners - 统计事件监听器数量
     * @returns {number} 监听器数量估算
     */
    countEventListeners() {
        // 这是一个估算，实际数量可能不同
        const elementsWithListeners = document.querySelectorAll('[onclick], [onchange], [oninput]');
        return elementsWithListeners.length;
    },
    
    /**
     * 统计活跃定时器
     * @function countActiveTimers - 统计活跃定时器数量
     * @returns {number} 定时器数量估算
     */
    countActiveTimers() {
        // 这是一个估算，基于已知的定时器
        let count = 0;
        
        if (this.timers.checker) count++;
        if (this.timers.reporter) count++;
        
        // 检查其他模块的定时器
        if (window.DebounceManager && window.DebounceManager.timers) {
            count += window.DebounceManager.timers.size;
        }
        
        return count;
    },
    
    /**
     * 获取当前状态
     * @function getStatus - 获取监控状态
     * @returns {Object} 当前状态
     */
    getStatus() {
        return {
            enabled: this.config.enabled,
            autoCheck: this.config.autoCheck,
            lastCheck: this.data.lastCheck,
            checkCount: this.data.checkCount,
            issueCount: this.data.issues.length,
            reportCount: this.data.reports.length
        };
    },

    /**
     * 创建质量仪表板
     * @function createDashboard - 创建可视化仪表板
     * @param {string} containerId - 容器元素ID
     */
    createDashboard(containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.warn('仪表板容器不存在:', containerId);
            return;
        }

        const report = this.generateReport();

        container.innerHTML = `
            <div class="quality-dashboard">
                <h3>代码质量监控仪表板</h3>

                <div class="dashboard-summary">
                    <div class="metric-card health-score">
                        <h4>健康评分</h4>
                        <div class="score ${this.getScoreClass(report.summary.healthScore)}">
                            ${report.summary.healthScore}
                        </div>
                    </div>

                    <div class="metric-card">
                        <h4>总问题数</h4>
                        <div class="value">${report.summary.totalIssues}</div>
                    </div>

                    <div class="metric-card critical">
                        <h4>关键问题</h4>
                        <div class="value">${report.summary.criticalIssues}</div>
                    </div>

                    <div class="metric-card">
                        <h4>检查次数</h4>
                        <div class="value">${report.summary.checkCount}</div>
                    </div>
                </div>

                <div class="dashboard-details">
                    <div class="issues-section">
                        <h4>问题分类</h4>
                        ${this.renderIssuesByCategory(report.issues)}
                    </div>

                    <div class="metrics-section">
                        <h4>性能指标</h4>
                        ${this.renderMetrics(report.metrics)}
                    </div>

                    <div class="recommendations-section">
                        <h4>改进建议</h4>
                        ${this.renderRecommendations(report.recommendations)}
                    </div>
                </div>

                <div class="dashboard-actions">
                    <button onclick="CodeQualityMonitor.runQualityCheck()">立即检查</button>
                    <button onclick="CodeQualityMonitor.generateReport()">生成报告</button>
                    <button onclick="CodeQualityMonitor.exportReport()">导出报告</button>
                </div>
            </div>
        `;

        // 添加样式
        this.addDashboardStyles();
    },

    /**
     * 获取评分等级样式类
     * @function getScoreClass - 根据评分获取样式类
     * @param {number} score - 健康评分
     * @returns {string} 样式类名
     */
    getScoreClass(score) {
        if (score >= 90) return 'excellent';
        if (score >= 70) return 'good';
        if (score >= 50) return 'fair';
        return 'poor';
    },

    /**
     * 渲染问题分类
     * @function renderIssuesByCategory - 渲染按类别分组的问题
     * @param {Object} issues - 分类后的问题
     * @returns {string} HTML字符串
     */
    renderIssuesByCategory(issues) {
        let html = '<div class="issues-grid">';

        for (const [category, categoryIssues] of Object.entries(issues)) {
            html += `
                <div class="issue-category">
                    <h5>${category} (${categoryIssues.length})</h5>
                    <ul>
                        ${categoryIssues.slice(0, 3).map(issue => `
                            <li class="issue-item ${issue.severity.toLowerCase()}">
                                <span class="severity">${issue.severity}</span>
                                <span class="title">${issue.title}</span>
                            </li>
                        `).join('')}
                        ${categoryIssues.length > 3 ? `<li class="more">...还有 ${categoryIssues.length - 3} 个问题</li>` : ''}
                    </ul>
                </div>
            `;
        }

        html += '</div>';
        return html;
    },

    /**
     * 渲染性能指标
     * @function renderMetrics - 渲染性能指标
     * @param {Object} metrics - 指标摘要
     * @returns {string} HTML字符串
     */
    renderMetrics(metrics) {
        let html = '<div class="metrics-grid">';

        for (const [name, metric] of Object.entries(metrics)) {
            const trendIcon = this.getTrendIcon(metric.trend);
            html += `
                <div class="metric-item">
                    <div class="metric-name">${this.formatMetricName(name)}</div>
                    <div class="metric-value">${this.formatMetricValue(metric.latest)}</div>
                    <div class="metric-trend">${trendIcon} ${metric.trend}</div>
                </div>
            `;
        }

        html += '</div>';
        return html;
    },

    /**
     * 渲染改进建议
     * @function renderRecommendations - 渲染改进建议
     * @param {Array} recommendations - 建议列表
     * @returns {string} HTML字符串
     */
    renderRecommendations(recommendations) {
        if (recommendations.length === 0) {
            return '<p class="no-recommendations">暂无改进建议，系统运行良好！</p>';
        }

        return `
            <ul class="recommendations-list">
                ${recommendations.map(rec => `<li>${rec}</li>`).join('')}
            </ul>
        `;
    },

    /**
     * 获取趋势图标
     * @function getTrendIcon - 根据趋势获取图标
     * @param {string} trend - 趋势方向
     * @returns {string} 图标字符
     */
    getTrendIcon(trend) {
        switch (trend) {
            case 'increasing': return '📈';
            case 'decreasing': return '📉';
            default: return '➡️';
        }
    },

    /**
     * 格式化指标名称
     * @function formatMetricName - 格式化指标名称为中文
     * @param {string} name - 英文指标名
     * @returns {string} 中文指标名
     */
    formatMetricName(name) {
        const nameMap = {
            'checkDuration': '检查耗时',
            'pageLoadTime': '页面加载时间',
            'memoryUsage': '内存使用',
            'errorRate': '错误率',
            'domHealth': 'DOM健康度'
        };
        return nameMap[name] || name;
    },

    /**
     * 格式化指标值
     * @function formatMetricValue - 格式化指标值显示
     * @param {*} value - 指标值
     * @returns {string} 格式化后的值
     */
    formatMetricValue(value) {
        if (typeof value === 'number') {
            if (value > 1000) {
                return `${(value / 1000).toFixed(1)}k`;
            }
            return value.toFixed(1);
        }

        if (typeof value === 'object' && value !== null) {
            if (value.percent !== undefined) {
                return `${value.percent.toFixed(1)}%`;
            }
            return JSON.stringify(value);
        }

        return String(value);
    },

    /**
     * 添加仪表板样式
     * @function addDashboardStyles - 添加仪表板CSS样式
     */
    addDashboardStyles() {
        if (document.getElementById('quality-dashboard-styles')) return;

        const styles = `
            <style id="quality-dashboard-styles">
                .quality-dashboard {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    max-width: 1200px;
                    margin: 20px auto;
                    padding: 20px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }

                .dashboard-summary {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 20px;
                    margin-bottom: 30px;
                }

                .metric-card {
                    background: white;
                    padding: 20px;
                    border-radius: 6px;
                    text-align: center;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }

                .metric-card h4 {
                    margin: 0 0 10px 0;
                    color: #666;
                    font-size: 14px;
                    font-weight: 500;
                }

                .metric-card .value, .metric-card .score {
                    font-size: 32px;
                    font-weight: bold;
                    margin: 0;
                }

                .score.excellent { color: #28a745; }
                .score.good { color: #17a2b8; }
                .score.fair { color: #ffc107; }
                .score.poor { color: #dc3545; }

                .dashboard-details {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 30px;
                    margin-bottom: 30px;
                }

                .issues-grid, .metrics-grid {
                    display: grid;
                    gap: 15px;
                }

                .issue-category, .metric-item {
                    background: white;
                    padding: 15px;
                    border-radius: 6px;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }

                .issue-item {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    padding: 5px 0;
                    border-bottom: 1px solid #eee;
                }

                .issue-item:last-child {
                    border-bottom: none;
                }

                .severity {
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                    text-transform: uppercase;
                }

                .severity.critical { background: #dc3545; color: white; }
                .severity.high { background: #fd7e14; color: white; }
                .severity.medium { background: #ffc107; color: black; }
                .severity.low { background: #6c757d; color: white; }

                .dashboard-actions {
                    text-align: center;
                    gap: 10px;
                    display: flex;
                    justify-content: center;
                }

                .dashboard-actions button {
                    padding: 10px 20px;
                    border: none;
                    border-radius: 4px;
                    background: #007bff;
                    color: white;
                    cursor: pointer;
                    font-size: 14px;
                }

                .dashboard-actions button:hover {
                    background: #0056b3;
                }

                @media (max-width: 768px) {
                    .dashboard-details {
                        grid-template-columns: 1fr;
                    }

                    .dashboard-summary {
                        grid-template-columns: 1fr 1fr;
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    },

    /**
     * 导出报告
     * @function exportReport - 导出质量报告
     * @param {string} format - 导出格式 ('json' | 'csv' | 'html')
     */
    exportReport(format = 'json') {
        const report = this.generateReport();
        let content = '';
        let filename = '';
        let mimeType = '';

        switch (format.toLowerCase()) {
            case 'json':
                content = JSON.stringify(report, null, 2);
                filename = `quality-report-${new Date().toISOString().split('T')[0]}.json`;
                mimeType = 'application/json';
                break;

            case 'csv':
                content = this.convertReportToCSV(report);
                filename = `quality-report-${new Date().toISOString().split('T')[0]}.csv`;
                mimeType = 'text/csv';
                break;

            case 'html':
                content = this.convertReportToHTML(report);
                filename = `quality-report-${new Date().toISOString().split('T')[0]}.html`;
                mimeType = 'text/html';
                break;

            default:
                console.warn('不支持的导出格式:', format);
                return;
        }

        // 创建下载链接
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log(`📄 质量报告已导出: ${filename}`);
    },

    /**
     * 转换报告为CSV格式
     * @function convertReportToCSV - 转换报告为CSV
     * @param {Object} report - 质量报告
     * @returns {string} CSV内容
     */
    convertReportToCSV(report) {
        let csv = 'Category,Severity,Title,Details,Timestamp\n';

        for (const [category, issues] of Object.entries(report.issues)) {
            issues.forEach(issue => {
                csv += `"${category}","${issue.severity}","${issue.title}","${issue.details}","${issue.timestamp}"\n`;
            });
        }

        return csv;
    },

    /**
     * 转换报告为HTML格式
     * @function convertReportToHTML - 转换报告为HTML
     * @param {Object} report - 质量报告
     * @returns {string} HTML内容
     */
    convertReportToHTML(report) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>代码质量报告</title>
                <meta charset="UTF-8">
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .summary { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 30px; }
                    .issue { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; }
                    .critical { border-left-color: #dc3545; }
                    .high { border-left-color: #fd7e14; }
                    .medium { border-left-color: #ffc107; }
                    .low { border-left-color: #6c757d; }
                </style>
            </head>
            <body>
                <h1>代码质量报告</h1>
                <div class="summary">
                    <h2>摘要</h2>
                    <p>健康评分: <strong>${report.summary.healthScore}</strong></p>
                    <p>总问题数: <strong>${report.summary.totalIssues}</strong></p>
                    <p>关键问题: <strong>${report.summary.criticalIssues}</strong></p>
                    <p>生成时间: <strong>${report.timestamp}</strong></p>
                </div>

                <h2>问题详情</h2>
                ${Object.entries(report.issues).map(([category, issues]) => `
                    <h3>${category}</h3>
                    ${issues.map(issue => `
                        <div class="issue ${issue.severity.toLowerCase()}">
                            <strong>${issue.severity}</strong>: ${issue.title}
                            <br><small>${issue.details}</small>
                        </div>
                    `).join('')}
                `).join('')}

                <h2>改进建议</h2>
                <ul>
                    ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                </ul>
            </body>
            </html>
        `;
    }
};

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.CodeQualityMonitor = CodeQualityMonitor;
    
    // 自动初始化（如果配置允许）
    if (window.AppConfig && window.AppConfig.debugMode) {
        CodeQualityMonitor.init({
            enabled: true,
            autoCheck: true
        });
    }
    
    console.log('✅ 代码质量监控模块已加载');
}

// ES6模块导出
export { CodeQualityMonitor };
