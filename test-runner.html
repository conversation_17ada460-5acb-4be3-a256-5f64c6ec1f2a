<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动化测试运行器</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .control-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .control-group button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        .control-group button:hover {
            background: #0056b3;
        }
        
        .control-group button.success {
            background: #28a745;
        }
        
        .control-group button.success:hover {
            background: #218838;
        }
        
        .control-group button.danger {
            background: #dc3545;
        }
        
        .control-group button.danger:hover {
            background: #c82333;
        }
        
        .status-bar {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .status-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .status-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .status-value.passed { color: #28a745; }
        .status-value.failed { color: #dc3545; }
        .status-value.total { color: #007bff; }
        .status-value.rate { color: #17a2b8; }
        
        .test-results {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-results-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .test-results-content {
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }
        
        .test-suite {
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        
        .test-suite-header {
            background: #f8f9fa;
            padding: 15px;
            font-weight: bold;
            border-bottom: 1px solid #dee2e6;
        }
        
        .test-case {
            padding: 10px 15px;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .test-case:last-child {
            border-bottom: none;
        }
        
        .test-case.passed {
            background: #f8fff9;
            border-left: 4px solid #28a745;
        }
        
        .test-case.failed {
            background: #fff8f8;
            border-left: 4px solid #dc3545;
        }
        
        .test-case.pending {
            background: #fffbf0;
            border-left: 4px solid #ffc107;
        }
        
        .test-name {
            flex: 1;
        }
        
        .test-duration {
            font-size: 12px;
            color: #666;
            margin-left: 10px;
        }
        
        .test-status {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .test-status.passed {
            background: #d4edda;
            color: #155724;
        }
        
        .test-status.failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .test-status.pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .error-details {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #dc3545;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin-top: 20px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.info { color: #0d6efd; }
        .log-entry.warn { color: #fd7e14; }
        .log-entry.error { color: #dc3545; }
        .log-entry.success { color: #198754; }
        
        @media (max-width: 768px) {
            .control-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group button {
                width: 100%;
            }
            
            .status-bar {
                grid-template-columns: 1fr 1fr;
            }
        }
    </style>
    
    <!-- 核心模块 -->
    <script src="js/core/app-config.js"></script>
    <script src="js/core/dom-cache.js"></script>
    <script src="js/core/debug-manager.js"></script>
    
    <!-- 测试框架 -->
    <script src="js/testing/test-framework.js"></script>
    
    <!-- 测试用例 -->
    <script src="js/testing/core-modules.test.js"></script>
    <script src="js/testing/integration.test.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 自动化测试运行器</h1>
            <p>模块化代码的单元测试和集成测试</p>
        </div>
        
        <div class="controls">
            <h3>测试控制</h3>
            <div class="control-group">
                <button onclick="runAllTests()" class="success">运行所有测试</button>
                <button onclick="runUnitTests()">运行单元测试</button>
                <button onclick="runIntegrationTests()">运行集成测试</button>
                <button onclick="clearResults()" class="danger">清空结果</button>
                <button onclick="exportTestReport('html')">导出HTML报告</button>
                <button onclick="exportTestReport('json')">导出JSON报告</button>
            </div>
            
            <div class="control-group">
                <label>
                    <input type="checkbox" id="verbose-mode" checked> 详细模式
                </label>
                <label>
                    <input type="checkbox" id="stop-on-failure"> 失败时停止
                </label>
                <label>
                    超时时间: <input type="number" id="timeout" value="5000" min="1000" max="30000" style="width: 80px;"> ms
                </label>
            </div>
        </div>
        
        <div class="status-bar">
            <div class="status-item">
                <div class="status-value total" id="total-tests">0</div>
                <div>总测试数</div>
            </div>
            <div class="status-item">
                <div class="status-value passed" id="passed-tests">0</div>
                <div>通过</div>
            </div>
            <div class="status-item">
                <div class="status-value failed" id="failed-tests">0</div>
                <div>失败</div>
            </div>
            <div class="status-item">
                <div class="status-value rate" id="success-rate">0%</div>
                <div>成功率</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="test-duration">0ms</div>
                <div>总耗时</div>
            </div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill" style="width: 0%;"></div>
        </div>
        
        <div class="test-results">
            <div class="test-results-header">
                <h3>测试结果</h3>
            </div>
            <div class="test-results-content" id="test-results-content">
                <p style="text-align: center; color: #666; padding: 40px;">
                    点击"运行所有测试"开始测试
                </p>
            </div>
        </div>
        
        <div class="log-output" id="log-output">
            <div class="log-entry info">[系统] 测试运行器已加载</div>
        </div>
    </div>
    
    <script>
        // 全局变量
        let currentTestResults = null;
        let testRunning = false;
        
        /**
         * 页面加载完成后初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            addLog('info', '页面加载完成，正在检查模块状态...');
            
            setTimeout(() => {
                checkModuleStatus();
                updateTestFrameworkConfig();
            }, 1000);
        });
        
        /**
         * 检查模块状态
         */
        function checkModuleStatus() {
            const requiredModules = [
                'AppConfig', 'CurrencyConfig', 'CompanyInfo', 'ExportConfig', 'TemplateConfig',
                'DOMCache', 'SafeDOM', 'DebugManager', 'TestFramework', 'Assert'
            ];
            
            let loadedCount = 0;
            requiredModules.forEach(moduleName => {
                if (typeof window[moduleName] !== 'undefined') {
                    loadedCount++;
                    addLog('success', `模块 ${moduleName} 加载成功`);
                } else {
                    addLog('error', `模块 ${moduleName} 加载失败`);
                }
            });
            
            addLog('info', `模块加载完成: ${loadedCount}/${requiredModules.length}`);
            
            if (loadedCount === requiredModules.length) {
                addLog('success', '所有模块加载成功，可以开始测试');
            } else {
                addLog('warn', '部分模块加载失败，测试结果可能不准确');
            }
        }
        
        /**
         * 更新测试框架配置
         */
        function updateTestFrameworkConfig() {
            if (window.TestFramework) {
                const verboseMode = document.getElementById('verbose-mode').checked;
                const stopOnFailure = document.getElementById('stop-on-failure').checked;
                const timeout = parseInt(document.getElementById('timeout').value);
                
                window.TestFramework.config.verbose = verboseMode;
                window.TestFramework.config.stopOnFailure = stopOnFailure;
                window.TestFramework.config.timeout = timeout;
                
                addLog('info', `测试配置已更新: 详细模式=${verboseMode}, 失败停止=${stopOnFailure}, 超时=${timeout}ms`);
            }
        }
        
        /**
         * 运行所有测试
         */
        async function runAllTests() {
            if (testRunning) {
                addLog('warn', '测试正在运行中，请等待完成');
                return;
            }
            
            if (!window.TestFramework) {
                addLog('error', '测试框架未加载');
                return;
            }
            
            testRunning = true;
            addLog('info', '开始运行所有测试...');
            
            try {
                updateTestFrameworkConfig();
                
                // 重置进度
                updateProgress(0);
                
                // 运行测试
                const results = await window.TestFramework.runTests();
                currentTestResults = results;
                
                // 更新UI
                updateStatusBar(results);
                updateProgress(100);
                displayTestResults();
                
                addLog('success', `测试完成: ${results.passed}/${results.total} 通过`);
                
            } catch (error) {
                addLog('error', `测试运行失败: ${error.message}`);
            } finally {
                testRunning = false;
            }
        }
        
        /**
         * 运行单元测试
         */
        async function runUnitTests() {
            addLog('info', '运行单元测试功能暂未实现');
            // TODO: 实现单独运行单元测试的功能
        }
        
        /**
         * 运行集成测试
         */
        async function runIntegrationTests() {
            addLog('info', '运行集成测试功能暂未实现');
            // TODO: 实现单独运行集成测试的功能
        }
        
        /**
         * 清空结果
         */
        function clearResults() {
            currentTestResults = null;
            
            // 重置状态栏
            document.getElementById('total-tests').textContent = '0';
            document.getElementById('passed-tests').textContent = '0';
            document.getElementById('failed-tests').textContent = '0';
            document.getElementById('success-rate').textContent = '0%';
            document.getElementById('test-duration').textContent = '0ms';
            
            // 重置进度条
            updateProgress(0);
            
            // 清空结果显示
            document.getElementById('test-results-content').innerHTML = 
                '<p style="text-align: center; color: #666; padding: 40px;">点击"运行所有测试"开始测试</p>';
            
            addLog('info', '测试结果已清空');
        }
        
        /**
         * 导出测试报告
         */
        function exportTestReport(format) {
            if (!currentTestResults) {
                addLog('warn', '没有测试结果可导出，请先运行测试');
                return;
            }
            
            if (!window.TestFramework) {
                addLog('error', '测试框架未加载');
                return;
            }
            
            try {
                window.TestFramework.exportReport(format);
                addLog('success', `测试报告已导出为 ${format.toUpperCase()} 格式`);
            } catch (error) {
                addLog('error', `导出报告失败: ${error.message}`);
            }
        }
        
        /**
         * 更新状态栏
         */
        function updateStatusBar(results) {
            document.getElementById('total-tests').textContent = results.total;
            document.getElementById('passed-tests').textContent = results.passed;
            document.getElementById('failed-tests').textContent = results.failed;
            
            const successRate = results.total > 0 ? ((results.passed / results.total) * 100).toFixed(1) : 0;
            document.getElementById('success-rate').textContent = successRate + '%';
            
            document.getElementById('test-duration').textContent = Math.round(results.duration) + 'ms';
        }
        
        /**
         * 更新进度条
         */
        function updateProgress(percent) {
            const progressFill = document.getElementById('progress-fill');
            progressFill.style.width = percent + '%';
        }
        
        /**
         * 显示测试结果
         */
        function displayTestResults() {
            if (!window.TestFramework || !window.TestFramework.data.suites) {
                return;
            }
            
            const container = document.getElementById('test-results-content');
            let html = '';
            
            window.TestFramework.data.suites.forEach(suite => {
                html += `
                    <div class="test-suite">
                        <div class="test-suite-header">
                            📦 ${suite.name} (${suite.tests.length} 个测试)
                        </div>
                `;
                
                suite.tests.forEach(test => {
                    const statusClass = test.status || 'pending';
                    const statusText = {
                        'passed': '✅ 通过',
                        'failed': '❌ 失败',
                        'pending': '⏳ 待运行'
                    }[statusClass] || '❓ 未知';
                    
                    html += `
                        <div class="test-case ${statusClass}">
                            <div class="test-name">${test.name}</div>
                            <div class="test-duration">${test.duration ? test.duration.toFixed(2) + 'ms' : ''}</div>
                            <div class="test-status ${statusClass}">${statusText}</div>
                        </div>
                    `;
                    
                    if (test.error) {
                        html += `
                            <div class="error-details">
                                错误: ${test.error.message}
                            </div>
                        `;
                    }
                });
                
                html += '</div>';
            });
            
            container.innerHTML = html;
        }
        
        /**
         * 添加日志
         */
        function addLog(level, message) {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
            
            // 限制日志数量
            const entries = logOutput.querySelectorAll('.log-entry');
            if (entries.length > 100) {
                entries[0].remove();
            }
        }
        
        // 监听配置变化
        document.getElementById('verbose-mode').addEventListener('change', updateTestFrameworkConfig);
        document.getElementById('stop-on-failure').addEventListener('change', updateTestFrameworkConfig);
        document.getElementById('timeout').addEventListener('change', updateTestFrameworkConfig);
    </script>
</body>
</html>
