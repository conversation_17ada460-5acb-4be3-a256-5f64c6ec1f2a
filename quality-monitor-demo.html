<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码质量监控演示</title>
    
    <!-- 基础样式 -->
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .header p {
            margin: 0;
            color: #666;
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .controls h3 {
            margin: 0 0 15px 0;
        }
        
        .control-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .control-group button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        .control-group button:hover {
            background: #0056b3;
        }
        
        .control-group button.danger {
            background: #dc3545;
        }
        
        .control-group button.danger:hover {
            background: #c82333;
        }
        
        .control-group button.success {
            background: #28a745;
        }
        
        .control-group button.success:hover {
            background: #218838;
        }
        
        .status-bar {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #6c757d;
        }
        
        .status-indicator.active {
            background: #28a745;
        }
        
        .status-indicator.warning {
            background: #ffc107;
        }
        
        .status-indicator.error {
            background: #dc3545;
        }
        
        .dashboard-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 400px;
        }
        
        .log-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .log-container h3 {
            margin: 0 0 15px 0;
        }
        
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.info { color: #0d6efd; }
        .log-entry.warn { color: #fd7e14; }
        .log-entry.error { color: #dc3545; }
        .log-entry.success { color: #198754; }
        
        @media (max-width: 768px) {
            .control-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group button {
                width: 100%;
            }
            
            .status-bar {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
    
    <!-- 核心模块 -->
    <script src="js/core/app-config.js"></script>
    <script src="js/core/dom-cache.js"></script>
    <script src="js/core/debug-manager.js"></script>
    
    <!-- 质量监控模块 -->
    <script src="js/utils/code-quality-monitor.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 代码质量监控系统</h1>
            <p>实时监控应用性能、内存使用、错误率和代码质量指标</p>
        </div>
        
        <div class="controls">
            <h3>监控控制</h3>
            <div class="control-group">
                <button onclick="startMonitoring()" class="success">开始监控</button>
                <button onclick="stopMonitoring()" class="danger">停止监控</button>
                <button onclick="runQualityCheck()">立即检查</button>
                <button onclick="generateReport()">生成报告</button>
                <button onclick="exportReport('json')">导出JSON</button>
                <button onclick="exportReport('html')">导出HTML</button>
                <button onclick="clearLogs()">清理日志</button>
            </div>
        </div>
        
        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator" id="monitor-status"></div>
                <span>监控状态: <span id="monitor-text">未启动</span></span>
            </div>
            <div class="status-item">
                <span>检查次数: <span id="check-count">0</span></span>
            </div>
            <div class="status-item">
                <span>问题数量: <span id="issue-count">0</span></span>
            </div>
            <div class="status-item">
                <span>健康评分: <span id="health-score">--</span></span>
            </div>
            <div class="status-item">
                <span>最后检查: <span id="last-check">从未</span></span>
            </div>
        </div>
        
        <div class="dashboard-container" id="quality-dashboard">
            <div style="padding: 40px; text-align: center; color: #666;">
                <h3>点击"开始监控"启动质量监控系统</h3>
                <p>系统将自动检查性能、内存、错误率等指标</p>
            </div>
        </div>
        
        <div class="log-container">
            <h3>监控日志</h3>
            <div class="log-output" id="log-output">
                <div class="log-entry info">[系统] 质量监控演示页面已加载</div>
            </div>
        </div>
    </div>
    
    <script>
        // 全局变量
        let monitoringActive = false;
        let statusUpdateInterval = null;
        
        /**
         * 页面加载完成后初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            addLog('info', '页面加载完成，正在初始化模块...');
            
            // 检查模块加载状态
            setTimeout(() => {
                checkModuleStatus();
                updateStatusBar();
            }, 1000);
        });
        
        /**
         * 检查模块状态
         */
        function checkModuleStatus() {
            const modules = {
                'AppConfig': typeof window.AppConfig !== 'undefined',
                'DOMCache': typeof window.DOMCache !== 'undefined',
                'DebugManager': typeof window.DebugManager !== 'undefined',
                'CodeQualityMonitor': typeof window.CodeQualityMonitor !== 'undefined'
            };
            
            let loadedCount = 0;
            for (const [name, loaded] of Object.entries(modules)) {
                if (loaded) {
                    loadedCount++;
                    addLog('success', `模块 ${name} 加载成功`);
                } else {
                    addLog('error', `模块 ${name} 加载失败`);
                }
            }
            
            addLog('info', `模块加载完成: ${loadedCount}/${Object.keys(modules).length}`);
            
            if (loadedCount === Object.keys(modules).length) {
                addLog('success', '所有模块加载成功，可以开始监控');
            }
        }
        
        /**
         * 开始监控
         */
        function startMonitoring() {
            if (!window.CodeQualityMonitor) {
                addLog('error', '代码质量监控模块未加载');
                return;
            }
            
            try {
                window.CodeQualityMonitor.init({
                    enabled: true,
                    autoCheck: true,
                    checkInterval: 10000, // 10秒检查一次（演示用）
                    reportInterval: 60000  // 1分钟生成报告（演示用）
                });
                
                monitoringActive = true;
                addLog('success', '质量监控已启动');
                
                // 创建仪表板
                window.CodeQualityMonitor.createDashboard('quality-dashboard');
                
                // 开始状态更新
                startStatusUpdates();
                
            } catch (error) {
                addLog('error', `启动监控失败: ${error.message}`);
            }
        }
        
        /**
         * 停止监控
         */
        function stopMonitoring() {
            if (!window.CodeQualityMonitor) {
                addLog('error', '代码质量监控模块未加载');
                return;
            }
            
            try {
                window.CodeQualityMonitor.stopMonitoring();
                monitoringActive = false;
                addLog('warn', '质量监控已停止');
                
                // 停止状态更新
                stopStatusUpdates();
                
            } catch (error) {
                addLog('error', `停止监控失败: ${error.message}`);
            }
        }
        
        /**
         * 运行质量检查
         */
        function runQualityCheck() {
            if (!window.CodeQualityMonitor) {
                addLog('error', '代码质量监控模块未加载');
                return;
            }
            
            try {
                window.CodeQualityMonitor.runQualityCheck();
                addLog('info', '手动质量检查已执行');
                
                // 更新仪表板
                setTimeout(() => {
                    window.CodeQualityMonitor.createDashboard('quality-dashboard');
                }, 1000);
                
            } catch (error) {
                addLog('error', `质量检查失败: ${error.message}`);
            }
        }
        
        /**
         * 生成报告
         */
        function generateReport() {
            if (!window.CodeQualityMonitor) {
                addLog('error', '代码质量监控模块未加载');
                return;
            }
            
            try {
                const report = window.CodeQualityMonitor.generateReport();
                addLog('success', `质量报告已生成，健康评分: ${report.summary.healthScore}`);
                
                // 更新仪表板
                window.CodeQualityMonitor.createDashboard('quality-dashboard');
                
            } catch (error) {
                addLog('error', `生成报告失败: ${error.message}`);
            }
        }
        
        /**
         * 导出报告
         */
        function exportReport(format) {
            if (!window.CodeQualityMonitor) {
                addLog('error', '代码质量监控模块未加载');
                return;
            }
            
            try {
                window.CodeQualityMonitor.exportReport(format);
                addLog('success', `报告已导出为 ${format.toUpperCase()} 格式`);
            } catch (error) {
                addLog('error', `导出报告失败: ${error.message}`);
            }
        }
        
        /**
         * 开始状态更新
         */
        function startStatusUpdates() {
            if (statusUpdateInterval) return;
            
            statusUpdateInterval = setInterval(() => {
                updateStatusBar();
            }, 2000);
        }
        
        /**
         * 停止状态更新
         */
        function stopStatusUpdates() {
            if (statusUpdateInterval) {
                clearInterval(statusUpdateInterval);
                statusUpdateInterval = null;
            }
        }
        
        /**
         * 更新状态栏
         */
        function updateStatusBar() {
            const monitorStatus = document.getElementById('monitor-status');
            const monitorText = document.getElementById('monitor-text');
            const checkCount = document.getElementById('check-count');
            const issueCount = document.getElementById('issue-count');
            const healthScore = document.getElementById('health-score');
            const lastCheck = document.getElementById('last-check');
            
            if (monitoringActive && window.CodeQualityMonitor) {
                const status = window.CodeQualityMonitor.getStatus();
                
                monitorStatus.className = 'status-indicator active';
                monitorText.textContent = '运行中';
                checkCount.textContent = status.checkCount || 0;
                issueCount.textContent = status.issueCount || 0;
                
                // 获取最新报告的健康评分
                if (window.CodeQualityMonitor.data.reports.length > 0) {
                    const latestReport = window.CodeQualityMonitor.data.reports[
                        window.CodeQualityMonitor.data.reports.length - 1
                    ];
                    healthScore.textContent = latestReport.summary.healthScore;
                } else {
                    healthScore.textContent = '--';
                }
                
                if (status.lastCheck) {
                    const checkTime = new Date(status.lastCheck);
                    lastCheck.textContent = checkTime.toLocaleTimeString();
                } else {
                    lastCheck.textContent = '从未';
                }
            } else {
                monitorStatus.className = 'status-indicator';
                monitorText.textContent = '未启动';
                checkCount.textContent = '0';
                issueCount.textContent = '0';
                healthScore.textContent = '--';
                lastCheck.textContent = '从未';
            }
        }
        
        /**
         * 添加日志
         */
        function addLog(level, message) {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
            
            // 限制日志数量
            const entries = logOutput.querySelectorAll('.log-entry');
            if (entries.length > 100) {
                entries[0].remove();
            }
        }
        
        /**
         * 清理日志
         */
        function clearLogs() {
            const logOutput = document.getElementById('log-output');
            logOutput.innerHTML = '<div class="log-entry info">[系统] 日志已清理</div>';
            addLog('info', '日志清理完成');
        }
        
        // 模拟一些问题用于演示
        function simulateIssues() {
            // 模拟内存使用增长
            const largeArray = new Array(100000).fill('demo data');
            
            // 模拟错误
            setTimeout(() => {
                try {
                    throw new Error('演示错误');
                } catch (e) {
                    console.error('模拟错误:', e);
                }
            }, 5000);
            
            addLog('info', '已模拟一些问题用于演示监控功能');
        }
        
        // 5秒后自动模拟问题
        setTimeout(simulateIssues, 5000);
    </script>
</body>
</html>
