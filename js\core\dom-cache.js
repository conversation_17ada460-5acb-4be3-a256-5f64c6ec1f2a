/**
 * DOM缓存管理模块
 * @file dom-cache.js - DOM元素缓存和管理
 * @description 提供高效的DOM元素缓存机制，减少重复的DOM查询操作
 */

/**
 * DOM缓存管理器
 * @namespace DOMCache - DOM元素缓存管理器
 */
const DOMCache = {
    // 表单元素缓存
    documentType: null,
    documentNumber: null,
    documentDate: null,
    customerName: null,
    customerPhone: null,
    customerEmail: null,
    companyName: null,
    taxId: null,
    companyAddress: null,
    companyPhone: null,
    contactPerson: null,
    channel: null,
    notes: null,
    
    // 项目表格相关
    itemsTable: null,
    itemsTbody: null,
    totalAmount: null,
    
    // AI填充相关
    aiFillPanel: null,
    aiFillBtn: null,
    aiTextInput: null,
    aiImageInput: null,
    aiProcessText: null,
    aiStatus: null,
    
    // 预览相关
    documentContainer: null,
    documentPreview: null,
    
    // 多订单相关
    multiOrderContainer: null,
    orderTabs: null,
    currentOrderDisplay: null,
    
    // 选择器相关
    currencySelector: null,
    companySelector: null,
    
    /**
     * 初始化DOM缓存
     * @function initCache - 初始化所有DOM元素缓存
     */
    initCache() {
        try {
            // 表单元素
            this.documentType = document.getElementById('document-type');
            this.documentNumber = document.getElementById('document-number');
            this.documentDate = document.getElementById('document-date');
            this.customerName = document.getElementById('customer-name');
            this.customerPhone = document.getElementById('customer-phone');
            this.customerEmail = document.getElementById('customer-email');
            this.companyName = document.getElementById('company-name');
            this.taxId = document.getElementById('tax-id');
            this.companyAddress = document.getElementById('company-address');
            this.companyPhone = document.getElementById('company-phone');
            this.contactPerson = document.getElementById('contact-person');
            this.channel = document.getElementById('channel');
            this.notes = document.getElementById('notes');
            
            // 项目表格
            this.itemsTable = document.getElementById('items-table');
            this.itemsTbody = document.getElementById('items-tbody');
            this.totalAmount = document.getElementById('total-amount');
            
            // AI填充
            this.aiFillPanel = document.getElementById('ai-fill-panel');
            this.aiFillBtn = document.getElementById('ai-fill-btn');
            this.aiTextInput = document.getElementById('ai-text-input');
            this.aiImageInput = document.getElementById('ai-image-input');
            this.aiProcessText = document.getElementById('ai-process-text');
            this.aiStatus = document.getElementById('ai-status');
            
            // 预览
            this.documentContainer = document.getElementById('document-container');
            this.documentPreview = document.getElementById('document-preview');
            
            // 多订单
            this.multiOrderContainer = document.getElementById('multi-order-container');
            this.orderTabs = document.getElementById('order-tabs');
            this.currentOrderDisplay = document.getElementById('current-order-display');
            
            // 选择器
            this.currencySelector = document.getElementById('currency-selector');
            this.companySelector = document.getElementById('company-selector');
            
            // 记录缓存统计
            const cacheStats = this.getCacheStats();
            console.log('✅ DOM缓存初始化完成:', cacheStats);
            
        } catch (error) {
            console.error('❌ DOM缓存初始化失败:', error);
        }
    },
    
    /**
     * 获取缓存统计信息
     * @function getCacheStats - 获取DOM缓存的统计信息
     * @returns {Object} 缓存统计对象
     */
    getCacheStats() {
        const stats = {
            总元素数: 0,
            有效元素数: 0,
            无效元素数: 0,
            缓存命中率: 0
        };
        
        for (const key in this) {
            if (typeof this[key] !== 'function' && key !== 'getCacheStats') {
                stats.总元素数++;
                if (this[key] && this[key].nodeType === Node.ELEMENT_NODE) {
                    stats.有效元素数++;
                } else {
                    stats.无效元素数++;
                }
            }
        }
        
        stats.缓存命中率 = stats.总元素数 > 0 
            ? Math.round((stats.有效元素数 / stats.总元素数) * 100) 
            : 0;
            
        return stats;
    },
    
    /**
     * 刷新指定元素的缓存
     * @function refreshCache - 刷新指定元素的缓存
     * @param {string} elementName - 元素名称
     */
    refreshCache(elementName) {
        const elementMap = {
            documentType: 'document-type',
            documentNumber: 'document-number',
            documentDate: 'document-date',
            customerName: 'customer-name',
            customerPhone: 'customer-phone',
            customerEmail: 'customer-email',
            companyName: 'company-name',
            taxId: 'tax-id',
            companyAddress: 'company-address',
            companyPhone: 'company-phone',
            contactPerson: 'contact-person',
            channel: 'channel',
            notes: 'notes',
            itemsTable: 'items-table',
            itemsTbody: 'items-tbody',
            totalAmount: 'total-amount',
            aiFillPanel: 'ai-fill-panel',
            aiFillBtn: 'ai-fill-btn',
            aiTextInput: 'ai-text-input',
            aiImageInput: 'ai-image-input',
            aiProcessText: 'ai-process-text',
            aiStatus: 'ai-status',
            documentContainer: 'document-container',
            documentPreview: 'document-preview',
            multiOrderContainer: 'multi-order-container',
            orderTabs: 'order-tabs',
            currentOrderDisplay: 'current-order-display',
            currencySelector: 'currency-selector',
            companySelector: 'company-selector'
        };
        
        const elementId = elementMap[elementName];
        if (elementId) {
            const element = document.getElementById(elementId);
            this[elementName] = element;
            
            if (element) {
                console.log(`✅ 缓存已刷新: ${elementName} -> ${elementId}`);
            } else {
                console.warn(`⚠️ 元素未找到: ${elementName} -> ${elementId}`);
            }
        } else {
            console.warn(`⚠️ 未知的元素名称: ${elementName}`);
        }
    },
    
    /**
     * 获取指定的DOM元素
     * @function get - 获取指定的DOM元素
     * @param {string} elementName - 元素名称
     * @returns {HTMLElement|null} DOM元素或null
     */
    get(elementName) {
        const element = this[elementName];
        if (!element || !element.parentNode) {
            this.refreshCache(elementName);
            return this[elementName];
        }
        return element;
    },
    
    /**
     * 验证缓存的DOM元素是否仍然有效
     * @function validateCache - 检查缓存的DOM元素是否仍然有效
     * @returns {Object} 验证结果
     */
    validateCache() {
        const invalidElements = [];
        const validElements = [];
        
        for (const key in this) {
            if (typeof this[key] !== 'function' && this[key] && this[key].nodeType === Node.ELEMENT_NODE) {
                if (this[key].parentNode) {
                    validElements.push(key);
                } else {
                    invalidElements.push(key);
                }
            }
        }
        
        return {
            valid: validElements,
            invalid: invalidElements,
            needsRefresh: invalidElements.length > 0
        };
    },
    
    /**
     * 强制重新缓存所有DOM元素
     * @function forceRefreshCache - 强制重新缓存所有DOM元素
     */
    forceRefreshCache() {
        console.log('🔄 强制刷新DOM缓存...');
        this.initCache();
    },
    
    /**
     * 清理无效的缓存
     * @function cleanup - 清理无效的DOM缓存
     */
    cleanup() {
        const validation = this.validateCache();
        if (validation.needsRefresh) {
            console.log('🧹 清理无效缓存:', validation.invalid);
            validation.invalid.forEach(elementName => {
                this[elementName] = null;
            });
        }
    }
};

/**
 * 安全DOM操作工具
 * @namespace SafeDOM - 安全的DOM操作工具
 */
const SafeDOM = {
    /**
     * 安全获取DOM元素
     * @function get - 安全获取DOM元素
     * @param {string} id - 元素ID
     * @returns {HTMLElement|null} DOM元素或null
     */
    get(id) {
        try {
            return document.getElementById(id);
        } catch (error) {
            console.warn(`SafeDOM.get 失败: ${id}`, error);
            return null;
        }
    },
    
    /**
     * 安全设置元素值
     * @function setValue - 安全设置元素值
     * @param {string} id - 元素ID
     * @param {string} value - 要设置的值
     * @returns {boolean} 是否设置成功
     */
    setValue(id, value) {
        try {
            const element = this.get(id);
            if (element) {
                element.value = value || '';
                return true;
            }
            return false;
        } catch (error) {
            console.warn(`SafeDOM.setValue 失败: ${id}`, error);
            return false;
        }
    },
    
    /**
     * 安全获取元素值
     * @function getValue - 安全获取元素值
     * @param {string} id - 元素ID
     * @returns {string} 元素值
     */
    getValue(id) {
        try {
            const element = this.get(id);
            return element ? (element.value || '') : '';
        } catch (error) {
            console.warn(`SafeDOM.getValue 失败: ${id}`, error);
            return '';
        }
    }
};

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.DOMCache = DOMCache;
    window.SafeDOM = SafeDOM;
    
    // 页面加载完成后自动初始化缓存
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            DOMCache.initCache();
        });
    } else {
        DOMCache.initCache();
    }
    
    console.log('✅ DOM缓存管理模块已加载');
}

// ES6模块导出
export { DOMCache, SafeDOM };
