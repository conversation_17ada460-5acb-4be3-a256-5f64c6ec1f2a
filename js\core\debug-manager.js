/**
 * 调试管理模块
 * @file debug-manager.js - 调试和日志管理
 * @description 提供统一的调试、日志记录和性能监控功能
 */

/**
 * 调试管理器
 * @namespace DebugManager - 调试和日志管理器
 */
const DebugManager = {
    // 调试模式开关
    enabled: false,
    
    // 日志级别定义
    logLevels: {
        DEBUG: 0,
        INFO: 1,
        WARN: 2,
        ERROR: 3,
        SUCCESS: 4
    },
    
    // 当前日志级别
    currentLogLevel: 1, // INFO级别
    
    // 性能监控数据
    performanceMetrics: {},
    
    // 日志历史记录
    logHistory: [],
    maxLogHistory: 1000,
    
    // 样式配置
    styles: {
        DEBUG: 'color: #6c757d; background: #f8f9fa;',
        INFO: 'color: #0d6efd; background: #e7f3ff;',
        WARN: 'color: #fd7e14; background: #fff3cd;',
        ERROR: 'color: #dc3545; background: #f8d7da;',
        SUCCESS: 'color: #198754; background: #d1e7dd;'
    },
    
    /**
     * 初始化调试管理器
     * @function init - 初始化调试管理器
     * @param {Object} config - 配置选项
     */
    init(config = {}) {
        this.enabled = config.enabled || this.enabled;
        this.currentLogLevel = config.logLevel !== undefined 
            ? this.logLevels[config.logLevel] || this.currentLogLevel 
            : this.currentLogLevel;
            
        console.log('🔧 调试管理器已初始化', {
            enabled: this.enabled,
            logLevel: Object.keys(this.logLevels)[this.currentLogLevel]
        });
    },
    
    /**
     * 记录不同级别的日志
     * @function log - 记录不同级别的日志
     * @param {string} level - 日志级别
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    log(level, message, data = null) {
        const levelValue = this.logLevels[level] || this.logLevels.INFO;
        if (levelValue < this.currentLogLevel) return;
        
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] [${level}] ${message}`;
        
        // 添加到历史记录
        this.addToHistory(level, message, data, timestamp);
        
        // 控制台输出
        const style = this.styles[level] || this.styles.INFO;
        
        if (data) {
            console.log(`%c${logMessage}`, style, data);
        } else {
            console.log(`%c${logMessage}`, style);
        }
        
        // 特殊处理错误级别
        if (level === 'ERROR' && data instanceof Error) {
            console.error('错误堆栈:', data.stack);
        }
    },
    
    /**
     * 添加到日志历史
     * @function addToHistory - 添加日志到历史记录
     * @param {string} level - 日志级别
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     * @param {string} timestamp - 时间戳
     */
    addToHistory(level, message, data, timestamp) {
        this.logHistory.push({
            level,
            message,
            data,
            timestamp
        });
        
        // 限制历史记录数量
        if (this.logHistory.length > this.maxLogHistory) {
            this.logHistory.shift();
        }
    },
    
    /**
     * 开始监控某个操作的性能
     * @function startPerformance - 开始监控某个操作的性能
     * @param {string} operation - 操作名称
     * @returns {string} 性能监控ID
     */
    startPerformance(operation) {
        const id = `${operation}_${Date.now()}`;
        this.performanceMetrics[id] = {
            operation,
            startTime: performance.now(),
            endTime: null,
            duration: null
        };
        
        this.log('DEBUG', `性能监控开始: ${operation}`, { id });
        return id;
    },
    
    /**
     * 结束监控并记录结果
     * @function endPerformance - 结束监控并记录结果
     * @param {string} id - 性能监控ID
     * @returns {Object|null} 性能数据
     */
    endPerformance(id) {
        if (!this.performanceMetrics[id]) {
            this.log('WARN', `性能监控ID不存在: ${id}`);
            return null;
        }
        
        const metric = this.performanceMetrics[id];
        metric.endTime = performance.now();
        metric.duration = metric.endTime - metric.startTime;
        
        this.log('DEBUG', `性能监控结束: ${metric.operation}`, {
            duration: `${metric.duration.toFixed(2)}ms`,
            operation: metric.operation
        });
        
        return metric;
    },
    
    /**
     * 记录导出过程的状态信息
     * @function logExportStatus - 记录导出过程的状态信息
     * @param {string} method - 导出方法
     * @param {string} type - 导出类型
     * @param {string} status - 状态
     * @param {Object} details - 详细信息
     */
    logExportStatus(method, type, status, details = {}) {
        const logData = {
            导出方法: method,
            导出类型: type,
            状态: status,
            时间戳: new Date().toISOString(),
            ...details
        };
        
        const level = status === '失败' ? 'ERROR' : status === '成功' ? 'SUCCESS' : 'INFO';
        this.log(level, `导出${status}: ${method} -> ${type}`, logData);
    },
    
    /**
     * 记录详细的错误信息
     * @function logError - 记录详细的错误信息
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     * @param {Object} additionalData - 附加数据
     */
    logError(error, context = '', additionalData = {}) {
        const errorInfo = {
            错误消息: error.message,
            错误类型: error.name,
            错误堆栈: error.stack,
            上下文: context,
            浏览器: navigator.userAgent,
            时间戳: new Date().toISOString(),
            URL: window.location.href,
            ...additionalData
        };
        
        this.log('ERROR', `错误发生: ${context}`, errorInfo);
    },
    
    /**
     * 获取当前系统状态信息
     * @function getSystemStatus - 获取当前系统状态信息
     * @returns {Object} 系统状态对象
     */
    getSystemStatus() {
        return {
            调试模式: this.enabled,
            日志级别: Object.keys(this.logLevels)[this.currentLogLevel],
            日志历史数量: this.logHistory.length,
            性能监控数量: Object.keys(this.performanceMetrics).length,
            内存使用: this.getMemoryUsage(),
            浏览器信息: {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled
            },
            页面信息: {
                url: window.location.href,
                title: document.title,
                readyState: document.readyState,
                visibilityState: document.visibilityState
            }
        };
    },
    
    /**
     * 获取内存使用情况
     * @function getMemoryUsage - 获取内存使用情况
     * @returns {Object} 内存使用信息
     */
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024),
                unit: 'MB'
            };
        }
        return { message: '浏览器不支持内存监控' };
    },
    
    /**
     * 导出日志历史
     * @function exportLogs - 导出日志历史
     * @param {string} format - 导出格式 ('json' | 'csv' | 'txt')
     * @returns {string} 导出的日志数据
     */
    exportLogs(format = 'json') {
        switch (format.toLowerCase()) {
            case 'json':
                return JSON.stringify(this.logHistory, null, 2);
            
            case 'csv':
                const headers = 'Timestamp,Level,Message,Data\n';
                const rows = this.logHistory.map(log => 
                    `"${log.timestamp}","${log.level}","${log.message}","${JSON.stringify(log.data)}"`
                ).join('\n');
                return headers + rows;
            
            case 'txt':
                return this.logHistory.map(log => 
                    `[${log.timestamp}] [${log.level}] ${log.message}${log.data ? ' | ' + JSON.stringify(log.data) : ''}`
                ).join('\n');
            
            default:
                this.log('WARN', `不支持的导出格式: ${format}`);
                return this.exportLogs('json');
        }
    },
    
    /**
     * 清理日志历史
     * @function clearLogs - 清理日志历史
     */
    clearLogs() {
        const count = this.logHistory.length;
        this.logHistory = [];
        this.log('INFO', `已清理 ${count} 条日志记录`);
    },
    
    /**
     * 设置日志级别
     * @function setLogLevel - 设置日志级别
     * @param {string} level - 日志级别
     */
    setLogLevel(level) {
        if (this.logLevels[level] !== undefined) {
            this.currentLogLevel = this.logLevels[level];
            this.log('INFO', `日志级别已设置为: ${level}`);
        } else {
            this.log('WARN', `无效的日志级别: ${level}`);
        }
    }
};

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.DebugManager = DebugManager;
    
    // 从AppConfig加载调试配置
    if (window.AppConfig) {
        DebugManager.init({
            enabled: window.AppConfig.debugMode,
            logLevel: window.AppConfig.logLevel
        });
    }
    
    console.log('✅ 调试管理模块已加载');
}

// ES6模块导出
export { DebugManager };
