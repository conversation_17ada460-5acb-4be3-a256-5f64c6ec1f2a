<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发票/收据生成器 - SmartOffice 2.0</title>
    
    <!-- 外部依赖库 - 带错误处理 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"
            onerror="console.warn('html2canvas CDN加载失败，PDF导出功能可能受限')"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"
            onerror="console.warn('jsPDF CDN加载失败，PDF导出功能可能受限')"></script>

    <!-- 外部图片资源管理模块 - 延迟加载确保依赖顺序 -->
    <script src="images-logo.js" defer onerror="console.warn('标志图片管理模块加载失败')"></script>
    <script src="images-header.js" defer onerror="console.warn('页眉图片管理模块加载失败')"></script>
    <script src="images-footer.js" defer onerror="console.warn('页脚图片管理模块加载失败')"></script>
    <script src="images-stamp.js" defer onerror="console.warn('印章图片管理模块加载失败')"></script>

    <!-- DOM重组导出管理器 - 彻底解决CSS缩放变换问题 -->
    <script src="export-dom-manager.js" defer onerror="console.warn('DOM重组导出管理器加载失败')"></script>

    <!-- 导出组件模块 - 延迟加载确保主文件先初始化 -->
    <script src="export-components.js" defer onerror="console.warn('导出组件模块加载失败')"></script>

    <!-- 模块化CSS文件 - 按优先级顺序加载 -->
    <!-- 基础样式层 -->
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/layout.css">
    <link rel="stylesheet" href="styles/components.css">

    <!-- 功能样式层 -->
    <link rel="stylesheet" href="styles/preview.css">

    <!-- 媒体查询样式层 -->
    <link rel="stylesheet" href="styles/print.css" media="print">
    <link rel="stylesheet" href="styles/responsive.css">

    <!-- 导出样式层 - 最后加载，确保最高优先级 -->
    <link rel="stylesheet" href="styles/export.css">

    <!-- 内联CSS已移除，使用模块化CSS文件 -->

    <!-- 预览模块 -->
    <script src="preview-module.js"></script>





















</head>
<body>
    <div class="container">
        <h1 class="main-title">
            发票/收据生成器 / Invoice/Receipt Generator
        </h1>
        
        <div class="grid">
            <!-- 左侧：表单输入区域 -->
            <div class="form-section">
                <h2 class="section-title">文档信息输入 / Document Information</h2>

                <!-- AI智能填充区域 - 简化版 -->
                <div class="form-group ai-fill-container">
                    <div class="ai-fill-header">
                        <h3 class="ai-fill-title">🤖 AI智能填充</h3>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="ai-fill-btn" onclick="toggleAIFillPanel()">
                            收起
                        </button>
                    </div>
                    <div id="ai-fill-panel">
                        <div class="ai-form-row">
                            <div class="form-group">
                                <textarea id="ai-text-input" rows="3" placeholder="粘贴订单信息或上传图片..."></textarea>
                            </div>
                            <div class="form-group">
                                <input type="file" id="ai-image-input" accept="image/jpeg,image/png" class="ai-image-input" title="上传图片文件">
                            </div>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-success" onclick="processAIFill()" id="ai-process-btn">
                                <span id="ai-process-text">分析填充</span>
                            </button>
                        </div>
                        <div id="ai-status" class="hidden ai-status"></div>
                    </div>
                </div>

                <!-- 文档类型选择 -->
                <div class="form-group">
                    <label for="document-type">文档类型 / Document Type</label>
                    <select id="document-type">
                        <option value="receipt">收据 / Receipt</option>
                        <option value="invoice">发票 / Invoice</option>
                    </select>
                </div>

                <!-- 公司选择 -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="company-selector">公司 / Company</label>
                        <select id="company-selector">
                            <option value="gomyhire">GoMyHire</option>
                            <option value="sky-mirror">Sky Mirror</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="currency-selector">货币 / Currency</label>
                        <select id="currency-selector">
                            <option value="MYR">马来西亚令吉 (RM) / Malaysian Ringgit</option>
                            <option value="CNY">人民币 (¥) / Chinese Yuan</option>
                        </select>
                    </div>
                </div>

                <!-- 公司信息字段 -->
                <div class="form-group">
                    <label for="company-name">公司名称 / Company Name</label>
                    <input type="text" id="company-name" placeholder="请输入公司名称 / Enter company name">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="tax-id">税号 / Tax ID</label>
                        <input type="text" id="tax-id" placeholder="请输入税号 / Enter tax ID">
                    </div>
                    <div class="form-group">
                        <label for="company-phone">公司电话 / Company Phone</label>
                        <input type="text" id="company-phone" placeholder="请输入公司电话 / Enter company phone">
                    </div>
                </div>

                <div class="form-group">
                    <label for="company-address">公司地址 / Company Address</label>
                    <input type="text" id="company-address" placeholder="请输入公司地址 / Enter company address">
                </div>

                <div class="form-group">
                    <label for="contact-person">负责人姓名 / Contact Person</label>
                    <input type="text" id="contact-person" placeholder="请输入负责人姓名 / Enter contact person name">
                </div>
                
                <!-- 基本信息 -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="document-number">单据号码 / Document Number</label>
                        <input type="text" id="document-number" placeholder="自动生成 / Auto Generate">
                    </div>
                    <div class="form-group">
                        <label for="document-date">日期 / Date</label>
                        <input type="date" id="document-date">
                    </div>
                </div>

                <!-- 客户信息 -->
                <div class="form-group">
                    <label for="customer-name">客户名称 / Customer Name</label>
                    <input type="text" id="customer-name" placeholder="请输入客户名称 / Enter customer name">
                </div>

                <div class="form-group">
                    <label for="channel">渠道 / Channel</label>
                    <input type="text" id="channel" placeholder="请输入渠道名称 / Enter channel name">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="customer-phone">客户电话 / Customer Phone</label>
                        <input type="text" id="customer-phone" placeholder="请输入客户电话 / Enter customer phone">
                    </div>
                    <div class="form-group">
                        <label for="customer-email">客户邮箱 / Customer Email</label>
                        <input type="email" id="customer-email" placeholder="请输入客户邮箱 / Enter customer email">
                    </div>
                </div>

                <!-- 项目明细 -->
                <div class="form-group">
                    <label>项目明细 / Item Details</label>

                    <!-- 多订单管理器 -->
                    <div class="multi-order-container multi-order-hidden" id="multi-order-container">
                        <div class="order-manager">
                            <div class="order-tabs" id="order-tabs">
                                <!-- 动态生成订单标签页 -->
                            </div>
                            <div class="order-controls">
                                <button type="button" class="btn btn-secondary btn-sm" onclick="addNewOrder()">+ 新增订单</button>
                                <select id="display-mode" onchange="switchDisplayMode(this.value)">
                                    <option value="combined">合并显示</option>
                                    <option value="separate">分别显示</option>
                                </select>
                                <button type="button" class="btn btn-info btn-sm" onclick="toggleMultiOrderMode()">切换模式</button>
                            </div>
                        </div>

                        <!-- 当前订单信息 -->
                        <div class="current-order-info" id="current-order-info">
                            <span>当前订单：<strong id="current-order-display">-</strong></span>
                            <span>客户：<strong id="current-customer-display">-</strong></span>
                        </div>
                    </div>

                    <table class="items-table" id="items-table">
                        <thead>
                            <tr>
                                <th class="order-column order-column-hidden" id="order-column-header">订单 / Order</th>
                                <th>项目描述 / Description</th>
                                <th>数量 / Qty</th>
                                <th>单价 / Price</th>
                                <th>金额 / Amount</th>
                                <th>操作 / Action</th>
                            </tr>
                        </thead>
                        <tbody id="items-tbody">
                            <tr>
                                <td class="order-column order-column-hidden"></td>
                                <td><input type="text" placeholder="项目描述 / Item description" class="item-description" title="项目描述 / Item description"></td>
                                <td><input type="number" value="1" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                                <td><input type="number" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                                <td class="item-amount">0.00</td>
                                <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                            </tr>
                        </tbody>
                    </table>
                    <button type="button" class="btn btn-secondary" onclick="addItem()">添加项目 / Add Item</button>
                </div>

                <!-- 总金额 -->
                <div class="form-group">
                    <label>总金额 / Total Amount</label>
                    <div class="total-amount-display">
                        RM <span id="total-amount">0.00</span>
                    </div>
                </div>

                <!-- 备注 -->
                <div class="form-group">
                    <label for="notes">备注 / Notes</label>
                    <textarea id="notes" rows="3" placeholder="请输入备注信息 / Enter notes"></textarea>
                </div>

                <!-- 操作按钮 -->
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="updatePreview()" title="手动刷新预览 / Manual refresh preview">
                        🔄 刷新预览 / Refresh Preview
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearForm()">清空表单 / Clear Form</button>
                </div>
            </div>
            
            <!-- 右侧：预览区域 -->
            <div class="preview-section">
                <div class="preview-header">
                    <h2>文档预览 / Document Preview</h2>
                    <div class="preview-controls">
                        <!-- 预览状态指示器 -->
                        <div id="preview-status" class="preview-status-indicator hidden">
                            <span id="preview-status-text">实时预览已启用 / Live preview enabled</span>
                        </div>
                        <div class="btn-group">
                            <!-- 简化的导出按钮 - 仅保留PDF和JPEG -->
                            <button type="button" class="btn btn-success export-btn" data-format="pdf">导出PDF / Export PDF</button>
                            <button type="button" class="btn btn-success export-btn" data-format="jpeg">导出JPEG / Export JPEG</button>
                        </div>
                    </div>
                </div>

                <!-- 预览容器 -->
                <div id="preview-container">
                    <div id="document-preview" class="a4-page">
                        <div id="document-container">
                            <div class="empty-preview-message">
                                请填写表单信息并点击"更新预览"按钮<br>
                                Please fill in the form and click "Update Preview" button
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        /**
         * @file 发票/收据生成器JavaScript功能模块 v3.0
         * @description 独立的发票收据生成器，包含所有必要的功能
         * @version 3.0.0
         * <AUTHOR> Team
         * @features
         * - 现代化导出系统：支持300DPI高质量PDF/JPEG导出
         * - AI智能填充：集成Gemini AI进行智能表单填充
         * - 多订单管理：支持多个订单的合并和分别显示
         * - 事件管理优化：使用事件委托减少重复绑定
         * - 安全DOM操作：防止XSS攻击的安全DOM操作工具
         * - 性能优化：DOM缓存、防抖机制、错误处理
         * @updated 2024年 - 完成代码重构和优化
         */

        // #region 图片资源管理对象
        /**
         * 图片资源管理器 - 存储所有图片的 base64 编码
         * @description 内置图片资源管理，确保独立HTML文件的完整性
         */
        const ImageBase64 = {
            /**
             * 公司标志图片 - 用于文档头部显示
             */
            logos: {
                'sky-mirror': '', // Sky Mirror World Tour 标志占位符
                'gomyhire': ''    // GoMyHire Travel 标志占位符
            },

            /**
             * 页眉图片 - 用于文档顶部装饰
             */
            headers: {
                'sky-mirror': '', // Sky Mirror 页眉图片占位符
                'gomyhire': ''    // GoMyHire 页眉图片占位符
            },

            /**
             * 页脚图片 - 用于文档底部装饰
             */
            footers: {
                'sky-mirror': '', // Sky Mirror 页脚图片占位符
                'gomyhire': ''    // GoMyHire 页脚图片占位符
            },

            /**
             * 印章图片 - 用于文档签章
             */
            stamps: {
                'sky-mirror': '', // Sky Mirror 印章占位符
                'gomyhire': ''    // GoMyHire 印章占位符
            },

            /**
             * 获取公司标志图片
             * @function getLogo - 根据公司代码获取对应的标志图片
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @returns {string} 图片的 base64 编码字符串
             */
            getLogo(company) {
                // 优先使用外部管理器，如果不可用则使用内置数据
                if (typeof logoImageManager !== 'undefined') {
                    return logoImageManager.getLogo(company);
                }
                return this.logos[company] || '';
            },

            /**
             * 获取页眉图片
             * @function getHeader - 根据公司代码获取对应的页眉图片
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @returns {string} 图片的 base64 编码字符串
             */
            getHeader(company) {
                // 优先使用外部管理器，如果不可用则使用内置数据
                if (typeof headerImageManager !== 'undefined') {
                    return headerImageManager.getHeader(company);
                }
                return this.headers[company] || '';
            },

            /**
             * 获取页脚图片
             * @function getFooter - 根据公司代码获取对应的页脚图片
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @returns {string} 图片的 base64 编码字符串
             */
            getFooter(company) {
                // 优先使用外部管理器，如果不可用则使用内置数据
                if (typeof footerImageManager !== 'undefined') {
                    return footerImageManager.getFooter(company);
                }
                return this.footers[company] || '';
            },

            /**
             * 获取印章图片
             * @function getStamp - 根据公司代码获取对应的印章图片
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @returns {string} 图片的 base64 编码字符串
             */
            getStamp(company) {
                // 优先使用外部管理器，如果不可用则使用内置数据
                if (typeof stampImageManager !== 'undefined') {
                    return stampImageManager.getStamp(company);
                }
                return this.stamps[company] || '';
            },

            /**
             * 更新图片资源
             * @function updateImage - 更新指定类型和公司的图片资源
             * @param {string} type - 图片类型 ('logo', 'header', 'footer', 'stamp')
             * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
             * @param {string} base64 - 图片的 base64 编码
             */
            updateImage(type, company, base64) {
                // 同时更新外部管理器和内置数据，确保兼容性
                switch (type) {
                    case 'logo':
                        this.logos[company] = base64;
                        if (typeof logoImageManager !== 'undefined') {
                            logoImageManager.setLogo(company, base64);
                        }
                        break;
                    case 'header':
                        this.headers[company] = base64;
                        if (typeof headerImageManager !== 'undefined') {
                            headerImageManager.setHeader(company, base64);
                        }
                        break;
                    case 'footer':
                        this.footers[company] = base64;
                        if (typeof footerImageManager !== 'undefined') {
                            footerImageManager.setFooter(company, base64);
                        }
                        break;
                    case 'stamp':
                        this.stamps[company] = base64;
                        if (typeof stampImageManager !== 'undefined') {
                            stampImageManager.setStamp(company, base64);
                        }
                        break;
                }
                console.log(`✅ 已更新${type}图片资源 - 公司: ${company}`);
            }
        };
        // #endregion

        // #region 导出系统已移至独立模块
        // 导出功能已提取到 export-components.js 模块中
        // 请确保已正确引入该模块




        // #endregion

        // #region 图片质量管理器
        /**
         * 图片质量管理器
         * @description 专门处理页眉页脚图片的高质量渲染和导出优化
         */
        const ImageQualityManager = {
            // 固定高度填充策略的尺寸配置
            recommendedSizes: {
                header: { width: 'auto', height: 130 },  // 页眉：固定高度130px，宽度自适应
                footer: { width: 'auto', height: 110 },  // 页脚：固定高度110px，宽度自适应
                stamp: { width: 120, height: 120 }      // 印章：120x120px
            },

            // 图片加载状态缓存
            loadedImages: new Map(),

            /**
             * 初始化图片质量管理器
             * @function init - 初始化图片质量管理器
             */
            init() {
                DebugManager.log('INFO', '图片质量管理器初始化完成');
                return this;
            },

            /**
             * 预加载并优化图片
             * @function preloadImage - 预加载图片并返回优化后的图片元素
             * @param {string} src - 图片源地址
             * @param {string} type - 图片类型 ('header', 'footer', 'stamp')
             * @returns {Promise<HTMLImageElement>} 优化后的图片元素
             */
            async preloadImage(src, type = 'header') {
                if (!src) return null;

                // 检查缓存
                if (this.loadedImages.has(src)) {
                    return this.loadedImages.get(src);
                }

                return new Promise((resolve, reject) => {
                    const img = new Image();

                    // 设置高质量渲染属性
                    img.style.imageRendering = 'high-quality';
                    img.style.imageRendering = '-webkit-optimize-contrast';
                    img.crossOrigin = 'anonymous'; // 避免CORS问题

                    img.onload = () => {
                        // 优化图片尺寸
                        const optimizedImg = this.optimizeImageSize(img, type);

                        // 缓存优化后的图片
                        this.loadedImages.set(src, optimizedImg);

                        DebugManager.log('DEBUG', `图片预加载完成: ${type}`, {
                            原始尺寸: `${img.naturalWidth}x${img.naturalHeight}`,
                            优化后尺寸: `${optimizedImg.width}x${optimizedImg.height}`,
                            推荐尺寸: `${this.recommendedSizes[type].width}x${this.recommendedSizes[type].height}`
                        });

                        resolve(optimizedImg);
                    };

                    img.onerror = (error) => {
                        DebugManager.log('ERROR', `图片加载失败: ${type}`, { src, error });
                        reject(new Error(`图片加载失败: ${src}`));
                    };

                    img.src = src;
                });
            },

            /**
             * 优化图片尺寸 - 固定高度填充策略
             * @function optimizeImageSize - 根据类型优化图片尺寸
             * @param {HTMLImageElement} img - 原始图片元素
             * @param {string} type - 图片类型
             * @returns {HTMLImageElement} 优化后的图片元素
             */
            optimizeImageSize(img, type) {
                const recommended = this.recommendedSizes[type];

                // 对于页眉和页脚，使用固定高度策略
                if (type === 'header' || type === 'footer') {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    // 固定高度，宽度根据原始宽高比计算
                    const aspectRatio = img.naturalWidth / img.naturalHeight;
                    const targetHeight = recommended.height;
                    const targetWidth = targetHeight * aspectRatio;

                    // 设置Canvas尺寸
                    canvas.width = targetWidth;
                    canvas.height = targetHeight;

                    // 启用最高质量缩放
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';

                    // 绘制优化后的图片
                    ctx.drawImage(img, 0, 0, targetWidth, targetHeight);

                    // 创建新的图片元素
                    const optimizedImg = new Image();
                    optimizedImg.src = canvas.toDataURL('image/png');
                    optimizedImg.width = targetWidth;
                    optimizedImg.height = targetHeight;

                    DebugManager.log('DEBUG', `${type}图片尺寸优化`, {
                        原始尺寸: `${img.naturalWidth}x${img.naturalHeight}`,
                        优化后尺寸: `${targetWidth}x${targetHeight}`,
                        固定高度: targetHeight,
                        计算宽度: targetWidth,
                        宽高比: aspectRatio.toFixed(2)
                    });

                    return optimizedImg;
                } else {
                    // 印章等其他图片保持原有逻辑
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    const targetWidth = recommended.width;
                    const targetHeight = recommended.height;

                    canvas.width = targetWidth;
                    canvas.height = targetHeight;

                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';
                    ctx.drawImage(img, 0, 0, targetWidth, targetHeight);

                    const optimizedImg = new Image();
                    optimizedImg.src = canvas.toDataURL('image/png');
                    optimizedImg.width = targetWidth;
                    optimizedImg.height = targetHeight;

                    return optimizedImg;
                }
            },

            /**
             * 等待所有图片加载完成
             * @function waitForAllImages - 等待容器中所有图片加载完成
             * @param {HTMLElement} container - 容器元素
             * @returns {Promise<void>} 加载完成的Promise
             */
            async waitForAllImages(container) {
                const images = container.querySelectorAll('img');
                const loadPromises = Array.from(images).map(img => {
                    if (img.complete) {
                        return Promise.resolve();
                    }

                    return new Promise((resolve, reject) => {
                        img.onload = resolve;
                        img.onerror = reject;

                        // 超时处理
                        setTimeout(() => {
                            DebugManager.log('WARN', '图片加载超时', { src: img.src });
                            resolve(); // 即使超时也继续
                        }, 5000);
                    });
                });

                await Promise.all(loadPromises);
                DebugManager.log('INFO', `所有图片加载完成，共${images.length}张图片`);
            },

            /**
             * 应用高质量样式
             * @function applyHighQualityStyles - 为图片元素应用高质量渲染样式
             * @param {HTMLElement} container - 容器元素
             */
            applyHighQualityStyles(container) {
                const images = container.querySelectorAll('img');
                images.forEach(img => {
                    // 应用高质量渲染样式
                    img.style.imageRendering = 'high-quality';
                    img.style.imageRendering = '-webkit-optimize-contrast';
                    img.style.webkitBackfaceVisibility = 'hidden';
                    img.style.backfaceVisibility = 'hidden';
                    img.style.webkitTransform = 'translateZ(0)';
                    img.style.transform = 'translateZ(0)';

                    // 确保图片完全加载
                    if (!img.complete) {
                        img.style.opacity = '0';
                        img.onload = () => {
                            img.style.opacity = '1';
                            img.style.transition = 'opacity 0.3s ease';
                        };
                    }
                });

                DebugManager.log('DEBUG', `已为${images.length}张图片应用高质量样式`);
            },

            /**
             * 检查图片质量 - 固定高度填充策略
             * @function checkImageQuality - 检查图片是否符合质量标准
             * @param {HTMLImageElement} img - 图片元素
             * @param {string} type - 图片类型
             * @returns {object} 质量检查结果
             */
            checkImageQuality(img, type) {
                const recommended = this.recommendedSizes[type];
                const actual = {
                    width: img.naturalWidth || img.width,
                    height: img.naturalHeight || img.height
                };

                let qualityScore, recommendation, recommendedSize;

                if (type === 'header' || type === 'footer') {
                    // 对于页眉页脚，主要检查高度质量
                    qualityScore = actual.height / recommended.height;
                    const aspectRatio = actual.width / actual.height;
                    const calculatedWidth = Math.round(recommended.height * aspectRatio);

                    recommendedSize = `高度${recommended.height}px，宽度自适应（当前约${calculatedWidth}px）`;
                    recommendation = qualityScore < 1.0 ?
                        `建议使用高度${recommended.height}px或更高的图片，宽度将自动按比例计算` :
                        '图片质量良好，高度充足';
                } else {
                    // 印章等其他图片保持原有逻辑
                    qualityScore = Math.min(
                        actual.width / recommended.width,
                        actual.height / recommended.height
                    );
                    recommendedSize = `${recommended.width}x${recommended.height}`;
                    recommendation = qualityScore < 1.0 ?
                        `建议使用${recommended.width}x${recommended.height}或更高分辨率的图片` :
                        '图片质量良好';
                }

                return {
                    score: qualityScore,
                    isHighQuality: qualityScore >= 1.0,
                    recommendation: recommendation,
                    actualSize: `${actual.width}x${actual.height}`,
                    recommendedSize: recommendedSize,
                    strategy: type === 'header' || type === 'footer' ? '固定高度填充' : '固定尺寸'
                };
            }
        };
        // #endregion

        // #region 调试和日志管理器
        /**
         * 调试和日志管理器
         * @description 统一管理导出过程中的调试信息和错误处理
         */
        const DebugManager = {
            // 调试模式开关
            debugMode: true,

            // 日志级别
            logLevels: {
                ERROR: 0,
                WARN: 1,
                INFO: 2,
                DEBUG: 3
            },

            currentLogLevel: 3, // DEBUG级别

            // 性能监控
            performanceMetrics: {},

            /**
             * 初始化调试管理器
             * @function init - 初始化调试管理器
             */
            init() {
                this.log('INFO', '调试管理器初始化完成');
                return this;
            },

            /**
             * 记录日志
             * @function log - 记录不同级别的日志
             * @param {string} level - 日志级别
             * @param {string} message - 日志消息
             * @param {object} data - 附加数据
             */
            log(level, message, data = null) {
                if (!this.debugMode) return;

                const levelValue = this.logLevels[level] || this.logLevels.INFO;
                if (levelValue > this.currentLogLevel) return;

                const timestamp = new Date().toISOString();
                const logMessage = `[${timestamp}] [${level}] ${message}`;

                switch (level) {
                    case 'ERROR':
                        console.error(logMessage, data);
                        break;
                    case 'WARN':
                        console.warn(logMessage, data);
                        break;
                    case 'INFO':
                        console.info(logMessage, data);
                        break;
                    case 'DEBUG':
                    default:
                        console.log(logMessage, data);
                        break;
                }
            },

            /**
             * 开始性能监控
             * @function startPerformance - 开始监控某个操作的性能
             * @param {string} operation - 操作名称
             */
            startPerformance(operation) {
                this.performanceMetrics[operation] = {
                    startTime: performance.now(),
                    endTime: null,
                    duration: null
                };
                this.log('DEBUG', `开始性能监控: ${operation}`);
            },

            /**
             * 结束性能监控
             * @function endPerformance - 结束监控并记录结果
             * @param {string} operation - 操作名称
             * @returns {number} 操作耗时（毫秒）
             */
            endPerformance(operation) {
                if (!this.performanceMetrics[operation]) {
                    this.log('WARN', `性能监控未找到操作: ${operation}`);
                    return 0;
                }

                const metric = this.performanceMetrics[operation];
                metric.endTime = performance.now();
                metric.duration = metric.endTime - metric.startTime;

                this.log('INFO', `性能监控完成: ${operation}`, {
                    耗时: `${metric.duration.toFixed(2)}ms`,
                    开始时间: metric.startTime,
                    结束时间: metric.endTime
                });

                return metric.duration;
            },

            /**
             * 记录导出状态
             * @function logExportStatus - 记录导出过程的状态信息
             * @param {string} method - 导出方法
             * @param {string} type - 导出类型（PDF/Image）
             * @param {string} status - 状态（开始/成功/失败）
             * @param {object} details - 详细信息
             */
            logExportStatus(method, type, status, details = {}) {
                const logData = {
                    导出方法: method,
                    导出类型: type,
                    状态: status,
                    时间戳: new Date().toISOString(),
                    ...details
                };

                const level = status === '失败' ? 'ERROR' : status === '成功' ? 'INFO' : 'DEBUG';
                this.log(level, `导出${status}: ${method} -> ${type}`, logData);
            },

            /**
             * 记录错误详情
             * @function logError - 记录详细的错误信息
             * @param {Error} error - 错误对象
             * @param {string} context - 错误上下文
             * @param {object} additionalInfo - 附加信息
             */
            logError(error, context, additionalInfo = {}) {
                const errorInfo = {
                    错误消息: error.message,
                    错误堆栈: error.stack,
                    错误上下文: context,
                    浏览器信息: navigator.userAgent,
                    页面URL: window.location.href,
                    时间戳: new Date().toISOString(),
                    ...additionalInfo
                };

                this.log('ERROR', `错误发生: ${context}`, errorInfo);
                return errorInfo;
            },

            /**
             * 获取系统状态
             * @function getSystemStatus - 获取当前系统状态信息
             * @returns {object} 系统状态对象
             */
            getSystemStatus() {
                return {
                    浏览器: navigator.userAgent,
                    视口尺寸: `${window.innerWidth}x${window.innerHeight}`,
                    设备像素比: window.devicePixelRatio,
                    在线状态: navigator.onLine,
                    语言: navigator.language,
                    平台: navigator.platform,
                    内存信息: navigator.deviceMemory ? `${navigator.deviceMemory}GB` : '未知',
                    连接类型: navigator.connection ? navigator.connection.effectiveType : '未知',
                    DOM就绪状态: document.readyState,
                    页面可见性: document.visibilityState
                };
            }
        };
        // #endregion

        // #region 移除旧导出代码 - 已清理完成





        // #endregion

        // #region 全局变量和配置
        /**
         * 全局配置对象
         * @description 存储应用程序的全局配置信息
         */
        const AppConfig = {
            currentCompany: 'gomyhire',     // 当前选中的公司
            currentDocumentType: 'receipt', // 当前文档类型
            currentCurrency: 'MYR',         // 当前货币类型
            itemCounter: 1,                 // 项目计数器
            autoPreview: true,              // 启用自动预览功能，确保AI填充后能自动更新
            geminiApiKey: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',  // Gemini API密钥（硬植入）
            geminiModel: 'gemini-2.5-flash-lite-preview-06-17',        // Gemini AI模型版本
            geminiApiVersion: 'v1beta',     // Gemini API版本
            geminiTemperature: 0.2,         // AI创造性参数
            geminiMaxTokens: 8192,          // 最大输出token数

            // 多订单支持配置
            multiOrderMode: false,          // 是否启用多订单模式
            multiOrderData: [],             // 多订单数据存储
            currentOrderIndex: 0,           // 当前选中的订单索引
            displayMode: 'separate',        // 显示模式：'separate' | 'combined'
            orderCounter: 1                 // 订单计数器
        };

        /**
         * DOM元素缓存对象
         * @description 缓存常用DOM元素，减少重复查询，提升性能
         */
        const DOMCache = {
            // 表单元素缓存
            documentType: null,
            companySelector: null,
            currencySelector: null,
            documentNumber: null,
            documentDate: null,
            customerName: null,
            channel: null,
            customerPhone: null,
            customerEmail: null,
            notes: null,
            totalAmount: null,
            itemsTable: null,
            itemsTbody: null,

            // 公司信息字段缓存
            companyName: null,
            taxId: null,
            companyAddress: null,
            companyPhone: null,
            contactPerson: null,

            // AI相关元素缓存
            aiFillPanel: null,
            aiFillBtn: null,
            aiTextInput: null,
            aiImageInput: null,
            aiStatus: null,
            aiProcessText: null,

            // 预览相关元素缓存
            documentContainer: null,
            documentPreview: null,
            previewContainer: null,
            previewStatus: null,
            previewStatusText: null,

            // 导出相关元素缓存
            exportButtons: null,
            modernExportUI: null,

            // 多订单相关元素缓存
            multiOrderToggle: null,
            displayModeSelector: null,
            orderTabs: null,
            currentOrderDisplay: null,

            /**
             * 初始化DOM缓存
             * @function initCache - 初始化所有DOM元素缓存
             */
            initCache() {
                console.log('🔄 初始化DOM缓存...');

                // 表单元素
                this.documentType = document.getElementById('document-type');
                this.companySelector = document.getElementById('company-selector');
                this.currencySelector = document.getElementById('currency-selector');
                this.documentNumber = document.getElementById('document-number');
                this.documentDate = document.getElementById('document-date');
                this.customerName = document.getElementById('customer-name');
                this.channel = document.getElementById('channel');
                this.customerPhone = document.getElementById('customer-phone');
                this.customerEmail = document.getElementById('customer-email');
                this.notes = document.getElementById('notes');
                this.totalAmount = document.getElementById('total-amount');
                this.itemsTable = document.getElementById('items-table');
                this.itemsTbody = document.getElementById('items-tbody');

                // 公司信息字段
                this.companyName = document.getElementById('company-name');
                this.taxId = document.getElementById('tax-id');
                this.companyAddress = document.getElementById('company-address');
                this.companyPhone = document.getElementById('company-phone');
                this.contactPerson = document.getElementById('contact-person');

                // AI相关元素
                this.aiFillPanel = document.getElementById('ai-fill-panel');
                this.aiFillBtn = document.getElementById('ai-fill-btn');
                this.aiTextInput = document.getElementById('ai-text-input');
                this.aiImageInput = document.getElementById('ai-image-input');
                this.aiStatus = document.getElementById('ai-status');
                this.aiProcessText = document.getElementById('ai-process-text');

                // 预览相关元素
                this.documentContainer = document.getElementById('document-container');
                this.documentPreview = document.getElementById('document-preview');
                this.previewContainer = document.getElementById('preview-container');
                this.previewStatus = document.getElementById('preview-status');
                this.previewStatusText = document.getElementById('preview-status-text');

                // 导出相关元素
                this.exportButtons = document.querySelector('.btn-group');
                this.modernExportUI = document.querySelector('.modern-export-ui');

                // 多订单相关元素
                this.multiOrderToggle = document.getElementById('multi-order-toggle');
                this.displayModeSelector = document.getElementById('display-mode-selector');
                this.orderTabs = document.getElementById('order-tabs');
                this.currentOrderDisplay = document.getElementById('current-order-display');

                // 统计缓存结果
                const cacheStats = this.getCacheStats();
                console.log('✅ DOM缓存初始化完成:', cacheStats);
            },

            /**
             * 获取缓存统计信息
             * @function getCacheStats - 获取DOM缓存的统计信息
             * @returns {Object} 缓存统计对象
             */
            getCacheStats() {
                const stats = {
                    总元素数: 0,
                    成功缓存: 0,
                    缓存失败: 0,
                    失败元素: []
                };

                for (const key in this) {
                    if (typeof this[key] !== 'function' && key !== 'getCacheStats') {
                        stats.总元素数++;
                        if (this[key]) {
                            stats.成功缓存++;
                        } else {
                            stats.缓存失败++;
                            stats.失败元素.push(key);
                        }
                    }
                }

                return stats;
            },

            /**
             * 刷新特定元素的缓存
             * @function refreshCache - 刷新指定元素的缓存
             * @param {string} elementName - 元素名称
             * @returns {boolean} 是否刷新成功
             */
            refreshCache(elementName) {
                const elementMap = {
                    documentType: 'document-type',
                    companySelector: 'company-selector',
                    currencySelector: 'currency-selector',
                    documentNumber: 'document-number',
                    documentDate: 'document-date',
                    customerName: 'customer-name',
                    channel: 'channel',
                    customerPhone: 'customer-phone',
                    customerEmail: 'customer-email',
                    notes: 'notes',
                    totalAmount: 'total-amount',
                    itemsTable: 'items-table',
                    itemsTbody: 'items-tbody',
                    companyName: 'company-name',
                    taxId: 'tax-id',
                    companyAddress: 'company-address',
                    companyPhone: 'company-phone',
                    contactPerson: 'contact-person',
                    aiFillPanel: 'ai-fill-panel',
                    aiFillBtn: 'ai-fill-btn',
                    aiTextInput: 'ai-text-input',
                    aiImageInput: 'ai-image-input',
                    aiStatus: 'ai-status',
                    aiProcessText: 'ai-process-text',
                    documentContainer: 'document-container',
                    documentPreview: 'document-preview',
                    previewContainer: 'preview-container',
                    previewStatus: 'preview-status',
                    previewStatusText: 'preview-status-text',
                    multiOrderToggle: 'multi-order-toggle',
                    displayModeSelector: 'display-mode-selector',
                    orderTabs: 'order-tabs',
                    currentOrderDisplay: 'current-order-display'
                };

                const elementId = elementMap[elementName];
                if (elementId) {
                    this[elementName] = document.getElementById(elementId);
                    return !!this[elementName];
                } else if (elementName === 'exportButtons') {
                    this.exportButtons = document.querySelector('.btn-group');
                    return !!this.exportButtons;
                } else if (elementName === 'modernExportUI') {
                    this.modernExportUI = document.querySelector('.modern-export-ui');
                    return !!this.modernExportUI;
                }

                return false;
            },

            /**
             * 获取缓存的DOM元素
             * @function get - 获取指定的DOM元素
             * @param {string} elementName - 元素名称
             * @returns {HTMLElement|null} DOM元素或null
             */
            get(elementName) {
                return this[elementName] || null;
            },

            /**
             * 验证缓存有效性
             * @function validateCache - 检查缓存的DOM元素是否仍然有效
             * @returns {Object} 验证结果
             */
            validateCache() {
                const invalidElements = [];
                const validElements = [];

                for (const key in this) {
                    if (typeof this[key] !== 'function' && this[key] && this[key].nodeType === Node.ELEMENT_NODE) {
                        if (document.contains(this[key])) {
                            validElements.push(key);
                        } else {
                            invalidElements.push(key);
                        }
                    }
                }

                if (invalidElements.length > 0) {
                    console.warn('⚠️ 发现失效的DOM缓存元素:', invalidElements);
                    this.initCache(); // 重新初始化缓存
                }

                return {
                    valid: validElements.length,
                    invalid: invalidElements.length,
                    invalidElements: invalidElements
                };
            },

            /**
             * 强制刷新DOM缓存
             * @function forceRefreshCache - 强制重新缓存所有DOM元素
             */
            forceRefreshCache() {
                console.log('🔄 DOM缓存已强制刷新');
                this.initCache();
            }
        };

        /**
         * 货币配置对象
         * @description 存储不同货币的显示信息和格式
         */
        const CurrencyConfig = {
            'MYR': {
                symbol: 'RM',
                name: '马来西亚令吉',
                englishName: 'Malaysian Ringgit',
                code: 'MYR'
            },
            'CNY': {
                symbol: '¥',
                name: '人民币',
                englishName: 'Chinese Yuan',
                code: 'CNY'
            }
        };



        /**
         * 公司信息配置
         * @description 存储不同公司的基本信息
         */
        const CompanyInfo = {
            'gomyhire': {
                name: 'GoMyHire Travel Sdn Bhd',
                address: 'Kuala Lumpur, Malaysia',
                phone: '+60 3-1234 5678',
                email: '<EMAIL>'
            },
            'sky-mirror': {
                name: 'Sky Mirror World Tour',
                address: 'Selangor, Malaysia',
                phone: '+60 3-8765 4321',
                email: '<EMAIL>'
            }
        };
        // #endregion

        // #region 工具函数
        /**
         * 性能优化的防抖函数管理器
         * @description 提供统一的防抖函数管理，避免重复创建，提升性能
         */
        const DebounceManager = {
            timers: new Map(),

            /**
             * 防抖函数
             * @function debounce - 防抖函数，延迟执行
             * @param {Function} func - 要执行的函数
             * @param {number} wait - 延迟时间（毫秒）
             * @param {string} key - 防抖键名，用于管理多个防抖函数
             * @returns {Function} 防抖后的函数
             */
            debounce(func, wait, key = 'default') {
                return (...args) => {
                    if (this.timers.has(key)) {
                        clearTimeout(this.timers.get(key));
                    }

                    const timer = setTimeout(() => {
                        this.timers.delete(key);
                        try {
                            func(...args);
                        } catch (error) {
                            console.error('防抖函数执行错误:', error);
                        }
                    }, wait);

                    this.timers.set(key, timer);
                };
            },

            /**
             * 清除指定的防抖定时器
             * @function clear - 清除指定键名的防抖定时器
             * @param {string} key - 防抖键名
             */
            clear(key) {
                if (this.timers.has(key)) {
                    clearTimeout(this.timers.get(key));
                    this.timers.delete(key);
                }
            },

            /**
             * 清除所有防抖定时器
             * @function clearAll - 清除所有防抖定时器
             */
            clearAll() {
                this.timers.forEach(timer => clearTimeout(timer));
                this.timers.clear();
            }
        };

        /**
         * 兼容性防抖函数已移除
         * @deprecated 请使用 DebounceManager.debounce() 或 safeDebouncedUpdatePreview()
         * @note 为了提高性能和避免重复创建，统一使用 DebounceManager 管理防抖函数
         */

        /**
         * 生成唯一单据号码
         * @function generateDocumentNumber - 生成基于时间戳的唯一单据号码
         * @param {string} type - 文档类型 ('invoice' 或 'receipt')
         * @returns {string} 格式化的单据号码
         */
        function generateDocumentNumber(type) {
            const date = new Date();
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            const prefix = type === 'invoice' ? 'INV' : 'RCP';
            return `${prefix}${year}${month}${day}${hours}${minutes}${seconds}`;
        }

        /**
         * 格式化金额显示
         * @function formatCurrency - 将数字格式化为货币显示格式
         * @param {number} amount - 金额数值
         * @param {boolean} withSymbol - 是否包含货币符号
         * @returns {string} 格式化后的金额字符串
         */
        function formatCurrency(amount, withSymbol = false) {
            const formattedAmount = parseFloat(amount || 0).toFixed(2);
            if (withSymbol) {
                const currency = CurrencyConfig[AppConfig.currentCurrency];
                return `${currency.symbol} ${formattedAmount}`;
            }
            return formattedAmount;
        }

        /**
         * 获取当前货币符号
         * @function getCurrentCurrencySymbol - 获取当前选中货币的符号
         * @returns {string} 货币符号
         */
        function getCurrentCurrencySymbol() {
            return CurrencyConfig[AppConfig.currentCurrency].symbol;
        }

        /**
         * 切换货币类型
         * @function switchCurrency - 切换货币类型并更新显示
         * @param {string} currencyCode - 货币代码 ('MYR' 或 'CNY')
         */
        function switchCurrency(currencyCode) {
            if (CurrencyConfig[currencyCode]) {
                AppConfig.currentCurrency = currencyCode;

                // 保存到localStorage
                localStorage.setItem('smartoffice_currency', currencyCode);

                // 更新所有金额显示
                updateAllCurrencyDisplays();

                // 如果启用自动预览，更新预览
                if (AppConfig.autoPreview) {
                    safeDebouncedUpdatePreview();
                }

                console.log(`货币已切换为: ${CurrencyConfig[currencyCode].name} (${currencyCode})`);
            }
        }

        /**
         * 更新所有货币显示
         * @function updateAllCurrencyDisplays - 更新页面上所有的货币符号显示
         */
        function updateAllCurrencyDisplays() {
            const symbol = getCurrentCurrencySymbol();

            // 更新总金额显示
            const totalAmountContainer = document.querySelector('.total-amount-display');
            if (totalAmountContainer && DOMCache.totalAmount) {
                const currentAmount = DOMCache.totalAmount.textContent;
                totalAmountContainer.innerHTML = `${symbol} <span id="total-amount">${currentAmount}</span>`;
                // 重新缓存更新后的元素
                DOMCache.refreshCache('totalAmount');
            }
        }

        /**
         * 计算项目总金额
         * @function calculateItemAmount - 计算单个项目的总金额
         * @param {number} quantity - 数量
         * @param {number} price - 单价
         * @returns {number} 总金额
         */
        function calculateItemAmount(quantity, price) {
            return (parseFloat(quantity || 0) * parseFloat(price || 0));
        }

        /**
         * 计算所有项目的总金额
         * @function calculateTotalAmount - 计算所有项目的总金额
         * @returns {number} 总金额
         */
        function calculateTotalAmount() {
            let total = 0;

            console.log(`💰 开始计算总金额 - 模式状态:`, {
                多订单模式: AppConfig.multiOrderMode,
                显示模式: AppConfig.displayMode,
                订单数据长度: AppConfig.multiOrderData ? AppConfig.multiOrderData.length : 0,
                当前订单索引: AppConfig.currentOrderIndex
            });

            // 检查是否为多订单合并模式
            if (AppConfig.multiOrderMode && AppConfig.displayMode === 'combined') {
                // 合并模式：从订单数据中计算总金额
                if (AppConfig.multiOrderData && AppConfig.multiOrderData.length > 0) {
                    AppConfig.multiOrderData.forEach((order, orderIndex) => {
                        if (order.items && order.items.length > 0) {
                            order.items.forEach((item, itemIndex) => {
                                const itemAmount = item.amount || 0;
                                total += itemAmount;
                                console.log(`💰 订单${orderIndex + 1}-项目${itemIndex + 1}: ${item.description} = ${formatCurrency(itemAmount)}`);
                            });
                        }
                    });
                    console.log(`💰 合并模式总金额计算完成: ${formatCurrency(total)} (来自${AppConfig.multiOrderData.length}个订单)`);
                } else {
                    console.warn('⚠️ 合并模式但无订单数据，回退到表格计算');
                    total = calculateTotalFromTable();
                }
            } else {
                // 单订单模式或分别显示模式：从表格输入框计算
                total = calculateTotalFromTable();
            }

            console.log(`💰 最终总金额: ${formatCurrency(total)}`);
            return total;
        }

        /**
         * 从表格计算总金额
         * @function calculateTotalFromTable - 从表格输入框计算总金额
         * @returns {number} 总金额
         */
        function calculateTotalFromTable() {
            let total = 0;
            // 优先使用缓存的tbody元素
            const tbody = DOMCache.itemsTbody || document.getElementById('items-tbody');
            const rows = tbody ? tbody.querySelectorAll('tr') : [];

            console.log(`💰 从表格计算总金额 - 表格行数: ${rows.length}`);

            rows.forEach((row, index) => {
                const quantityInput = row.querySelector('.item-quantity');
                const priceInput = row.querySelector('.item-price');
                const descriptionInput = row.querySelector('.item-description');

                // 早期返回：检查必要元素
                if (!quantityInput || !priceInput || !descriptionInput) {
                    console.warn(`⚠️ 表格行${index + 1}: 缺少必要的输入框元素`);
                    return;
                }

                const quantity = parseFloat(quantityInput.value || 0);
                const price = parseFloat(priceInput.value || 0);
                const description = descriptionInput.value.trim();

                // 早期返回：检查有效数据
                if (!description || quantity <= 0 || price < 0) {
                    console.log(`⚠️ 表格行${index + 1}: 跳过空项目或无效数据 (描述: "${description}", 数量: ${quantity}, 价格: ${price})`);
                    return;
                }

                const itemAmount = calculateItemAmount(quantity, price);
                total += itemAmount;
                console.log(`💰 表格行${index + 1}: ${description} (${quantity} × ${price}) = ${formatCurrency(itemAmount)}`);
            });

            console.log(`💰 表格模式总金额计算完成: ${formatCurrency(total)}`);
            return total;
        }

        /**
         * 更新项目金额显示
         * @function updateItemAmount - 更新单个项目的金额显示
         * @param {HTMLElement} row - 项目行元素
         */
        function updateItemAmount(row) {
            try {
                const quantity = parseFloat(row.querySelector('.item-quantity').value || 0);
                const price = parseFloat(row.querySelector('.item-price').value || 0);
                const amount = calculateItemAmount(quantity, price);

                row.querySelector('.item-amount').textContent = formatCurrency(amount);

                // 安全地更新总金额（使用防抖）
                setTimeout(() => {
                    if (!isUpdatingTotal) {
                        updateTotalAmount();
                    }
                }, 100);
            } catch (error) {
                console.error('❌ updateItemAmount错误:', error);
            }
        }

        // 防止updateTotalAmount无限循环的标记
        let isUpdatingTotal = false;

        /**
         * 更新总金额显示
         * @function updateTotalAmount - 更新总金额显示
         */
        function updateTotalAmount() {
            if (isUpdatingTotal) {
                console.log('⚠️ updateTotalAmount正在执行中，跳过重复调用');
                return;
            }

            isUpdatingTotal = true;
            try {
                const total = calculateTotalAmount();
                if (DOMCache.totalAmount) {
                    DOMCache.totalAmount.textContent = formatCurrency(total);
                }

                // 移除自动预览更新调用，避免循环调用
                // 总金额更新不应该触发预览更新，预览更新应该由表单输入事件触发
            } catch (error) {
                console.error('❌ updateTotalAmount错误:', error);
            } finally {
                isUpdatingTotal = false;
            }
        }

        /**
         * HTML安全转义函数
         * @function escapeHtml - 转义HTML特殊字符，确保内容安全显示
         * @param {string} text - 需要转义的文本
         * @returns {string} 转义后的安全文本
         */
        function escapeHtml(text) {
            if (!text) return '';
            return String(text)
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;')
                .replace(/\//g, '&#x2F;');
        }

        /**
         * 强制刷新DOM缓存
         * @function refreshDOMCache - 强制更新DOM元素缓存，确保数据一致性
         */
        function refreshDOMCache() {
            DOMCache.documentType = document.getElementById('document-type');
            DOMCache.documentNumber = document.getElementById('document-number');
            DOMCache.customerName = document.getElementById('customer-name');
            DOMCache.customerPhone = document.getElementById('customer-phone');
            DOMCache.customerEmail = document.getElementById('customer-email');
            DOMCache.notes = document.getElementById('notes');
            DOMCache.itemsTbody = document.querySelector('#items-tbody');
            DOMCache.totalAmount = document.getElementById('total-amount');

            console.log('🔄 DOM缓存已强制刷新');
        }

        /**
         * 收集表单数据（包含新增字段）- 增强版
         * @function collectFormData - 收集所有表单输入数据，确保数据一致性和安全性
         * @returns {Object} 表单数据对象
         */
        function collectFormData() {
            // 强制刷新DOM缓存，确保数据一致性
            refreshDOMCache();

            const items = [];
            // 直接从DOM获取最新数据，不依赖缓存
            const rows = document.querySelectorAll('#items-tbody tr');

            console.log(`📊 收集表单数据 - 模式: ${AppConfig.multiOrderMode ? '多订单' : '单订单'}, 显示: ${AppConfig.displayMode || '标准'}, 行数: ${rows.length}`);

            rows.forEach((row, index) => {
                const descriptionElement = row.querySelector('.item-description');
                const quantityElement = row.querySelector('.item-quantity');
                const priceElement = row.querySelector('.item-price');

                if (!descriptionElement || !quantityElement || !priceElement) {
                    console.warn(`⚠️ 项目 ${index + 1} 缺少必要的输入元素`);
                    return;
                }

                const description = descriptionElement.value.trim();
                const quantity = parseFloat(quantityElement.value || 0);
                const price = parseFloat(priceElement.value || 0);

                console.log(`📝 处理项目 ${index + 1}:`, {
                    原始描述: description,
                    安全描述: escapeHtml(description),
                    数量: quantity,
                    价格: price
                });

                if (description && quantity > 0 && price >= 0) {
                    items.push({
                        description: description, // 保持原始数据用于计算
                        safeDescription: escapeHtml(description), // 安全版本用于显示
                        quantity,
                        price,
                        amount: calculateItemAmount(quantity, price)
                    });
                }
            });

            // 直接从DOM获取最新值，不依赖缓存
            const docType = document.getElementById('document-type')?.value || 'invoice';
            const total = calculateTotalAmount();

            // 直接从DOM获取所有字段的最新值，确保数据一致性
            const documentNumber = document.getElementById('document-number')?.value.trim() || generateDocumentNumber(docType);
            const customerName = document.getElementById('customer-name')?.value.trim() || '';
            const customerPhone = document.getElementById('customer-phone')?.value.trim() || '';
            const customerEmail = document.getElementById('customer-email')?.value.trim() || '';
            const companyName = document.getElementById('company-name')?.value.trim() || '';
            const taxId = document.getElementById('tax-id')?.value.trim() || '';
            const companyAddress = document.getElementById('company-address')?.value.trim() || '';
            const companyPhone = document.getElementById('company-phone')?.value.trim() || '';
            const contactPerson = document.getElementById('contact-person')?.value.trim() || '';
            const channel = document.getElementById('channel')?.value.trim() || '';
            const notes = document.getElementById('notes')?.value.trim() || '';

            const data = {
                documentType: docType,
                documentNumber: documentNumber,
                safeDocumentNumber: escapeHtml(documentNumber),
                date: document.getElementById('document-date')?.value || new Date().toISOString().split('T')[0],

                // 公司信息 - 原始数据和安全版本
                companyName: companyName,
                safeCompanyName: escapeHtml(companyName),
                taxId: taxId,
                safeTaxId: escapeHtml(taxId),
                companyAddress: companyAddress,
                safeCompanyAddress: escapeHtml(companyAddress),
                companyPhone: companyPhone,
                safeCompanyPhone: escapeHtml(companyPhone),
                contactPerson: contactPerson,
                safeContactPerson: escapeHtml(contactPerson),

                // 客户信息 - 原始数据和安全版本
                customerName: customerName,
                safeCustomerName: escapeHtml(customerName),
                channel: channel,
                safeChannel: escapeHtml(channel),
                customerPhone: customerPhone,
                safeCustomerPhone: escapeHtml(customerPhone),
                customerEmail: customerEmail,
                safeCustomerEmail: escapeHtml(customerEmail),

                items: items,
                total: total,
                notes: notes,
                safeNotes: escapeHtml(notes)
            };

            console.log(`📊 表单数据收集完成 - 项目数: ${items.length}, 总金额: ${formatCurrency(total)}`);
            return data;
        }

        /**
         * 控制公司信息字段显示
         * @function toggleCompanyFields - 根据文档类型显示或隐藏公司信息字段（增强版：支持数据清空）
         * @param {string} documentType - 文档类型 ('invoice' 或 'receipt')
         */
        function toggleCompanyFields(documentType) {
            const companyFields = [
                'company-name',
                'tax-id',
                'company-address',
                'company-phone',
                'contact-person'
            ];

            companyFields.forEach(fieldId => {
                const element = SafeDOM.get(fieldId);
                const fieldGroup = element?.closest('.form-group');
                if (fieldGroup) {
                    if (documentType === 'invoice') {
                        fieldGroup.style.display = 'block';
                        fieldGroup.classList.remove('hidden');
                    } else {
                        fieldGroup.style.display = 'none';
                        fieldGroup.classList.add('hidden');

                        // 切换到收据模式时，清空公司信息字段
                        if (element && element.value) {
                            element.value = '';
                            console.log(`🧹 已清空字段: ${fieldId}`);
                        }
                    }
                }
            });

            // 对于form-row中的字段，需要特殊处理
            const taxIdElement = SafeDOM.get('tax-id');
            const phoneElement = SafeDOM.get('company-phone');
            const taxIdGroup = taxIdElement?.closest('.form-group');
            const phoneGroup = phoneElement?.closest('.form-group');

            if (taxIdGroup && phoneGroup) {
                const formRow = taxIdGroup.closest('.form-row');
                if (formRow) {
                    if (documentType === 'invoice') {
                        formRow.style.display = 'grid';
                        formRow.classList.remove('hidden');
                    } else {
                        formRow.style.display = 'none';
                        formRow.classList.add('hidden');

                        // 切换到收据模式时，清空form-row中的公司信息字段
                        if (taxIdElement && taxIdElement.value) {
                            taxIdElement.value = '';
                            console.log('🧹 已清空字段: tax-id');
                        }
                        if (phoneElement && phoneElement.value) {
                            phoneElement.value = '';
                            console.log('🧹 已清空字段: company-phone');
                        }
                    }
                }
            }

            // 更新全局配置
            AppConfig.currentDocumentType = documentType;

            console.log(`📋 字段显示已更新: ${documentType === 'invoice' ? '显示' : '隐藏'}公司信息字段`);

            // 触发预览更新
            if (AppConfig.autoPreview && typeof safeDebouncedUpdatePreview === 'function') {
                console.log('🔄 触发预览更新以反映文档类型变化');
                safeDebouncedUpdatePreview();
            }
        }

        /**
         * AI智能填充功能模块
         * @description 集成Gemini AI进行智能表单填充
         */

        /**
         * 切换AI填充面板显示
         * @function toggleAIFillPanel - 显示或隐藏AI填充面板
         */
        function toggleAIFillPanel() {
            const panel = DOMCache.aiFillPanel || SafeDOM.get('ai-fill-panel');
            const btn = DOMCache.aiFillBtn || SafeDOM.get('ai-fill-btn');

            if (panel && btn) {
                if (panel.classList.contains('hidden')) {
                    panel.classList.remove('hidden');
                    btn.textContent = '收起';
                } else {
                    panel.classList.add('hidden');
                    btn.textContent = '展开';
                }
            } else {
                console.warn('⚠️ AI填充面板或按钮元素未找到', {
                    panel: !!panel,
                    btn: !!btn
                });
            }
        }



        /**
         * 显示AI处理状态
         * @function showAIStatus - 显示AI处理状态信息
         * @param {string} message - 状态消息
         * @param {string} type - 状态类型 ('info', 'success', 'error')
         */
        function showAIStatus(message, type = 'info') {
            const statusDiv = DOMCache.aiStatus || document.getElementById('ai-status');
            if (statusDiv) {
                statusDiv.classList.remove('hidden');
                statusDiv.textContent = message;

                // 清除之前的状态类
                statusDiv.classList.remove('success', 'error', 'info');

                // 设置状态样式 - 使用CSS类而不是内联样式
                statusDiv.className = `ai-status ${type}`;
            }
        }

        /**
         * 隐藏AI状态
         * @function hideAIStatus - 隐藏AI状态显示
         */
        function hideAIStatus() {
            const statusDiv = DOMCache.aiStatus || document.getElementById('ai-status');
            if (statusDiv) {
                statusDiv.classList.add('hidden');
            }
        }

        /**
         * 清空AI输入
         * @function clearAIInput - 清空AI智能填充的输入内容
         */
        function clearAIInput() {
            const textInput = DOMCache.aiTextInput || document.getElementById('ai-text-input');
            const imageInput = DOMCache.aiImageInput || document.getElementById('ai-image-input');

            if (textInput) {
                textInput.value = '';
            }

            if (imageInput) {
                imageInput.value = '';
            }

            hideAIStatus();
            console.log('✅ AI输入已清空');
        }

        // #region 开发环境测试函数 - 生产环境中将被移除
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' || window.DEBUG_MODE) {
            /**
             * 测试AI智能填充功能
             * @function testAIFill - 测试AI智能填充功能是否正常工作
             */
            function testAIFill() {
            console.log('🧪 开始测试AI智能填充功能...');

            // 检查DOM元素
            const textInput = document.getElementById('ai-text-input');
            const imageInput = document.getElementById('ai-image-input');
            const processBtn = document.getElementById('ai-process-text');
            const statusDiv = document.getElementById('ai-status');

            console.log('🔍 DOM元素检查:', {
                文本输入框: !!textInput,
                图片输入框: !!imageInput,
                处理按钮: !!processBtn,
                状态显示: !!statusDiv
            });

            // 检查DOMCache
            console.log('🔍 DOMCache检查:', {
                aiTextInput: !!DOMCache.aiTextInput,
                aiImageInput: !!DOMCache.aiImageInput,
                aiProcessText: !!DOMCache.aiProcessText,
                aiStatus: !!DOMCache.aiStatus
            });

            // 检查关键函数
            console.log('🔍 函数检查:', {
                processAIFill: typeof processAIFill,
                showAIStatus: typeof showAIStatus,
                toggleAIFillPanel: typeof toggleAIFillPanel,
                AppConfig: typeof AppConfig,
                geminiApiKey: !!AppConfig.geminiApiKey
            });

            // 测试状态显示
            if (statusDiv) {
                showAIStatus('测试消息', 'info');
                setTimeout(() => {
                    hideAIStatus();
                }, 2000);
            }

            console.log('✅ AI智能填充功能测试完成');
        }

            // 将测试函数添加到全局作用域，方便调试
            window.testAIFill = testAIFill;

            /**
             * 测试动态字段显示功能
             * @function testDynamicFieldDisplay - 测试发票/收据模式下的字段显示逻辑
             */
            function testDynamicFieldDisplay() {
            console.log('🧪 开始测试动态字段显示功能...');

            const docTypeSelect = document.getElementById('document-type');
            const companyFields = [
                'company-name',
                'tax-id',
                'company-address',
                'company-phone',
                'contact-person'
            ];

            // 测试数据
            const testCompanyData = {
                companyName: '测试公司 Test Company',
                taxId: 'TEST123456',
                companyAddress: '测试地址 Test Address',
                companyPhone: '+60 12-345-6789',
                contactPerson: '测试联系人 Test Contact'
            };

            console.log('📋 测试1: 切换到发票模式');
            docTypeSelect.value = 'invoice';
            toggleCompanyFields('invoice');

            // 填充测试数据
            fillCompanyInfo(testCompanyData);

            // 检查字段是否可见且已填充
            let invoiceTestResults = {};
            companyFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                const fieldGroup = element?.closest('.form-group');
                invoiceTestResults[fieldId] = {
                    visible: fieldGroup ? !fieldGroup.classList.contains('hidden') : false,
                    filled: element ? element.value !== '' : false,
                    value: element ? element.value : null
                };
            });

            console.log('📊 发票模式测试结果:', invoiceTestResults);

            console.log('📋 测试2: 切换到收据模式');
            docTypeSelect.value = 'receipt';
            toggleCompanyFields('receipt');

            // 检查字段是否隐藏且已清空
            let receiptTestResults = {};
            companyFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                const fieldGroup = element?.closest('.form-group');
                receiptTestResults[fieldId] = {
                    hidden: fieldGroup ? fieldGroup.classList.contains('hidden') : false,
                    cleared: element ? element.value === '' : true,
                    value: element ? element.value : null
                };
            });

            console.log('📊 收据模式测试结果:', receiptTestResults);

            console.log('📋 测试3: 收据模式下的AI填充测试');
            fillCompanyInfo(testCompanyData);

            let aiFillTestResults = {};
            companyFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                aiFillTestResults[fieldId] = {
                    shouldBeEmpty: element ? element.value === '' : true,
                    value: element ? element.value : null
                };
            });

            console.log('📊 收据模式AI填充测试结果:', aiFillTestResults);

            // 生成测试报告
            const allInvoiceFieldsVisible = Object.values(invoiceTestResults).every(result => result.visible);
            const allInvoiceFieldsFilled = Object.values(invoiceTestResults).every(result => result.filled);
            const allReceiptFieldsHidden = Object.values(receiptTestResults).every(result => result.hidden);
            const allReceiptFieldsCleared = Object.values(receiptTestResults).every(result => result.cleared);
            const allAIFillFieldsEmpty = Object.values(aiFillTestResults).every(result => result.shouldBeEmpty);

            const testReport = {
                发票模式字段可见性: allInvoiceFieldsVisible ? '✅ 通过' : '❌ 失败',
                发票模式字段填充: allInvoiceFieldsFilled ? '✅ 通过' : '❌ 失败',
                收据模式字段隐藏: allReceiptFieldsHidden ? '✅ 通过' : '❌ 失败',
                收据模式字段清空: allReceiptFieldsCleared ? '✅ 通过' : '❌ 失败',
                收据模式AI填充跳过: allAIFillFieldsEmpty ? '✅ 通过' : '❌ 失败',
                总体测试结果: (allInvoiceFieldsVisible && allInvoiceFieldsFilled &&
                             allReceiptFieldsHidden && allReceiptFieldsCleared &&
                             allAIFillFieldsEmpty) ? '✅ 全部通过' : '❌ 部分失败'
            };

            console.log('📋 动态字段显示功能测试报告:');
            console.table(testReport);

            return testReport;
        }

            // 将测试函数添加到全局作用域
            window.testDynamicFieldDisplay = testDynamicFieldDisplay;

            /**
             * 测试页脚定位一致性（修正版：验证bottom: 0定位）
             * @function testFooterPositioning - 测试预览和导出模式下的页脚是否正确贴合底部边缘
             */
            function testFooterPositioning() {
            console.log('🧪 开始测试页脚定位一致性（验证bottom: 0定位）...');

            const testResults = {
                预览模式: {},
                导出模式: {},
                一致性检查: {}
            };

            // 测试预览模式下的页脚定位
            console.log('📋 测试1: 预览模式页脚定位（验证是否贴合底部边缘）');
            const previewContainer = document.getElementById('document-container');
            const previewFooter = previewContainer?.querySelector('.unified-document-footer, .company-footer-image-container');

            if (previewFooter) {
                const previewStyle = window.getComputedStyle(previewFooter);
                const previewRect = previewFooter.getBoundingClientRect();
                const containerRect = previewContainer.getBoundingClientRect();

                // 计算页脚底部与容器底部的距离（应该为0或接近0）
                const bottomGap = containerRect.bottom - previewRect.bottom;
                const isAtBottom = Math.abs(bottomGap) < 2; // 允许2px误差

                testResults.预览模式 = {
                    position: previewStyle.position,
                    bottom: previewStyle.bottom,
                    height: previewStyle.height,
                    实际高度: previewRect.height,
                    底部间距: bottomGap,
                    z_index: previewStyle.zIndex,
                    是否贴合底部: isAtBottom,
                    CSS_bottom值: previewStyle.bottom,
                    容器高度: containerRect.height,
                    页脚顶部位置: previewRect.top - containerRect.top,
                    预期页脚顶部: containerRect.height - 110 // A4高度减去110px页脚高度
                };
            } else {
                testResults.预览模式 = { 错误: '未找到页脚元素' };
            }

            console.log('📊 预览模式页脚定位结果:', testResults.预览模式);

            // 模拟导出模式测试
            console.log('📋 测试2: 导出模式页脚定位（验证是否贴合底部边缘）');
            if (previewContainer && previewFooter) {
                // 临时添加导出模式类
                previewContainer.classList.add('export-mode');

                // 等待样式应用
                setTimeout(() => {
                    const exportStyle = window.getComputedStyle(previewFooter);
                    const exportRect = previewFooter.getBoundingClientRect();
                    const exportContainerRect = previewContainer.getBoundingClientRect();

                    // 计算页脚底部与容器底部的距离（应该为0或接近0）
                    const exportBottomGap = exportContainerRect.bottom - exportRect.bottom;
                    const exportIsAtBottom = Math.abs(exportBottomGap) < 2; // 允许2px误差

                    testResults.导出模式 = {
                        position: exportStyle.position,
                        bottom: exportStyle.bottom,
                        height: exportStyle.height,
                        实际高度: exportRect.height,
                        底部间距: exportBottomGap,
                        z_index: exportStyle.zIndex,
                        是否贴合底部: exportIsAtBottom,
                        CSS_bottom值: exportStyle.bottom,
                        容器高度: exportContainerRect.height,
                        页脚顶部位置: exportRect.top - exportContainerRect.top,
                        预期页脚顶部: exportContainerRect.height - 110 // A4高度减去110px页脚高度
                    };

                    console.log('📊 导出模式页脚定位结果:', testResults.导出模式);

                    // 一致性检查（重点验证bottom: 0定位）
                    testResults.一致性检查 = {
                        position一致: testResults.预览模式.position === testResults.导出模式.position,
                        height一致: Math.abs(testResults.预览模式.实际高度 - testResults.导出模式.实际高度) < 2,
                        底部贴合一致: testResults.预览模式.是否贴合底部 === testResults.导出模式.是否贴合底部,
                        z_index一致: testResults.预览模式.z_index === testResults.导出模式.z_index,
                        CSS_bottom一致: testResults.预览模式.CSS_bottom值 === testResults.导出模式.CSS_bottom值,
                        底部间距合理: Math.abs(testResults.预览模式.底部间距) < 2 && Math.abs(testResults.导出模式.底部间距) < 2
                    };

                    const allConsistent = Object.values(testResults.一致性检查).every(result => result === true);

                    const finalReport = {
                        预览模式页脚贴合底部: testResults.预览模式.是否贴合底部 ? '✅ 正确贴合' : '❌ 未贴合底部',
                        导出模式页脚贴合底部: testResults.导出模式.是否贴合底部 ? '✅ 正确贴合' : '❌ 未贴合底部',
                        预览模式底部间距: `${testResults.预览模式.底部间距?.toFixed(1) || 'N/A'}px`,
                        导出模式底部间距: `${testResults.导出模式.底部间距?.toFixed(1) || 'N/A'}px`,
                        position属性一致性: testResults.一致性检查.position一致 ? '✅ 一致' : '❌ 不一致',
                        高度一致性: testResults.一致性检查.height一致 ? '✅ 一致' : '❌ 不一致',
                        底部贴合一致性: testResults.一致性检查.底部贴合一致 ? '✅ 一致' : '❌ 不一致',
                        CSS_bottom值一致性: testResults.一致性检查.CSS_bottom一致 ? '✅ 一致' : '❌ 不一致',
                        总体测试结果: allConsistent && testResults.预览模式.是否贴合底部 && testResults.导出模式.是否贴合底部 ? '✅ 完全通过' : '❌ 存在问题'
                    };

                    console.log('📋 页脚定位一致性测试报告:');
                    console.table(finalReport);

                    // 移除导出模式类
                    previewContainer.classList.remove('export-mode');

                    return finalReport;
                }, 100);
            } else {
                testResults.导出模式 = { 错误: '未找到容器或页脚元素' };
                console.log('❌ 导出模式测试失败:', testResults.导出模式);
            }

            return testResults;
        }

            // 将测试函数添加到全局作用域
            window.testFooterPositioning = testFooterPositioning;

            /**
             * 深度诊断页脚定位问题
             * @function deepDiagnoseFooterPositioning - 深度分析页脚定位的所有相关因素
             */
            function deepDiagnoseFooterPositioning() {
            console.log('🔍 开始深度诊断页脚定位问题...');

            const diagnosis = {
                DOM结构分析: {},
                CSS样式分析: {},
                定位计算分析: {},
                容器分析: {},
                问题识别: [],
                修复建议: []
            };

            // 1. DOM结构分析
            console.log('📋 步骤1: DOM结构分析');
            const container = document.getElementById('document-container');
            const footer = container?.querySelector('.unified-document-footer, .company-footer-image-container');

            diagnosis.DOM结构分析 = {
                容器存在: !!container,
                页脚存在: !!footer,
                容器ID: container?.id || 'N/A',
                页脚类名: footer?.className || 'N/A',
                页脚标签: footer?.tagName || 'N/A',
                父容器: footer?.parentElement?.tagName || 'N/A',
                兄弟元素数量: footer?.parentElement?.children?.length || 0
            };

            if (!container || !footer) {
                diagnosis.问题识别.push('❌ 关键DOM元素缺失');
                console.error('❌ 无法找到容器或页脚元素');
                return diagnosis;
            }

            // 2. CSS样式分析
            console.log('📋 步骤2: CSS样式分析');
            const footerStyle = window.getComputedStyle(footer);
            const containerStyle = window.getComputedStyle(container);

            diagnosis.CSS样式分析 = {
                页脚样式: {
                    position: footerStyle.position,
                    bottom: footerStyle.bottom,
                    left: footerStyle.left,
                    right: footerStyle.right,
                    height: footerStyle.height,
                    width: footerStyle.width,
                    zIndex: footerStyle.zIndex,
                    display: footerStyle.display,
                    backgroundColor: footerStyle.backgroundColor,
                    transform: footerStyle.transform,
                    margin: footerStyle.margin,
                    padding: footerStyle.padding
                },
                容器样式: {
                    position: containerStyle.position,
                    height: containerStyle.height,
                    minHeight: containerStyle.minHeight,
                    paddingBottom: containerStyle.paddingBottom,
                    overflow: containerStyle.overflow,
                    display: containerStyle.display
                }
            };

            // 检查CSS问题
            if (footerStyle.position !== 'absolute') {
                diagnosis.问题识别.push('❌ 页脚position不是absolute');
            }
            if (footerStyle.bottom !== '0px') {
                diagnosis.问题识别.push(`❌ 页脚bottom值不是0px，当前为: ${footerStyle.bottom}`);
            }

            // 3. 定位计算分析
            console.log('📋 步骤3: 定位计算分析');
            const footerRect = footer.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();

            const bottomGap = containerRect.bottom - footerRect.bottom;
            const topGap = footerRect.top - containerRect.top;
            const expectedTopPosition = containerRect.height - 110; // 期望的页脚顶部位置

            diagnosis.定位计算分析 = {
                容器尺寸: {
                    宽度: containerRect.width,
                    高度: containerRect.height,
                    顶部: containerRect.top,
                    底部: containerRect.bottom
                },
                页脚尺寸: {
                    宽度: footerRect.width,
                    高度: footerRect.height,
                    顶部: footerRect.top,
                    底部: footerRect.bottom
                },
                位置关系: {
                    底部间距: bottomGap,
                    顶部间距: topGap,
                    期望顶部位置: expectedTopPosition,
                    实际顶部位置: topGap,
                    位置偏差: topGap - expectedTopPosition,
                    是否贴合底部: Math.abs(bottomGap) <= 2
                }
            };

            if (Math.abs(bottomGap) > 2) {
                diagnosis.问题识别.push(`❌ 页脚未贴合底部，间距: ${bottomGap.toFixed(1)}px`);
            }

            console.log('📊 诊断结果:', diagnosis);
            return diagnosis;
        }

            // 将深度诊断函数添加到全局作用域
            window.deepDiagnoseFooterPositioning = deepDiagnoseFooterPositioning;

            /**
             * AI填充数据清理测试工具
             * @description 测试AI填充功能的数据清理效果
             */
            const AIDataCleaningTester = {
            /**
             * 测试用例数据
             */
            testCases: [
                {
                    name: '包含司机信息的订单数据',
                    input: {
                        customerName: '张师傅',
                        customerPhone: '13812345678',
                        documentNumber: 'ORD123456',
                        notes: '司机：李师傅，车牌：京A12345，身份证：110101199001011234',
                        items: [
                            { description: '专车服务 - 司机李师傅提供', quantity: 1, price: 100 }
                        ]
                    },
                    expected: {
                        customerName: '', // 应该被过滤
                        customerPhone: '13812345678', // 有效电话保留
                        documentNumber: 'ORD123456', // 订单号保留
                        notes: '', // 司机信息应该被清理
                        items: [
                            { description: '专车服务 - [身份证号已隐藏]', quantity: 1, price: 100 }
                        ]
                    }
                },
                {
                    name: '正常客户订单数据',
                    input: {
                        customerName: '王小明',
                        customerPhone: '13987654321',
                        documentNumber: 'ORD789012',
                        notes: '客户要求：到达后电话联系',
                        items: [
                            { description: '快车服务', quantity: 1, price: 50 }
                        ]
                    },
                    expected: {
                        customerName: '王小明', // 正常客户名保留
                        customerPhone: '13987654321', // 有效电话保留
                        documentNumber: 'ORD789012', // 订单号保留
                        notes: '客户要求：到达后电话联系', // 正常备注保留
                        items: [
                            { description: '快车服务', quantity: 1, price: 50 }
                        ]
                    }
                },
                {
                    name: '混合信息数据',
                    input: {
                        customerName: '乘客李女士',
                        customerPhone: '13611111111',
                        documentNumber: 'ORD345678',
                        notes: '乘客李女士，司机张师傅，车牌沪B67890',
                        items: [
                            { description: '出租车服务 - 司机信息：张师傅', quantity: 1, price: 80 }
                        ]
                    },
                    expected: {
                        customerName: '乘客李女士', // 客户信息保留
                        customerPhone: '13611111111', // 有效电话保留
                        documentNumber: 'ORD345678', // 订单号保留
                        notes: '乘客李女士，司机张师傅，[车牌号已隐藏]', // 部分清理
                        items: [
                            { description: '出租车服务 - 司机信息：张师傅', quantity: 1, price: 80 }
                        ]
                    }
                }
            ],

            /**
             * 运行所有测试用例
             * @function runAllTests - 运行所有数据清理测试
             */
            runAllTests() {
                console.log('🧪 开始AI数据清理测试...');

                let passedTests = 0;
                let totalTests = this.testCases.length;

                this.testCases.forEach((testCase, index) => {
                    console.log(`\n📋 测试 ${index + 1}: ${testCase.name}`);

                    const result = DataCleaningValidator.cleanAIResults(testCase.input);
                    const passed = this.compareResults(result, testCase.expected, testCase.name);

                    if (passed) {
                        passedTests++;
                        console.log(`✅ 测试 ${index + 1} 通过`);
                    } else {
                        console.log(`❌ 测试 ${index + 1} 失败`);
                    }
                });

                const successRate = (passedTests / totalTests * 100).toFixed(1);
                console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过 (${successRate}%)`);

                if (passedTests === totalTests) {
                    console.log('🎉 所有测试通过！数据清理功能正常工作');
                } else {
                    console.log('⚠️ 部分测试失败，需要检查数据清理逻辑');
                }

                return { passedTests, totalTests, successRate };
            },

            /**
             * 比较测试结果
             * @function compareResults - 比较实际结果和期望结果
             * @param {Object} actual - 实际结果
             * @param {Object} expected - 期望结果
             * @param {string} testName - 测试名称
             * @returns {boolean} 是否匹配
             */
            compareResults(actual, expected, testName) {
                console.log(`🔍 ${testName} - 比较结果:`);
                console.log('实际结果:', actual);
                console.log('期望结果:', expected);

                let allMatch = true;

                // 检查客户姓名
                if (actual.customerName !== expected.customerName) {
                    console.log(`❌ 客户姓名不匹配: 实际="${actual.customerName}", 期望="${expected.customerName}"`);
                    allMatch = false;
                }

                // 检查客户电话
                if (actual.customerPhone !== expected.customerPhone) {
                    console.log(`❌ 客户电话不匹配: 实际="${actual.customerPhone}", 期望="${expected.customerPhone}"`);
                    allMatch = false;
                }

                // 检查订单号
                if (actual.documentNumber !== expected.documentNumber) {
                    console.log(`❌ 订单号不匹配: 实际="${actual.documentNumber}", 期望="${expected.documentNumber}"`);
                    allMatch = false;
                }

                // 检查备注（部分匹配，因为清理逻辑可能有差异）
                if (expected.notes === '' && actual.notes !== '') {
                    console.log(`⚠️ 备注应该被清空但未清空: 实际="${actual.notes}"`);
                    // 不算作失败，但给出警告
                }

                return allMatch;
            },

            /**
             * 测试特定数据
             * @function testSpecificData - 测试特定的数据清理
             * @param {Object} data - 要测试的数据
             */
            testSpecificData(data) {
                console.log('🧪 测试特定数据清理...');
                console.log('输入数据:', data);

                const cleaned = DataCleaningValidator.cleanAIResults(data);
                console.log('清理后数据:', cleaned);

                // 检查是否包含司机信息
                const hasDriverInfo = this.checkForDriverInfo(cleaned);
                if (hasDriverInfo.found) {
                    console.warn('⚠️ 清理后仍包含司机信息:', hasDriverInfo.details);
                } else {
                    console.log('✅ 未发现司机信息泄露');
                }

                return cleaned;
            },

            /**
             * 检查数据中是否包含司机信息
             * @function checkForDriverInfo - 检查数据中是否包含司机信息
             * @param {Object} data - 要检查的数据
             * @returns {Object} 检查结果
             */
            checkForDriverInfo(data) {
                const issues = [];

                if (data.customerName && DataCleaningValidator.containsDriverInfo(data.customerName)) {
                    issues.push(`客户姓名包含司机信息: ${data.customerName}`);
                }

                if (data.notes && DataCleaningValidator.containsDriverInfo(data.notes)) {
                    issues.push(`备注包含司机信息: ${data.notes}`);
                }

                if (data.items && Array.isArray(data.items)) {
                    data.items.forEach((item, index) => {
                        if (item.description && DataCleaningValidator.containsDriverInfo(item.description)) {
                            issues.push(`项目${index + 1}描述包含司机信息: ${item.description}`);
                        }
                    });
                }

                return {
                    found: issues.length > 0,
                    details: issues
                };
            }
        };

            // 将测试工具添加到全局作用域
            window.AIDataCleaningTester = AIDataCleaningTester;
            window.testAIDataCleaning = () => AIDataCleaningTester.runAllTests();

            /**
             * 项目合并测试工具
             * @description 专门测试AI填充结果的项目合并功能
             */
            function testItemMerging() {
            console.log('🧪 开始测试项目合并功能...');

            // 测试用例：您提供的订单数据
            const testCase = {
                customerName: 'Deden PURNAMAHADI',
                documentNumber: '102837',
                items: [
                    {
                        description: 'Airport Pickup (MH-724)',
                        quantity: 1,
                        price: 96.60
                    },
                    {
                        description: '7 Seater MPV (6 passengers, 2 luggages)',
                        quantity: 1,
                        price: 0.00
                    }
                ]
            };

            console.log('📋 原始数据:', testCase);

            // 使用数据清理器处理
            const cleanedData = DataCleaningValidator.cleanAIResults(testCase);

            console.log('📋 清理后数据:', cleanedData);

            // 验证结果
            if (cleanedData.items && cleanedData.items.length === 1) {
                console.log('✅ 项目合并成功！');
                console.log('📝 合并后的项目描述:', cleanedData.items[0].description);
                console.log('💰 价格:', cleanedData.items[0].price);
            } else {
                console.log('❌ 项目合并失败，仍有多个项目');
            }

            return cleanedData;
        }

            // 添加到全局作用域
            window.testItemMerging = testItemMerging;

            /**
             * 检查CSS样式冲突
             * @function checkCSSConflicts - 检查可能影响页脚定位的CSS样式冲突
             */
            function checkCSSConflicts() {
            console.log('🔍 检查CSS样式冲突...');

            const footer = document.querySelector('.unified-document-footer, .company-footer-image-container');
            if (!footer) {
                console.error('❌ 未找到页脚元素');
                return null;
            }

            const conflicts = {
                样式来源分析: {},
                优先级问题: [],
                覆盖样式: [],
                建议修复: []
            };

            // 获取所有应用到页脚的CSS规则
            const allRules = [];
            for (let sheet of document.styleSheets) {
                try {
                    for (let rule of sheet.cssRules || sheet.rules) {
                        if (rule.style && rule.selectorText) {
                            // 检查选择器是否匹配页脚元素
                            try {
                                if (footer.matches(rule.selectorText)) {
                                    allRules.push({
                                        selector: rule.selectorText,
                                        position: rule.style.position,
                                        bottom: rule.style.bottom,
                                        height: rule.style.height,
                                        zIndex: rule.style.zIndex,
                                        transform: rule.style.transform,
                                        important: rule.style.cssText.includes('!important'),
                                        sheet: sheet.href || 'inline'
                                    });
                                }
                            } catch (e) {
                                // 忽略无效的选择器
                            }
                        }
                    }
                } catch (e) {
                    // 忽略跨域样式表
                }
            }

            conflicts.样式来源分析 = {
                匹配规则数量: allRules.length,
                规则详情: allRules
            };

            // 检查内联样式
            const inlineStyle = {
                position: footer.style.position,
                bottom: footer.style.bottom,
                height: footer.style.height,
                zIndex: footer.style.zIndex,
                transform: footer.style.transform
            };

            conflicts.内联样式 = inlineStyle;

            // 检查计算后的样式
            const computedStyle = window.getComputedStyle(footer);
            conflicts.计算后样式 = {
                position: computedStyle.position,
                bottom: computedStyle.bottom,
                height: computedStyle.height,
                zIndex: computedStyle.zIndex,
                transform: computedStyle.transform
            };

            // 分析问题
            if (computedStyle.position !== 'absolute') {
                conflicts.优先级问题.push('position属性未正确应用为absolute');
            }
            if (computedStyle.bottom !== '0px') {
                conflicts.优先级问题.push(`bottom属性未正确应用为0px，当前为: ${computedStyle.bottom}`);
            }

            console.log('📊 CSS冲突分析结果:', conflicts);
            return conflicts;
        }

        /**
         * 强制修复页脚定位
         * @function forceFixFooterPosition - 强制修复页脚定位到底部边缘
         */
        function forceFixFooterPosition() {
            console.log('🔧 开始强制修复页脚定位...');

            const footer = document.querySelector('.unified-document-footer, .company-footer-image-container');
            const container = document.getElementById('document-container');

            if (!footer || !container) {
                console.error('❌ 未找到必要的DOM元素');
                return false;
            }

            // 强制设置页脚样式
            footer.style.setProperty('position', 'absolute', 'important');
            footer.style.setProperty('bottom', '0px', 'important');
            footer.style.setProperty('left', '0px', 'important');
            footer.style.setProperty('right', '0px', 'important');
            footer.style.setProperty('height', '110px', 'important');
            footer.style.setProperty('width', '100%', 'important');
            footer.style.setProperty('z-index', '100', 'important');
            footer.style.setProperty('background-color', 'white', 'important');
            footer.style.setProperty('display', 'flex', 'important');
            footer.style.setProperty('align-items', 'center', 'important');
            footer.style.setProperty('justify-content', 'center', 'important');
            footer.style.setProperty('box-sizing', 'border-box', 'important');
            footer.style.setProperty('margin', '0', 'important');
            footer.style.setProperty('padding', '5px', 'important');
            footer.style.setProperty('transform', 'none', 'important');

            // 确保容器有正确的定位上下文
            if (window.getComputedStyle(container).position === 'static') {
                container.style.setProperty('position', 'relative', 'important');
            }

            // 确保容器有足够的底部边距
            container.style.setProperty('padding-bottom', '125px', 'important'); // 110px + 15px

            console.log('✅ 页脚定位强制修复完成');

            // 验证修复结果
            setTimeout(() => {
                const footerRect = footer.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();
                const bottomGap = containerRect.bottom - footerRect.bottom;

                console.log('🔍 修复后验证:', {
                    底部间距: bottomGap + 'px',
                    是否贴合底部: Math.abs(bottomGap) <= 2,
                    页脚高度: footerRect.height + 'px',
                    position: window.getComputedStyle(footer).position,
                    bottom: window.getComputedStyle(footer).bottom
                });
            }, 100);

            return true;
        }

            // 将函数添加到全局作用域
            window.checkCSSConflicts = checkCSSConflicts;
            window.forceFixFooterPosition = forceFixFooterPosition;

            /**
             * 基础功能测试函数
             * @function testBasicFunctionality - 测试基础功能可用性
             */
            window.testBasicFunctionality = function() {
                console.log('⚙️ 测试基础功能...');

                try {
                    const results = {
                        escapeHtmlFunction: typeof escapeHtml === 'function',
                        collectFormDataFunction: typeof collectFormData === 'function',
                        previewModuleAvailable: typeof PreviewModule !== 'undefined',
                        modernExportSystemAvailable: typeof ModernExportSystem !== 'undefined',
                        html2canvasAvailable: typeof html2canvas !== 'undefined',
                        jsPDFAvailable: typeof window.jspdf !== 'undefined'
                    };

                    console.log('✅ 基础功能测试完成:', results);
                    return results;

                } catch (error) {
                    console.error('❌ 基础功能测试失败:', error);
                    return { error: error.message };
                }
            };

        /**
         * 处理AI智能填充
         * @function processAIFill - 处理AI智能填充请求
         */
        async function processAIFill() {
            console.log('🚀 processAIFill 函数被调用');

            // 确保DOM缓存是最新的
            if (!DOMCache.aiTextInput) {
                DOMCache.aiTextInput = document.getElementById('ai-text-input');
            }
            if (!DOMCache.aiImageInput) {
                DOMCache.aiImageInput = document.getElementById('ai-image-input');
            }

            const textInput = DOMCache.aiTextInput ? DOMCache.aiTextInput.value.trim() : '';
            const imageInput = DOMCache.aiImageInput ? DOMCache.aiImageInput.files[0] : null;

            console.log('📝 输入检查:', {
                文本内容: textInput ? `${textInput.substring(0, 50)}...` : '无',
                图片文件: imageInput ? imageInput.name : '无'
            });

            if (!textInput && !imageInput) {
                showAIStatus('请输入文本或上传图片', 'error');
                return;
            }

            const processBtn = DOMCache.aiProcessText || document.getElementById('ai-process-text');
            const originalText = processBtn ? processBtn.textContent : '分析填充';

            try {
                // 显示处理状态
                processBtn.textContent = '🔄 分析中... / Analyzing...';
                showAIStatus('正在连接Gemini AI进行智能分析... / Connecting to Gemini AI for analysis...', 'info');

                let analysisResult;

                if (imageInput) {
                    analysisResult = await processImageWithGemini(imageInput, textInput);
                } else {
                    analysisResult = await processTextWithGemini(textInput);
                }

                if (analysisResult) {
                    console.log('🎯 AI分析结果获取成功，开始应用到表单:', analysisResult);
                    await applyAIResults(analysisResult);
                    showAIStatus('分析完成，信息已填充', 'success');

                    // 添加数据填充验证
                    setTimeout(() => {
                        const verificationResults = {
                            documentNumber: document.getElementById('document-number').value,
                            customerName: document.getElementById('customer-name').value,
                            customerPhone: document.getElementById('customer-phone').value,
                            itemsCount: document.querySelectorAll('#items-tbody tr').length,
                            firstItemDescription: document.querySelector('#items-tbody tr .item-description')?.value || '',
                            totalAmount: document.getElementById('total-amount').textContent
                        };
                        console.log('🔍 数据填充验证结果:', verificationResults);
                    }, 1000);
                } else {
                    console.error('❌ AI分析结果为空或无效');
                    showAIStatus('AI分析未能提取有效信息，请检查输入内容 / AI analysis could not extract valid information', 'error');
                }

            } catch (error) {
                console.error('AI处理错误:', error);
                showAIStatus(`AI处理失败: ${error.message} / AI processing failed: ${error.message}`, 'error');
            } finally {
                processBtn.textContent = originalText;
            }
        }

        /**
         * Gemini AI服务类
         * @class GeminiService - 统一管理Gemini AI相关功能
         */
        class GeminiService {
            /**
             * 构建AI分析提示词
             * @function buildPrompt - 构建用于AI分析的提示词
             * @param {string} text - 要分析的文本
             * @returns {string} 构建好的提示词
             */
            static buildPrompt(text) {
                return `
请分析以下文本，提取相关的发票/收据信息，并以JSON格式返回。

【重要：数据清理和隐私保护规则】
在提取信息时，请严格遵守以下数据清理规则：

1. 司机信息过滤：
   - 自动识别并移除司机个人信息（姓名、电话、身份证号、驾驶证号等）
   - 过滤包含以下关键词的个人信息：司机、师傅、驾驶员、开车的、车主、代驾、网约车司机、滴滴司机、出租车司机、driver、chauffeur
   - 移除车牌号、驾驶证号等敏感信息

2. 非订单内容清理：
   - 过滤聊天记录：问候语、确认语句、感谢语句、时间位置相关对话
   - 移除系统通知：平台通知、订单状态更新、支付通知、评价提醒
   - 排除非结构化的对话内容

3. 客户信息优先保留：
   - 当无法区分司机信息和客户信息时，优先保留客户相关数据
   - 客户相关关键词：乘客、客户、用户、顾客、passenger、customer、user
   - 在客户信息上下文中提取姓名和联系方式

4. 数据验证规则：
   - 客户姓名不应包含司机相关词汇
   - 电话号码应为有效的中国手机号格式（1开头的11位数字）
   - 订单号应为合理的格式（字母+数字组合、纯数字等）

请提取以下字段（如果存在）：
- companyName: 公司名称（字符串）
- taxId: 税号（字符串）
- companyAddress: 公司地址（字符串）
- companyPhone: 公司电话（字符串）
- contactPerson: 负责人姓名（字符串）
- documentNumber: 单据号码（字符串，重点识别以下类型的订单标识符）
  * Order ID / 订单ID
  * 订单编号 / Order Number
  * 单号 / Document Number
  * OTA Reference / OTA参考号
  * Booking Reference / 预订参考号
  * Transaction ID / 交易ID
  * Invoice Number / 发票号
  * Receipt Number / 收据号
  * 支持中英文混合识别
  * 识别常见编号格式：字母+数字组合、纯数字、带分隔符的编号等
  * 处理不同标签格式：冒号、等号、空格分隔等
- customerName: 客户名称（字符串，仅提取客户/乘客信息，排除司机信息）
- channel: 渠道名称（字符串）
- customerPhone: 客户电话（字符串，仅提取客户/乘客电话，排除司机电话）
- customerEmail: 客户邮箱（字符串）
- items: 项目数组，每个项目包含 {description, quantity, price}
  * description: 项目描述（字符串，保留服务详情例如行程相关，航班号，人数，车型，排除司机相关信息）
  * quantity: 数量（数字，默认为1）
  * price: 价格（数字，如果文本中没有明确价格信息则设为0，不要使用空字符串）

  【重要：项目合并规则】
  * 一个订单通常只应该有一个主要服务项目，请将相关信息合并到单个项目中
  * 将服务类型、车型、航班号、人数等信息合并到一个描述中，例如："Airport Pickup (MH-724) - 7 Seater MPV (6 passengers, 2 luggages)"
  * 只有当存在明确的不同收费项目时才创建多个items
  * 车型信息、乘客人数、行李数量等应作为服务描述的补充说明，而不是独立的收费项目
  * 如果某些信息没有独立价格，不要创建价格为0的独立项目

- notes: 备注信息（字符串，保留订单相关备注/需求，排除聊天记录和司机信息，不保留与项目描述重复的内容）

重要格式要求：
1. price字段必须是数字类型，例如：140, 0, 99.5
2. 不要使用空字符串""作为price值
3. 如果无法确定价格，请设置price为0
4. quantity字段也必须是数字类型，默认为1
5. 严格执行隐私保护：绝不包含司机个人敏感信息

要分析的文本：
${text}

请只返回JSON格式的结果，不要包含其他解释文字。如果某个字段无法提取，字符串字段请设为空字符串，数字字段请设为0或1（quantity默认为1）。
特别注意：
1. 仔细查找文本中的各种单据号码、订单号、参考号等标识符，并提取到documentNumber字段中
2. 严格过滤司机个人信息，确保隐私保护
3. 优先保留客户和订单相关的核心业务信息
`;
            }

            /**
             * 调用Gemini API
             * @function callAPI - 调用Gemini API进行文本分析
             * @param {string} prompt - 分析提示词
             * @returns {Object} API响应数据
             */
            static async callAPI(prompt) {
                console.log('🌐 开始Gemini API调用 - 模型:', AppConfig.geminiModel);
                const apiUrl = `https://generativelanguage.googleapis.com/${AppConfig.geminiApiVersion}/models/${AppConfig.geminiModel}:generateContent?key=${AppConfig.geminiApiKey}`;

                const requestBody = {
                    contents: [{
                        parts: [{
                            text: prompt
                        }]
                    }],
                    generationConfig: {
                        temperature: AppConfig.geminiTemperature,
                        topK: 1,
                        topP: 1,
                        maxOutputTokens: AppConfig.geminiMaxTokens,
                    },
                    safetySettings: [
                        {
                            category: "HARM_CATEGORY_HARASSMENT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_HATE_SPEECH",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        }
                    ]
                };

                console.log('🔧 API请求配置:', {
                    模型: AppConfig.geminiModel,
                    温度: AppConfig.geminiTemperature,
                    最大Token: AppConfig.geminiMaxTokens,
                    URL: apiUrl
                });

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('🌐 API响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    const errorMessage = errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`;

                    console.error('🌐 API错误响应:', errorData);
                    this.handleAPIError(response.status, errorMessage);
                }

                return await response.json();
            }

            /**
             * 处理API错误
             * @function handleAPIError - 处理不同类型的API错误
             * @param {number} status - HTTP状态码
             * @param {string} message - 错误消息
             */
            static handleAPIError(status, message) {
                if (status === 400) {
                    throw new Error(`请求格式错误: ${message}`);
                } else if (status === 401) {
                    throw new Error(`API Key无效或已过期: ${message}`);
                } else if (status === 403) {
                    throw new Error(`权限不足或配额不够: ${message}`);
                } else if (status === 429) {
                    throw new Error(`请求频率过高，请稍后重试: ${message}`);
                } else if (status === 404) {
                    throw new Error(`模型不存在或不可用: ${AppConfig.geminiModel}`);
                } else {
                    throw new Error(`Gemini API调用失败: ${message}`);
                }
            }

            /**
             * 验证API响应
             * @function validateResponse - 验证API响应的格式和内容
             * @param {Object} data - API响应数据
             * @returns {string} 生成的文本内容
             */
            static validateResponse(data) {
                console.log('🌐 API完整响应:', data);

                // 检查响应格式
                if (!data.candidates || data.candidates.length === 0) {
                    throw new Error('API返回空结果，可能是内容被安全过滤器阻止');
                }

                const candidate = data.candidates[0];

                // 检查是否被安全过滤器阻止
                if (candidate.finishReason === 'SAFETY') {
                    throw new Error('内容被安全过滤器阻止');
                }

                if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
                    throw new Error('API返回格式异常，无法获取生成内容');
                }

                const generatedText = candidate.content.parts[0].text;

                console.log('🤖 Gemini文本分析 - 原始响应:', generatedText);
                console.log('📊 响应统计:', {
                    模型: AppConfig.geminiModel,
                    响应长度: generatedText?.length || 0,
                    完成原因: candidate.finishReason,
                    安全评级: candidate.safetyRatings?.length || 0
                });

                return generatedText;
            }

            /**
             * 处理文本分析
             * @function processText - 使用Gemini AI分析文本内容
             * @param {string} text - 要分析的文本
             * @returns {Object|null} 分析结果对象
             */
            static async processText(text) {
                console.log('🔍 Gemini文本分析 - 输入文本:', text.substring(0, 200) + (text.length > 200 ? '...' : ''));

                try {
                    // 构建提示词
                    const prompt = this.buildPrompt(text);

                    // 调用API
                    const data = await this.callAPI(prompt);

                    // 验证响应
                    const generatedText = this.validateResponse(data);

                    if (generatedText) {
                        // 使用统一的JSON解析函数
                        const parseResult = parseGeminiJsonResponse(generatedText);
                        if (parseResult.success) {
                            console.log('📋 Gemini文本分析 - 解析成功:', parseResult.data);
                            return parseResult.data;
                        } else {
                            console.warn('⚠️ Gemini文本分析 - JSON解析失败');
                            return null;
                        }
                    }

                    console.warn('⚠️ Gemini文本分析 - 未能解析有效JSON');
                    return null;
                } catch (error) {
                    console.error('❌ Gemini文本分析错误:', error);
                    throw error;
                }
            }
        }

        /**
         * 使用Gemini AI处理文本（保持向后兼容）
         * @function processTextWithGemini - 使用Gemini AI分析文本内容
         * @param {string} text - 要分析的文本
         * @returns {Object|null} 分析结果对象
         */
        async function processTextWithGemini(text) {
            return await GeminiService.processText(text);
        }


        /**
         * JSON解析器类 - 使用责任链模式简化复杂逻辑
         * @class JSONParser - 提供多种JSON解析策略
         */
        class JSONParser {
            /**
             * 解析JSON响应
             * @function parse - 主要解析方法，使用责任链模式
             * @param {string} responseText - 原始响应文本
             * @returns {Object} 解析结果
             */
            static parse(responseText) {
                const parsers = [
                    new MarkdownJSONParser(),
                    new ObjectJSONParser(),
                    new ArrayJSONParser(),
                    new RepairJSONParser()
                ];

                for (const parser of parsers) {
                    const result = parser.tryParse(responseText);
                    if (result.success) {
                        return this.processResult(result);
                    }
                }

                console.warn('⚠️ 所有解析器都失败了');
                return { success: false, data: null, isArrayFormat: false };
            }

            /**
             * 处理解析结果
             * @function processResult - 统一处理解析结果
             * @param {Object} result - 解析结果
             * @returns {Object} 处理后的结果
             */
            static processResult(result) {
                try {
                    // 验证和修复价格字段
                    this.fixPriceFields(result.data);

                    // 智能处理数组和对象格式
                    return this.handleDataFormat(result);

                } catch (error) {
                    console.error('❌ 结果处理失败:', error);
                    return { success: false, data: null, isArrayFormat: false };
                }
            }

            /**
             * 修复价格字段
             * @function fixPriceFields - 修复和验证价格字段
             * @param {Object} data - 数据对象
             */
            static fixPriceFields(data) {
                if (!data || !data.items || !Array.isArray(data.items)) return;

                data.items.forEach((item, index) => {
                    // 修复价格字段
                    if (item.price === '' || item.price === null || item.price === undefined) {
                        item.price = 0;
                    } else if (typeof item.price === 'string') {
                        const numPrice = parseFloat(item.price);
                        item.price = !isNaN(numPrice) ? numPrice : 0;
                    }

                    // 修复数量字段
                    if (item.quantity === '' || item.quantity === null || item.quantity === undefined) {
                        item.quantity = 1;
                    } else if (typeof item.quantity === 'string') {
                        const numQuantity = parseFloat(item.quantity);
                        item.quantity = (!isNaN(numQuantity) && numQuantity > 0) ? numQuantity : 1;
                    }
                });

                console.log('🔧 价格字段验证和修复完成');
            }

            /**
             * 处理数据格式
             * @function handleDataFormat - 智能处理数组和对象格式
             * @param {Object} result - 解析结果
             * @returns {Object} 格式化后的结果
             */
            static handleDataFormat(result) {
                const { data, isArrayFormat } = result;

                if (Array.isArray(data)) {
                    if (data.length === 1) {
                        console.log('🔄 检测到单对象数组，提取对象内容');
                        return { success: true, data: { isMultiOrder: false, ...data[0] }, isArrayFormat };
                    } else if (data.length > 1) {
                        console.log(`🔢 检测到多订单: ${data.length}个订单`);
                        return { success: true, data: { isMultiOrder: true, orders: data }, isArrayFormat };
                    } else {
                        console.warn('⚠️ 解析结果为空数组');
                        return { success: false, data: null, isArrayFormat };
                    }
                } else {
                    console.log('🔢 单个对象格式');
                    return { success: true, data: { isMultiOrder: false, ...data }, isArrayFormat };
                }
            }
        }

        /**
         * Markdown JSON解析器
         * @class MarkdownJSONParser - 解析markdown代码块中的JSON
         */
        class MarkdownJSONParser {
            tryParse(responseText) {
                const markdownMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/);
                if (markdownMatch) {
                    console.log('🔍 从markdown代码块中提取JSON');
                    try {
                        const data = JSON.parse(markdownMatch[1]);
                        return { success: true, data, isArrayFormat: Array.isArray(data) };
                    } catch (error) {
                        console.log('❌ Markdown JSON解析失败:', error);
                    }
                }
                return { success: false };
            }
        }

        /**
         * 对象JSON解析器
         * @class ObjectJSONParser - 解析对象格式的JSON
         */
        class ObjectJSONParser {
            tryParse(responseText) {
                const objectMatch = responseText.match(/\{[\s\S]*\}/);
                if (objectMatch) {
                    console.log('🔍 尝试解析对象格式JSON');
                    try {
                        const data = JSON.parse(objectMatch[0]);
                        return { success: true, data, isArrayFormat: false };
                    } catch (error) {
                        console.log('❌ 对象JSON解析失败:', error);
                    }
                }
                return { success: false };
            }
        }

        /**
         * 数组JSON解析器
         * @class ArrayJSONParser - 解析数组格式的JSON
         */
        class ArrayJSONParser {
            tryParse(responseText) {
                const arrayMatch = responseText.match(/\[[\s\S]*\]/);
                if (arrayMatch) {
                    console.log('🔍 尝试解析数组格式JSON');
                    try {
                        const data = JSON.parse(arrayMatch[0]);
                        return { success: true, data, isArrayFormat: true };
                    } catch (error) {
                        console.log('❌ 数组JSON解析失败:', error);
                    }
                }
                return { success: false };
            }
        }

        /**
         * 修复JSON解析器
         * @class RepairJSONParser - 尝试修复并解析损坏的JSON
         */
        class RepairJSONParser {
            tryParse(responseText) {
                console.log('🔍 尝试修复JSON格式...');

                // 尝试多种修复策略
                const repairStrategies = [
                    this.fixCommonIssues.bind(this),
                    this.removeInvalidChars.bind(this),
                    this.fixQuotes.bind(this)
                ];

                for (const strategy of repairStrategies) {
                    try {
                        const repairedJson = strategy(responseText);
                        const data = JSON.parse(repairedJson);
                        console.log('✅ JSON修复成功');
                        return { success: true, data, isArrayFormat: Array.isArray(data) };
                    } catch (error) {
                        continue; // 尝试下一个策略
                    }
                }

                console.log('❌ 所有修复策略都失败了');
                return { success: false };
            }

            fixCommonIssues(text) {
                return text
                    .replace(/,\s*}/g, '}')  // 移除对象末尾的逗号
                    .replace(/,\s*]/g, ']')  // 移除数组末尾的逗号
                    .replace(/'/g, '"');     // 单引号转双引号
            }

            removeInvalidChars(text) {
                return text.replace(/[\x00-\x1F\x7F]/g, ''); // 移除控制字符
            }

            fixQuotes(text) {
                return text.replace(/([{,]\s*)(\w+):/g, '$1"$2":'); // 为属性名添加引号
            }
        }

        /**
         * 解析Gemini AI返回的JSON响应（保持向后兼容）
         * @function parseGeminiJsonResponse - 统一的JSON解析逻辑，支持对象和数组格式
         * @param {string} responseText - Gemini AI的原始响应文本
         * @returns {Object|null} 解析结果对象，包含 {success: boolean, data: any, isArrayFormat: boolean}
         */
        function parseGeminiJsonResponse(responseText) {
            return JSONParser.parse(responseText);
        }


        /**
         * 安全的DOM查询工具
         * @description 提供统一的DOM查询和错误处理机制
         */
        const SafeDOM = {
            /**
             * 安全获取元素
             * @function get - 安全获取DOM元素，包含错误处理
             * @param {string} selector - CSS选择器或元素ID
             * @param {string} type - 查询类型 ('id' 或 'selector')
             * @param {HTMLElement} context - 查询上下文，默认为document
             * @returns {HTMLElement|null} DOM元素或null
             */
            get(selector, type = 'id', context = document) {
                try {
                    if (!selector) {
                        console.warn('⚠️ SafeDOM.get: 选择器为空');
                        return null;
                    }

                    let element = null;
                    if (type === 'id') {
                        element = context.getElementById ? context.getElementById(selector) : context.querySelector(`#${selector}`);
                    } else {
                        element = context.querySelector(selector);
                    }

                    if (!element) {
                        console.warn(`⚠️ SafeDOM.get: 未找到元素 "${selector}" (类型: ${type})`);
                    }

                    return element;
                } catch (error) {
                    console.error(`❌ SafeDOM.get错误: 查询"${selector}"失败`, error);
                    return null;
                }
            },

            /**
             * 安全获取多个元素
             * @function getAll - 安全获取多个DOM元素
             * @param {string} selector - CSS选择器
             * @param {HTMLElement} context - 查询上下文，默认为document
             * @returns {NodeList|Array} 元素列表
             */
            getAll(selector, context = document) {
                try {
                    if (!selector) {
                        console.warn('⚠️ SafeDOM.getAll: 选择器为空');
                        return [];
                    }

                    const elements = context.querySelectorAll(selector);
                    if (elements.length === 0) {
                        console.warn(`⚠️ SafeDOM.getAll: 未找到元素 "${selector}"`);
                    }

                    return elements;
                } catch (error) {
                    console.error(`❌ SafeDOM.getAll错误: 查询"${selector}"失败`, error);
                    return [];
                }
            },

            /**
             * 安全设置元素值
             * @function setValue - 安全设置元素的值
             * @param {string} selector - 元素选择器
             * @param {any} value - 要设置的值
             * @param {string} type - 查询类型
             * @returns {boolean} 是否设置成功
             */
            setValue(selector, value, type = 'id') {
                try {
                    const element = this.get(selector, type);
                    if (element) {
                        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.tagName === 'SELECT') {
                            element.value = value;
                        } else {
                            element.textContent = value;
                        }
                        return true;
                    }
                    return false;
                } catch (error) {
                    console.error(`❌ SafeDOM.setValue错误: 设置"${selector}"值失败`, error);
                    return false;
                }
            },

            /**
             * 安全获取元素值
             * @function getValue - 安全获取元素的值
             * @param {string} selector - 元素选择器
             * @param {string} type - 查询类型
             * @param {any} defaultValue - 默认值
             * @returns {any} 元素值或默认值
             */
            getValue(selector, type = 'id', defaultValue = '') {
                try {
                    const element = this.get(selector, type);
                    if (element) {
                        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.tagName === 'SELECT') {
                            return element.value || defaultValue;
                        } else {
                            return element.textContent || defaultValue;
                        }
                    }
                    return defaultValue;
                } catch (error) {
                    console.error(`❌ SafeDOM.getValue错误: 获取"${selector}"值失败`, error);
                    return defaultValue;
                }
            },

            /**
             * 检查元素是否存在
             * @function exists - 检查DOM元素是否存在
             * @param {string} selector - 元素选择器
             * @param {string} type - 查询类型
             * @returns {boolean} 元素是否存在
             */
            exists(selector, type = 'id') {
                return !!this.get(selector, type);
            }
        };

        // generateExportFilename 函数已移至 export-components.js 模块

        /**
         * 事件管理器
         * @description 统一管理事件绑定，使用事件委托减少重复绑定
         */
        const EventManager = {
            // 存储已绑定的事件
            boundEvents: new Set(),

            /**
             * 初始化事件委托
             * @function init - 初始化全局事件委托
             */
            init() {
                console.log('🔄 初始化事件管理器...');

                // 全局点击事件委托
                if (!this.boundEvents.has('global-click')) {
                    document.addEventListener('click', this.handleGlobalClick.bind(this));
                    this.boundEvents.add('global-click');
                }

                // 全局输入事件委托
                if (!this.boundEvents.has('global-input')) {
                    document.addEventListener('input', this.handleGlobalInput.bind(this));
                    this.boundEvents.add('global-input');
                }

                // 全局变化事件委托
                if (!this.boundEvents.has('global-change')) {
                    document.addEventListener('change', this.handleGlobalChange.bind(this));
                    this.boundEvents.add('global-change');
                }

                console.log('✅ 事件管理器初始化完成');
            },

            /**
             * 处理全局点击事件
             * @function handleGlobalClick - 统一处理点击事件
             * @param {Event} e - 点击事件对象
             */
            handleGlobalClick(e) {
                const target = e.target;
                const classList = target.classList;

                try {
                    // 导出按钮
                    if (classList.contains('export-btn')) {
                        const format = target.dataset.format;
                        if (format && typeof ModernExportSystem !== 'undefined') {
                            ModernExportSystem.startExport(format);
                        }
                        return;
                    }

                    // 删除项目按钮
                    if (target.textContent.includes('删除') || target.textContent.includes('Delete')) {
                        if (typeof removeItem === 'function') {
                            removeItem(target);
                        }
                        return;
                    }

                    // AI相关按钮
                    if (target.id === 'ai-fill-btn') {
                        if (typeof toggleAIFillPanel === 'function') {
                            toggleAIFillPanel();
                        }
                        return;
                    }

                    // 处理AI分析按钮（特殊处理）
                    if (target.id === 'ai-process-text' || target.id === 'ai-process-btn' ||
                        target.closest('#ai-process-text') || target.closest('#ai-process-btn') ||
                        target.textContent.includes('分析填充') || target.textContent.includes('分析中')) {
                        console.log('🎯 AI分析按钮被点击');
                        if (typeof processAIFill === 'function') {
                            processAIFill();
                        } else {
                            console.error('❌ processAIFill 函数未定义');
                        }
                        return;
                    }

                    // 处理其他特定按钮
                    const buttonActions = {
                        'ai-process-text': () => typeof processAIFill === 'function' && processAIFill(),
                        'clear-ai-input': () => typeof clearAIInput === 'function' && clearAIInput(),
                        'refresh-preview': () => typeof safeDebouncedUpdatePreview === 'function' && safeDebouncedUpdatePreview(),
                        'clear-form': () => typeof clearForm === 'function' && clearForm(),
                        'add-item': () => typeof addItem === 'function' && addItem()
                    };

                    if (target.id && buttonActions[target.id]) {
                        buttonActions[target.id]();
                        return;
                    }

                    // 处理onclick属性（向后兼容）
                    if (target.onclick) {
                        // 让原有的onclick处理器正常执行
                        return;
                    }

                } catch (error) {
                    console.error('❌ 全局点击事件处理错误:', error);
                }
            },

            /**
             * 处理全局输入事件
             * @function handleGlobalInput - 统一处理输入事件
             * @param {Event} e - 输入事件对象
             */
            handleGlobalInput(e) {
                const target = e.target;
                const classList = target.classList;

                try {
                    // 项目数量和价格输入
                    if (classList.contains('item-quantity') || classList.contains('item-price')) {
                        const row = target.closest('tr');
                        if (row && typeof updateItemAmount === 'function') {
                            updateItemAmount(row);
                        }
                        return;
                    }

                    // 项目描述输入（触发预览更新）
                    if (classList.contains('item-description')) {
                        if (AppConfig.autoPreview && typeof safeDebouncedUpdatePreview === 'function') {
                            safeDebouncedUpdatePreview();
                        }
                        return;
                    }

                    // 其他表单字段（触发预览更新）
                    const autoPreviewFields = [
                        'document-number', 'customer-name', 'customer-phone',
                        'customer-email', 'notes', 'company-name', 'tax-id',
                        'company-address', 'company-phone', 'contact-person'
                    ];

                    if (autoPreviewFields.includes(target.id) && AppConfig.autoPreview) {
                        if (typeof safeDebouncedUpdatePreview === 'function') {
                            safeDebouncedUpdatePreview();
                        }
                        return;
                    }

                } catch (error) {
                    console.error('❌ 全局输入事件处理错误:', error);
                }
            },

            /**
             * 处理全局变化事件
             * @function handleGlobalChange - 统一处理变化事件
             * @param {Event} e - 变化事件对象
             */
            handleGlobalChange(e) {
                const target = e.target;

                try {
                    // 文档类型变化
                    if (target.id === 'document-type') {
                        const newType = target.value;
                        const numberInput = SafeDOM.get('document-number');
                        if (numberInput && typeof generateDocumentNumber === 'function') {
                            numberInput.value = generateDocumentNumber(newType);
                        }
                        if (typeof toggleCompanyFields === 'function') {
                            toggleCompanyFields(newType);
                        }
                        if (AppConfig.autoPreview && typeof safeDebouncedUpdatePreview === 'function') {
                            safeDebouncedUpdatePreview();
                        }
                        return;
                    }

                    // 公司选择器变化
                    if (target.id === 'company-selector') {
                        if (typeof loadCompanyInfo === 'function') {
                            loadCompanyInfo(target.value);
                        }
                        return;
                    }

                    // 货币选择器变化
                    if (target.id === 'currency-selector') {
                        if (typeof switchCurrency === 'function') {
                            switchCurrency(target.value);
                        }
                        return;
                    }

                    // 显示模式变化
                    if (target.id === 'display-mode') {
                        if (typeof switchDisplayMode === 'function') {
                            switchDisplayMode(target.value);
                        }
                        return;
                    }

                    // 数字字段变化（触发总金额更新）
                    if (target.type === 'number' && AppConfig.autoPreview) {
                        if (typeof safeDebouncedUpdatePreview === 'function') {
                            safeDebouncedUpdatePreview();
                        }
                        return;
                    }

                } catch (error) {
                    console.error('❌ 全局变化事件处理错误:', error);
                }
            },

            /**
             * 绑定特定元素事件
             * @function bindElementEvent - 为特定元素绑定事件
             * @param {string} selector - 元素选择器
             * @param {string} eventType - 事件类型
             * @param {Function} handler - 事件处理函数
             * @param {string} key - 事件键名（用于防重复绑定）
             */
            bindElementEvent(selector, eventType, handler, key) {
                if (this.boundEvents.has(key)) {
                    return; // 已绑定，跳过
                }

                const element = SafeDOM.get(selector, 'selector');
                if (element) {
                    element.addEventListener(eventType, handler);
                    this.boundEvents.add(key);
                    console.log(`✅ 绑定事件: ${key}`);
                } else {
                    console.warn(`⚠️ 元素未找到，无法绑定事件: ${selector}`);
                }
            },

            /**
             * 获取绑定状态
             * @function getBindingStats - 获取事件绑定统计信息
             * @returns {Object} 绑定统计对象
             */
            getBindingStats() {
                return {
                    总绑定数: this.boundEvents.size,
                    绑定列表: Array.from(this.boundEvents)
                };
            },

            /**
             * 清理所有事件监听器
             * @function cleanup - 清理所有已绑定的事件监听器
             */
            cleanup() {
                // 清理全局事件监听器
                if (this.boundEvents.has('global-click')) {
                    document.removeEventListener('click', this.handleGlobalClick);
                    this.boundEvents.delete('global-click');
                }
                if (this.boundEvents.has('global-input')) {
                    document.removeEventListener('input', this.handleGlobalInput);
                    this.boundEvents.delete('global-input');
                }
                if (this.boundEvents.has('global-change')) {
                    document.removeEventListener('change', this.handleGlobalChange);
                    this.boundEvents.delete('global-change');
                }

                console.log('🧹 事件监听器清理完成');
            }
        };

        /**
         * 安全的DOM操作工具
         * @description 提供安全的DOM操作方法，避免XSS攻击
         */
        const SafeDOMBuilder = {
            /**
             * 安全创建元素
             * @function createElement - 安全创建DOM元素
             * @param {string} tagName - 标签名
             * @param {Object} options - 选项对象
             * @returns {HTMLElement} 创建的元素
             */
            createElement(tagName, options = {}) {
                const element = document.createElement(tagName);

                if (options.className) {
                    element.className = options.className;
                }

                if (options.textContent) {
                    element.textContent = options.textContent;
                }

                if (options.attributes) {
                    Object.entries(options.attributes).forEach(([key, value]) => {
                        element.setAttribute(key, value);
                    });
                }

                if (options.style) {
                    Object.assign(element.style, options.style);
                }

                return element;
            },

            /**
             * 安全创建表格行
             * @function createTableRow - 安全创建表格行
             * @param {Array} cellData - 单元格数据数组
             * @param {Object} options - 选项对象
             * @returns {HTMLTableRowElement} 表格行元素
             */
            createTableRow(cellData, options = {}) {
                const row = document.createElement('tr');

                if (options.className) {
                    row.className = options.className;
                }

                cellData.forEach(cell => {
                    const td = document.createElement('td');

                    if (typeof cell === 'string') {
                        td.textContent = cell;
                    } else if (typeof cell === 'object') {
                        if (cell.html) {
                            // 仅在明确需要HTML时使用，并进行清理
                            td.innerHTML = this.sanitizeHTML(cell.html);
                        } else if (cell.textContent) {
                            td.textContent = cell.textContent;
                        }

                        if (cell.className) {
                            td.className = cell.className;
                        }

                        if (cell.style) {
                            Object.assign(td.style, cell.style);
                        }

                        if (cell.attributes) {
                            Object.entries(cell.attributes).forEach(([key, value]) => {
                                td.setAttribute(key, value);
                            });
                        }
                    }

                    row.appendChild(td);
                });

                return row;
            },

            /**
             * 清理HTML内容
             * @function sanitizeHTML - 基本的HTML清理
             * @param {string} html - 原始HTML
             * @returns {string} 清理后的HTML
             */
            sanitizeHTML(html) {
                // 基本的HTML清理，移除潜在危险的标签和属性
                return html
                    .replace(/<script[^>]*>.*?<\/script>/gi, '')
                    .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
                    .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
                    .replace(/javascript:/gi, '');
            },

            /**
             * 安全清空并填充容器
             * @function safeReplaceContent - 安全替换容器内容
             * @param {HTMLElement} container - 目标容器
             * @param {HTMLElement|Array} content - 新内容
             */
            safeReplaceContent(container, content) {
                if (!container) {
                    console.warn('⚠️ SafeDOMBuilder.safeReplaceContent: 容器为空');
                    return;
                }

                // 清空容器
                while (container.firstChild) {
                    container.removeChild(container.firstChild);
                }

                // 添加新内容
                if (Array.isArray(content)) {
                    content.forEach(item => {
                        if (item instanceof HTMLElement) {
                            container.appendChild(item);
                        }
                    });
                } else if (content instanceof HTMLElement) {
                    container.appendChild(content);
                } else if (typeof content === 'string') {
                    container.textContent = content;
                }
            },

            /**
             * 创建空状态消息
             * @function createEmptyMessage - 创建空状态消息元素
             * @param {string} message - 消息文本
             * @param {Object} options - 选项对象
             * @returns {HTMLElement} 消息元素
             */
            createEmptyMessage(message, options = {}) {
                return this.createElement('div', {
                    className: options.className || 'empty-preview-message',
                    textContent: message,
                    style: {
                        padding: '20px',
                        textAlign: 'center',
                        color: '#666',
                        ...options.style
                    }
                });
            }
        };

        /**
         * 清理JSON字符串
         * @function cleanJsonString - 清理和修复常见的JSON格式问题
         * @param {string} jsonStr - 原始JSON字符串
         * @returns {string} 清理后的JSON字符串
         */
        function cleanJsonString(jsonStr) {
            return jsonStr
                .replace(/```json\s*/g, '') // 移除markdown代码块标记
                .replace(/```\s*/g, '')     // 移除结束的代码块标记
                .replace(/,\s*}/g, '}')     // 移除对象末尾多余的逗号
                .replace(/,\s*]/g, ']')     // 移除数组末尾多余的逗号
                .replace(/\n/g, ' ')        // 替换换行符为空格
                .replace(/\r/g, ' ')        // 替换回车符为空格
                .replace(/\t/g, ' ')        // 替换制表符为空格
                .replace(/\s+/g, ' ')       // 合并多个空格
                .trim();                    // 移除首尾空格
        }

        /**
         * 使用Gemini AI处理图片
         * @function processImageWithGemini - 使用Gemini AI分析图片内容
         * @param {File} imageFile - 图片文件
         * @param {string} additionalText - 额外的文本信息
         * @returns {Object|null} 分析结果对象
         */
        async function processImageWithGemini(imageFile, additionalText = '') {
            // 将图片转换为base64
            const base64Image = await fileToBase64(imageFile);
            const base64Data = base64Image.split(',')[1]; // 移除data:image/...;base64,前缀

            const prompt = `
请分析这张图片${additionalText ? '和以下文本' : ''}，提取相关的发票/收据信息，并以JSON格式返回。

${additionalText ? `额外文本信息：${additionalText}` : ''}

【重要：图片数据清理和隐私保护规则】
在分析图片和提取信息时，请严格遵守以下数据清理规则：

1. 司机信息过滤（图片识别重点）：
   - 识别并移除图片中的司机个人信息（姓名、电话、身份证号、驾驶证号、车牌号等）
   - 过滤包含以下关键词的个人信息：司机、师傅、驾驶员、开车的、车主、代驾、网约车司机、滴滴司机、出租车司机、driver、chauffeur
   - 特别注意图片中的证件信息、个人头像、车辆信息等敏感内容
   - 移除车牌号格式的文本（如：京A12345、沪B67890等）

2. 图片内容智能识别：
   - 区分订单截图、聊天记录截图、证件照片等不同类型
   - 从订单截图中提取结构化信息，忽略聊天对话内容
   - 识别表格、列表、标签等结构化数据区域
   - 过滤图片中的广告、推广信息、无关文字

3. 客户信息优先保留：
   - 当图片中同时出现多个姓名时，优先选择客户/乘客相关的姓名
   - 客户相关标识：乘客、客户、用户、顾客、收货人、联系人、passenger、customer、user
   - 在明确的客户信息区域提取联系方式

4. 图片文本验证：
   - 验证识别的文本是否为有效的订单信息
   - 检查电话号码格式（中国手机号：1开头的11位数字）
   - 验证订单号格式的合理性
   - 排除图片中的水印、时间戳等无关信息

请提取以下字段（如果存在）：
- companyName: 公司名称
- taxId: 税号
- companyAddress: 公司地址
- companyPhone: 公司电话
- contactPerson: 负责人姓名
- documentNumber: 单据号码（重点识别以下类型的订单标识符）
  * Order ID / 订单ID
  * 订单编号 / Order Number
  * 单号 / Document Number
  * OTA Reference / OTA参考号
  * Booking Reference / 预订参考号
  * Transaction ID / 交易ID
  * Invoice Number / 发票号
  * Receipt Number / 收据号
  * 支持中英文混合识别
  * 识别常见编号格式：字母+数字组合、纯数字、带分隔符的编号等
  * 处理不同标签格式：冒号、等号、空格分隔等
- customerName: 客户名称（仅提取客户/乘客信息，严格排除司机信息）
- channel: 渠道名称
- customerPhone: 客户电话（仅提取客户/乘客电话，严格排除司机电话）
- customerEmail: 客户邮箱
- items: 项目数组，每个项目包含 {description, quantity, price}
  * description: 项目描述（保留服务详情，排除司机相关信息）
  * quantity: 数量（数字，默认为1）
  * price: 价格（数字，如果图片中没有明确价格信息则设为0）

  【重要：项目合并规则】
  * 一个订单通常只应该有一个主要服务项目，请将相关信息合并到单个项目中
  * 将服务类型、车型、航班号、人数等信息合并到一个描述中，例如："Airport Pickup (MH-724) - 7 Seater MPV (6 passengers, 2 luggages)"
  * 只有当存在明确的不同收费项目时才创建多个items
  * 车型信息、乘客人数、行李数量等应作为服务描述的补充说明，而不是独立的收费项目
  * 如果某些信息没有独立价格，不要创建价格为0的独立项目

- notes: 备注信息（保留订单相关备注，排除聊天记录和司机信息）

重要格式要求：
1. price字段必须是数字类型，例如：140, 0, 99.5
2. 不要使用空字符串""作为price值
3. 如果无法确定价格，请设置price为0
4. quantity字段也必须是数字类型，默认为1
5. 严格执行隐私保护：绝不包含司机个人敏感信息
6. 图片识别重点：区分结构化订单信息和非结构化聊天内容

请只返回JSON格式的结果，不要包含其他解释文字。如果某个字段无法提取，请设为空字符串或空数组。
特别注意：
1. 仔细查找图片中的各种单据号码、订单号、参考号等标识符，并提取到documentNumber字段中
2. 严格过滤司机个人信息和车辆信息，确保隐私保护
3. 优先识别和保留客户订单相关的核心业务信息
4. 区分图片类型，重点处理订单截图而非聊天记录截图
`;

            console.log('🖼️ Gemini图片分析 - 开始处理图片:', imageFile.name, '大小:', (imageFile.size / 1024).toFixed(2) + 'KB');
            if (additionalText) {
                console.log('📝 额外文本信息:', additionalText.substring(0, 100) + (additionalText.length > 100 ? '...' : ''));
            }

            try {
                console.log('🌐 开始Gemini图片分析 - 模型:', AppConfig.geminiModel);
                const apiUrl = `https://generativelanguage.googleapis.com/${AppConfig.geminiApiVersion}/models/${AppConfig.geminiModel}:generateContent?key=${AppConfig.geminiApiKey}`;

                const requestBody = {
                    contents: [{
                        parts: [
                            {
                                text: prompt
                            },
                            {
                                inline_data: {
                                    mime_type: imageFile.type,
                                    data: base64Data
                                }
                            }
                        ]
                    }],
                    generationConfig: {
                        temperature: AppConfig.geminiTemperature,
                        topK: 1,
                        topP: 1,
                        maxOutputTokens: AppConfig.geminiMaxTokens,
                    },
                    safetySettings: [
                        {
                            category: "HARM_CATEGORY_HARASSMENT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_HATE_SPEECH",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        }
                    ]
                };

                console.log('🔧 API图片分析请求配置:', {
                    模型: AppConfig.geminiModel,
                    温度: AppConfig.geminiTemperature,
                    最大Token: AppConfig.geminiMaxTokens,
                    图片类型: imageFile.type,
                    图片大小: (imageFile.size / 1024).toFixed(2) + 'KB',
                    URL: apiUrl
                });

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('🌐 图片分析API响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    const errorMessage = errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`;

                    console.error('🌐 图片分析API错误响应:', errorData);

                    // 提供更详细的错误信息
                    if (response.status === 400) {
                        throw new Error(`图片分析请求格式错误: ${errorMessage}`);
                    } else if (response.status === 401) {
                        throw new Error(`API Key无效或已过期: ${errorMessage}`);
                    } else if (response.status === 403) {
                        throw new Error(`权限不足或配额不够: ${errorMessage}`);
                    } else if (response.status === 429) {
                        throw new Error(`请求频率过高，请稍后重试: ${errorMessage}`);
                    } else if (response.status === 404) {
                        throw new Error(`模型不存在或不可用: ${AppConfig.geminiModel}`);
                    } else {
                        throw new Error(`图片分析API调用失败: ${errorMessage}`);
                    }
                }

                const data = await response.json();
                console.log('🌐 图片分析API完整响应:', data);

                // 检查响应格式
                if (!data.candidates || data.candidates.length === 0) {
                    throw new Error('图片分析API返回空结果，可能是内容被安全过滤器阻止');
                }

                const candidate = data.candidates[0];

                // 检查是否被安全过滤器阻止
                if (candidate.finishReason === 'SAFETY') {
                    throw new Error('图片内容被安全过滤器阻止');
                }

                if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
                    throw new Error('图片分析API返回格式异常，无法获取生成内容');
                }

                const generatedText = candidate.content.parts[0].text;

                console.log('🤖 Gemini图片分析 - 原始响应:', generatedText);
                console.log('📊 图片分析响应统计:', {
                    模型: AppConfig.geminiModel,
                    响应长度: generatedText?.length || 0,
                    完成原因: candidate.finishReason,
                    安全评级: candidate.safetyRatings?.length || 0,
                    图片信息: `${imageFile.name} (${(imageFile.size / 1024).toFixed(2)}KB)`
                });

                if (generatedText) {
                    // 使用统一的JSON解析函数
                    const parseResult = parseGeminiJsonResponse(generatedText);
                    if (parseResult.success) {
                        console.log('📋 Gemini图片分析 - 解析成功:', parseResult.data);
                        return parseResult.data;
                    } else {
                        console.warn('⚠️ Gemini图片分析 - JSON解析失败');
                        return null;
                    }
                }

                console.warn('⚠️ Gemini图片分析 - 未能解析有效JSON');
                return null;
            } catch (error) {
                console.error('❌ Gemini图片分析错误:', error);
                throw error;
            }
        }

        /**
         * 将文件转换为base64
         * @function fileToBase64 - 将文件转换为base64编码
         * @param {File} file - 文件对象
         * @returns {Promise<string>} base64编码字符串
         */
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }

        /**
         * 数据清理和验证工具
         * @description 提供司机信息过滤和数据验证功能
         */
        const DataCleaningValidator = {
            // 司机相关关键词
            driverKeywords: [
                '司机', '师傅', '驾驶员', '开车的', '车主', '代驾', '网约车司机',
                '滴滴司机', '出租车司机', '专车司机', '快车司机',
                'driver', 'chauffeur', 'operator', 'pilot',
                'didi', '滴滴', 'uber', 'lyft', 'grab'
            ],

            // 客户相关关键词
            customerKeywords: [
                '乘客', '客户', '用户', '顾客', '收货人', '联系人',
                'passenger', 'customer', 'user', 'client'
            ],

            // 敏感信息正则表达式
            sensitivePatterns: {
                // 身份证号：18位数字或17位数字+X
                idCard: /\b\d{17}[\dXx]\b/g,
                // 车牌号
                licensePlate: /[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4,5}[A-Z0-9挂学警港澳]/g,
                // 中国手机号
                phoneNumber: /\b1[3-9]\d{9}\b/g
            },

            /**
             * 检查文本是否包含司机信息
             * @function containsDriverInfo - 检查文本是否包含司机相关信息
             * @param {string} text - 要检查的文本
             * @returns {boolean} 是否包含司机信息
             */
            containsDriverInfo(text) {
                if (!text || typeof text !== 'string') return false;
                const lowerText = text.toLowerCase();
                return this.driverKeywords.some(keyword => lowerText.includes(keyword.toLowerCase()));
            },

            /**
             * 检查文本是否包含客户信息
             * @function containsCustomerInfo - 检查文本是否包含客户相关信息
             * @param {string} text - 要检查的文本
             * @returns {boolean} 是否包含客户信息
             */
            containsCustomerInfo(text) {
                if (!text || typeof text !== 'string') return false;
                const lowerText = text.toLowerCase();
                return this.customerKeywords.some(keyword => lowerText.includes(keyword.toLowerCase()));
            },

            /**
             * 移除敏感信息
             * @function removeSensitiveInfo - 移除文本中的敏感信息
             * @param {string} text - 要清理的文本
             * @returns {string} 清理后的文本
             */
            removeSensitiveInfo(text) {
                if (!text || typeof text !== 'string') return text;
                let cleanedText = text;

                // 移除身份证号
                cleanedText = cleanedText.replace(this.sensitivePatterns.idCard, '[身份证号已隐藏]');

                // 移除车牌号
                cleanedText = cleanedText.replace(this.sensitivePatterns.licensePlate, '[车牌号已隐藏]');

                return cleanedText;
            },

            /**
             * 验证电话号码格式
             * @function validatePhoneNumber - 验证电话号码是否为有效的中国手机号
             * @param {string} phone - 电话号码
             * @returns {boolean} 是否为有效电话号码
             */
            validatePhoneNumber(phone) {
                if (!phone || typeof phone !== 'string') return false;
                return /^1[3-9]\d{9}$/.test(phone.replace(/\s|-/g, ''));
            },

            /**
             * 验证客户姓名
             * @function validateCustomerName - 验证客户姓名是否合理
             * @param {string} name - 客户姓名
             * @returns {boolean} 是否为有效客户姓名
             */
            validateCustomerName(name) {
                if (!name || typeof name !== 'string') return false;

                // 检查长度
                if (name.length < 2 || name.length > 20) return false;

                // 检查是否包含司机相关关键词
                if (this.containsDriverInfo(name)) return false;

                return true;
            },

            /**
             * 智能合并项目
             * @function mergeItems - 智能合并不合理的多项目为单个项目
             * @param {Array} items - 项目数组
             * @returns {Array} 合并后的项目数组
             */
            mergeItems(items) {
                if (!items || !Array.isArray(items) || items.length <= 1) return items;

                console.log('🔄 开始智能合并项目:', items);

                // 检查是否需要合并
                const needsMerging = this.shouldMergeItems(items);
                if (!needsMerging) {
                    console.log('✅ 项目无需合并');
                    return items;
                }

                // 找到主要收费项目（价格最高的）
                const mainItem = items.reduce((max, item) => {
                    const price = parseFloat(item.price) || 0;
                    const maxPrice = parseFloat(max.price) || 0;
                    return price > maxPrice ? item : max;
                }, items[0]);

                // 收集所有描述信息
                const descriptions = items
                    .map(item => item.description)
                    .filter(desc => desc && desc.trim())
                    .filter(desc => desc !== mainItem.description); // 排除主项目的描述

                // 合并描述
                let mergedDescription = mainItem.description || '';
                if (descriptions.length > 0) {
                    // 智能合并：将车型、人数等信息作为补充说明
                    const additionalInfo = descriptions.join(' - ');
                    if (mergedDescription && !mergedDescription.includes(additionalInfo)) {
                        mergedDescription += ` - ${additionalInfo}`;
                    } else if (!mergedDescription) {
                        mergedDescription = additionalInfo;
                    }
                }

                // 创建合并后的项目
                const mergedItem = {
                    description: mergedDescription,
                    quantity: mainItem.quantity || 1,
                    price: mainItem.price || 0
                };

                console.log('🔄 项目合并完成:', {
                    原始项目数量: items.length,
                    合并后: [mergedItem],
                    主要项目: mainItem.description,
                    合并描述: mergedDescription
                });

                return [mergedItem];
            },

            /**
             * 判断是否需要合并项目
             * @function shouldMergeItems - 判断项目是否需要合并
             * @param {Array} items - 项目数组
             * @returns {boolean} 是否需要合并
             */
            shouldMergeItems(items) {
                if (!items || items.length <= 1) return false;

                // 检查是否有多个价格为0的项目
                const zeroPrice = items.filter(item => parseFloat(item.price) === 0);
                if (zeroPrice.length > 0) {
                    console.log('🔍 发现价格为0的项目，可能需要合并:', zeroPrice);
                    return true;
                }

                // 检查是否有相似的服务描述
                const serviceKeywords = ['pickup', 'airport', '接机', '送机', '专车', '快车', '出租车', 'taxi', 'car'];
                const vehicleKeywords = ['mpv', 'seater', '座', '人座', 'passengers', 'luggages'];

                let hasService = false;
                let hasVehicle = false;

                items.forEach(item => {
                    const desc = (item.description || '').toLowerCase();
                    if (serviceKeywords.some(keyword => desc.includes(keyword))) {
                        hasService = true;
                    }
                    if (vehicleKeywords.some(keyword => desc.includes(keyword))) {
                        hasVehicle = true;
                    }
                });

                // 如果同时有服务类型和车型信息的独立项目，建议合并
                if (hasService && hasVehicle) {
                    console.log('🔍 发现服务类型和车型信息分离，建议合并');
                    return true;
                }

                return false;
            },

            /**
             * 清理AI结果数据
             * @function cleanAIResults - 清理AI分析结果，移除司机信息
             * @param {Object} results - AI分析结果
             * @returns {Object} 清理后的结果
             */
            cleanAIResults(results) {
                if (!results || typeof results !== 'object') return results;

                const cleanedResults = { ...results };

                // 清理客户姓名
                if (cleanedResults.customerName) {
                    if (!this.validateCustomerName(cleanedResults.customerName)) {
                        console.warn('⚠️ 客户姓名验证失败，可能包含司机信息:', cleanedResults.customerName);
                        cleanedResults.customerName = '';
                    }
                }

                // 清理客户电话
                if (cleanedResults.customerPhone) {
                    if (!this.validatePhoneNumber(cleanedResults.customerPhone)) {
                        console.warn('⚠️ 客户电话验证失败:', cleanedResults.customerPhone);
                        cleanedResults.customerPhone = '';
                    }
                }

                // 清理备注信息
                if (cleanedResults.notes) {
                    cleanedResults.notes = this.removeSensitiveInfo(cleanedResults.notes);

                    // 如果备注主要是司机信息，则清空
                    if (this.containsDriverInfo(cleanedResults.notes) && !this.containsCustomerInfo(cleanedResults.notes)) {
                        console.warn('⚠️ 备注主要包含司机信息，已清空');
                        cleanedResults.notes = '';
                    }
                }

                // 清理项目描述
                if (cleanedResults.items && Array.isArray(cleanedResults.items)) {
                    cleanedResults.items = cleanedResults.items.map(item => {
                        if (item.description) {
                            item.description = this.removeSensitiveInfo(item.description);
                        }
                        return item;
                    });

                    // 智能合并项目
                    cleanedResults.items = this.mergeItems(cleanedResults.items);
                }

                console.log('🧹 数据清理完成:', {
                    原始结果: results,
                    清理后结果: cleanedResults
                });

                return cleanedResults;
            }
        };

        /**
         * 应用AI分析结果到表单
         * @function applyAIResults - 将AI分析结果填充到表单字段
         * @param {Object} results - AI分析结果对象
         */
        async function applyAIResults(results) {
            if (!results) return;

            console.log('🔄 开始应用AI分析结果到表单:', results);

            // 数据清理和验证
            const cleanedResults = DataCleaningValidator.cleanAIResults(results);
            console.log('🧹 数据清理后的结果:', cleanedResults);

            // 早期返回：检查多订单结果
            if (!cleanedResults.isMultiOrder || !cleanedResults.orders || !Array.isArray(cleanedResults.orders)) {
                // 继续处理单订单
            } else if (cleanedResults.orders.length > 1) {
                console.log(`🔄 检测到多订单: ${cleanedResults.orders.length}个订单，启用多订单模式`);
                // 对每个订单进行数据清理
                const cleanedOrders = cleanedResults.orders.map(order => DataCleaningValidator.cleanAIResults(order));
                await handleMultiOrderResults(cleanedOrders);
                return;
            } else if (cleanedResults.orders.length === 1) {
                console.log('🔄 检测到单订单（数组格式），使用单订单模式处理');
                const cleanedOrder = DataCleaningValidator.cleanAIResults(cleanedResults.orders[0]);
                await applySingleOrderResults(cleanedOrder);
                return;
            }

            // 单订单处理（移除isMultiOrder标记）
            const singleOrderData = { ...cleanedResults };
            delete singleOrderData.isMultiOrder;
            await applySingleOrderResults(singleOrderData);
        }

        /**
         * 处理多订单AI结果
         * @function handleMultiOrderResults - 处理多个订单的AI分析结果
         * @param {Array} orders - 订单数组
         */
        async function handleMultiOrderResults(orders) {
            console.log(`🔄 开始处理多订单AI结果: ${orders.length}个订单`);

            // 修复：只有真正的多订单才切换模式
            if (orders.length <= 1) {
                console.log('⚠️ 订单数量不足，不应使用多订单模式，转为单订单处理');
                if (orders.length === 1) {
                    await applySingleOrderResults(orders[0]);
                }
                return;
            }

            // 智能模式切换：自动切换到多订单模式
            if (!AppConfig.multiOrderMode) {
                console.log('🔄 智能模式切换：检测到多订单，自动切换到多订单模式');
                MultiOrderManager.toggleMode();
            }

            // 确保模式状态正确设置
            AppConfig.multiOrderMode = true;
            console.log(`✅ 多订单模式状态确认: ${AppConfig.multiOrderMode}`);

            // 清空现有订单数据
            AppConfig.multiOrderData = [];
            AppConfig.currentOrderIndex = 0;

            // 处理每个订单
            for (let i = 0; i < orders.length; i++) {
                const orderData = orders[i];
                console.log(`📋 处理订单 ${i + 1}/${orders.length}:`, orderData.documentNumber || `订单${i + 1}`);

                // 创建订单对象，确保项目金额正确计算
                const processedItems = (orderData.items || []).map(item => ({
                    ...item,
                    amount: calculateItemAmount(item.quantity || 1, item.price || 0)
                }));

                const newOrder = {
                    orderId: orderData.documentNumber || `ORD${String(AppConfig.orderCounter).padStart(3, '0')}`,
                    orderName: `订单${i + 1}`,
                    documentNumber: orderData.documentNumber || '',
                    customerName: orderData.customerName || '',
                    customerPhone: orderData.customerPhone || '',
                    customerEmail: orderData.customerEmail || '',
                    items: processedItems,
                    notes: orderData.notes || ''
                };

                AppConfig.multiOrderData.push(newOrder);
                AppConfig.orderCounter++;
            }

            // 切换到第一个订单
            AppConfig.currentOrderIndex = 0;
            MultiOrderManager.loadOrderToForm(AppConfig.multiOrderData[0]);
            MultiOrderManager.updateOrderTabs();
            MultiOrderManager.updateCurrentOrderDisplay();

            // 设置默认显示模式为合并显示
            AppConfig.displayMode = 'combined';
            const displayModeSelector = document.getElementById('display-mode');
            if (displayModeSelector) {
                displayModeSelector.value = 'combined';
            }

            // 自动切换到合并显示模式
            MultiOrderManager.showCombinedView();

            // 填充公司信息（所有订单共享）
            const firstOrder = orders[0];
            fillCompanyInfo(firstOrder);

            // 智能切换功能
            await performIntelligentSwitching(firstOrder);

            // 更新总金额和预览
            updateTotalAmount();

            // 多订单AI填充完成后强制更新预览
            console.log('🔄 多订单AI填充完成，强制更新预览');
            setTimeout(() => {
                try {
                    safeDebouncedUpdatePreview();
                    console.log('✅ 多订单预览更新成功');
                } catch (error) {
                    console.error('❌ 多订单预览更新失败:', error);
                    // 备用方案
                    updatePreview();
                }
            }, 300);

            console.log(`🎉 多订单AI结果处理完成: ${orders.length}个订单已创建`);

            // 输出多订单摘要
            const multiOrderSummary = orders.map((order, index) => ({
                序号: index + 1,
                单据号码: order.documentNumber || '未识别',
                客户名称: order.customerName || '未识别',
                项目数量: (order.items && order.items.length) || 0
            }));
            console.table(multiOrderSummary);
        }

        /**
         * 应用单订单AI结果
         * @function applySingleOrderResults - 应用单个订单的AI分析结果
         * @param {Object} results - 单订单AI分析结果
         */
        async function applySingleOrderResults(results) {
            console.log('🔄 开始应用单订单AI结果:', results);
            console.log('🔍 数据详细检查:', {
                documentNumber: results.documentNumber,
                customerName: results.customerName,
                customerPhone: results.customerPhone,
                customerEmail: results.customerEmail,
                companyName: results.companyName,
                itemsCount: results.items ? results.items.length : 0,
                itemsData: results.items,
                notes: results.notes
            });

            // 验证必要的DOM元素是否存在
            const domElements = {
                documentNumber: !!document.getElementById('document-number'),
                customerName: !!document.getElementById('customer-name'),
                customerPhone: !!document.getElementById('customer-phone'),
                itemsTbody: !!document.getElementById('items-tbody')
            };
            console.log('🔍 DOM元素检查:', domElements);

            // 智能模式切换：确保单订单模式
            if (AppConfig.multiOrderMode) {
                console.log('🔄 智能模式切换：检测到单订单，当前为多订单模式');
                console.log('ℹ️ 保持多订单模式，单订单数据将添加到当前订单');
            } else {
                console.log('✅ 当前为单订单模式，直接应用数据');
            }

            // 修复：增强单据号码填充逻辑
            console.log('🔍 检查单据号码:', results.documentNumber, typeof results.documentNumber);
            if (results.documentNumber) {
                // 使用多重DOM选择策略
                let documentNumberInput = null;

                // 尝试从缓存获取
                if (DOMCache.documentNumber) {
                    documentNumberInput = DOMCache.documentNumber;
                    console.log('✅ 从DOMCache获取单据号码输入框');
                } else {
                    // 直接查找DOM元素
                    documentNumberInput = document.getElementById('document-number');
                    console.log('✅ 直接从DOM获取单据号码输入框');
                }

                if (documentNumberInput) {
                    const currentValue = documentNumberInput.value.trim();
                    console.log('🔍 当前单据号码值:', currentValue);

                    // 如果当前单据号码为空，或者是自动生成的格式（INV/RCP开头），则使用AI识别的号码
                    if (!currentValue || currentValue.match(/^(INV|RCP)\d{14}$/)) {
                        documentNumberInput.value = String(results.documentNumber);
                        console.log('✅ 单据号码已填充:', results.documentNumber);

                        // 触发change事件以确保其他监听器响应
                        documentNumberInput.dispatchEvent(new Event('change', { bubbles: true }));
                    } else {
                        console.log('ℹ️ 单据号码已存在，跳过填充:', currentValue);
                    }
                } else {
                    console.error('❌ 无法找到单据号码输入框元素');
                }
            } else {
                console.log('⚠️ AI未识别到单据号码 - 数据中无documentNumber字段');
            }

            // 填充公司信息
            fillCompanyInfo(results);

            // 修复：增强客户信息填充逻辑
            console.log('🔍 开始填充客户信息:', {
                customerName: results.customerName,
                customerPhone: results.customerPhone,
                customerEmail: results.customerEmail,
                channel: results.channel
            });

            // 客户名称
            if (results.customerName) {
                let customerNameInput = DOMCache.customerName || document.getElementById('customer-name');
                if (customerNameInput) {
                    customerNameInput.value = String(results.customerName);
                    console.log('✅ 客户名称已填充:', results.customerName);
                    customerNameInput.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    console.error('❌ 无法找到客户名称输入框元素');
                }
            } else {
                console.log('⚠️ AI未识别到客户名称');
            }

            // 渠道信息
            if (results.channel) {
                let channelInput = DOMCache.channel || document.getElementById('channel');
                if (channelInput) {
                    channelInput.value = String(results.channel);
                    console.log('✅ 渠道已填充:', results.channel);
                    channelInput.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    console.log('⚠️ 渠道输入框不存在，跳过填充');
                }
            }

            // 客户电话
            if (results.customerPhone) {
                let customerPhoneInput = DOMCache.customerPhone || document.getElementById('customer-phone');
                if (customerPhoneInput) {
                    customerPhoneInput.value = String(results.customerPhone);
                    console.log('✅ 客户电话已填充:', results.customerPhone);
                    customerPhoneInput.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    console.error('❌ 无法找到客户电话输入框元素');
                }
            } else {
                console.log('⚠️ AI未识别到客户电话');
            }

            // 客户邮箱
            if (results.customerEmail) {
                let customerEmailInput = DOMCache.customerEmail || document.getElementById('customer-email');
                if (customerEmailInput) {
                    customerEmailInput.value = String(results.customerEmail);
                    console.log('✅ 客户邮箱已填充:', results.customerEmail);
                    customerEmailInput.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    console.log('⚠️ 客户邮箱输入框不存在，跳过填充');
                }
            }

            // 修复：增强备注填充逻辑
            if (results.notes) {
                let notesInput = DOMCache.notes || document.getElementById('notes');
                if (notesInput) {
                    notesInput.value = String(results.notes);
                    console.log('✅ 备注已填充:', results.notes);
                    notesInput.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    console.log('⚠️ 备注输入框不存在，跳过填充');
                }
            }

            // 修复：增强项目数据处理
            console.log('🔍 检查项目数据:', results.items);
            if (results.items && Array.isArray(results.items) && results.items.length > 0) {
                console.log(`🔄 开始处理 ${results.items.length} 个项目`);
                await fillItemsFromAI(results.items);
                console.log('✅ 项目数据已填充:', results.items.length + '个项目');
            } else {
                console.log('⚠️ 无有效项目数据:', {
                    hasItems: !!results.items,
                    isArray: Array.isArray(results.items),
                    length: results.items ? results.items.length : 0
                });
            }

            // 智能切换功能
            await performIntelligentSwitching(results);

            // 修复：强制更新总金额和预览
            console.log('🔄 开始更新总金额和预览');
            updateTotalAmount();

            // 确保AI填充后预览能够自动更新
            console.log('🔍 自动预览状态:', AppConfig.autoPreview);
            console.log('🔄 强制触发预览更新（AI填充完成）');

            // AI填充完成后，无论autoPreview设置如何，都应该更新预览
            // 使用延迟确保DOM更新完成
            setTimeout(() => {
                try {
                    safeDebouncedUpdatePreview();
                    console.log('✅ AI填充后预览更新已触发');
                } catch (error) {
                    console.error('❌ AI填充后预览更新失败:', error);
                    // 备用方案：直接调用updatePreview
                    try {
                        updatePreview();
                        console.log('✅ 使用备用方案更新预览成功');
                    } catch (backupError) {
                        console.error('❌ 备用预览更新也失败:', backupError);
                    }
                }
            }, 200); // 增加延迟时间，确保所有DOM操作完成

            console.log('🎉 AI分析结果已全部应用到表单完成');

            /**
         * 数据验证管道已移除
         * @note 原数据验证管道会导致性能问题和无限循环，已移除
         * @reason 在AI结果应用过程中，验证管道会触发额外的预览更新，
         *         导致递归调用和性能下降，因此采用更轻量级的验证方式
         */

            // 输出填充摘要
            const fillSummary = {
                单据号码: results.documentNumber || '未识别',
                公司名称: results.companyName || '未识别',
                客户名称: results.customerName || '未识别',
                项目数量: (results.items && results.items.length) || 0,
                备注: results.notes ? '已填充' : '未识别'
            };
            console.table(fillSummary);
        }

        // 数据验证状态管理
        let isValidatingDataTransfer = false;
        let dataValidationRetryCount = 0;
        const MAX_DATA_VALIDATION_RETRY = 2;

        /**
         * 验证数据传输管道
         * @function validateDataTransferPipeline - 验证从AI结果到DOM元素的完整数据传输
         * @param {Object} originalResults - 原始AI分析结果
         */
        async function validateDataTransferPipeline(originalResults) {
            // 防止重复验证
            if (isValidatingDataTransfer) {
                console.log('⚠️ 数据传输验证正在进行中，跳过重复调用');
                return;
            }

            // 检查重试次数
            if (dataValidationRetryCount >= MAX_DATA_VALIDATION_RETRY) {
                console.log('⚠️ 数据验证重试次数已达上限，停止验证');
                dataValidationRetryCount = 0;
                return;
            }

            isValidatingDataTransfer = true;
            console.log(`🔍 开始验证数据传输管道 (第${dataValidationRetryCount + 1}次)`);

            // 1. 验证DOM元素状态
            const domValidation = {
                documentNumber: document.getElementById('document-number')?.value || '',
                customerName: document.getElementById('customer-name')?.value || '',
                customerPhone: document.getElementById('customer-phone')?.value || '',
                customerEmail: document.getElementById('customer-email')?.value || '',
                notes: document.getElementById('notes')?.value || '',
                itemsTableRows: document.querySelectorAll('#items-tbody tr').length
            };

            console.log('📋 DOM元素当前状态:', domValidation);

            // 2. 验证表格数据
            const tableValidation = [];
            const rows = document.querySelectorAll('#items-tbody tr');
            rows.forEach((row, index) => {
                const description = row.querySelector('.item-description')?.value || '';
                const quantity = parseFloat(row.querySelector('.item-quantity')?.value || 0);
                const price = parseFloat(row.querySelector('.item-price')?.value || 0);
                const amount = row.querySelector('.item-amount')?.textContent || '';

                tableValidation.push({
                    行号: index + 1,
                    描述: description,
                    数量: quantity,
                    价格: price,
                    金额: amount
                });
            });

            console.log('📊 表格数据验证:', tableValidation);

            // 3. 验证总金额计算
            const calculatedTotal = calculateTotalAmount();
            console.log('💰 总金额验证:', {
                计算结果: formatCurrency(calculatedTotal),
                显示元素: document.getElementById('total-amount')?.textContent || '未找到'
            });

            // 4. 对比原始数据和最终结果
            const comparisonResult = {
                单据号码匹配: originalResults.documentNumber === domValidation.documentNumber,
                客户名称匹配: originalResults.customerName === domValidation.customerName,
                客户电话匹配: originalResults.customerPhone === domValidation.customerPhone,
                项目数量匹配: (originalResults.items?.length || 0) === domValidation.itemsTableRows,
                总金额大于零: calculatedTotal > 0
            };

            console.log('🔍 数据匹配验证:', comparisonResult);

            // 5. 检查失败项目并提供修复建议
            const failures = [];
            if (!comparisonResult.单据号码匹配) {
                failures.push(`单据号码不匹配: 期望"${originalResults.documentNumber}", 实际"${domValidation.documentNumber}"`);
            }
            if (!comparisonResult.客户名称匹配) {
                failures.push(`客户名称不匹配: 期望"${originalResults.customerName}", 实际"${domValidation.customerName}"`);
            }
            if (!comparisonResult.项目数量匹配) {
                failures.push(`项目数量不匹配: 期望${originalResults.items?.length || 0}, 实际${domValidation.itemsTableRows}`);
            }
            if (!comparisonResult.总金额大于零) {
                failures.push(`总金额为零: 计算结果${formatCurrency(calculatedTotal)}`);
            }

            if (failures.length > 0) {
                console.error('❌ 数据传输管道验证失败:', failures);

                // 尝试自动修复（但要防止无限循环）
                dataValidationRetryCount++;
                if (dataValidationRetryCount < MAX_DATA_VALIDATION_RETRY) {
                    console.log(`🔧 尝试自动修复数据传输问题 (第${dataValidationRetryCount}次)`);
                    await attemptDataTransferFix(originalResults);
                } else {
                    console.log('⚠️ 数据传输修复重试次数已达上限，停止修复尝试');
                }
            } else {
                console.log('✅ 数据传输管道验证通过');
                dataValidationRetryCount = 0; // 重置计数器
            }

            isValidatingDataTransfer = false; // 释放验证锁
        }

        /**
         * 尝试修复数据传输问题
         * @function attemptDataTransferFix - 尝试自动修复数据传输失败
         * @param {Object} originalResults - 原始AI分析结果
         */
        async function attemptDataTransferFix(originalResults) {
            console.log('🔧 开始自动修复数据传输问题');

            // 强制重新填充单据号码
            if (originalResults.documentNumber) {
                const docInput = document.getElementById('document-number');
                if (docInput) {
                    docInput.value = String(originalResults.documentNumber);
                    docInput.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('🔧 强制修复单据号码');
                }
            }

            // 强制重新填充客户信息
            if (originalResults.customerName) {
                const nameInput = document.getElementById('customer-name');
                if (nameInput) {
                    nameInput.value = String(originalResults.customerName);
                    nameInput.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('🔧 强制修复客户名称');
                }
            }

            if (originalResults.customerPhone) {
                const phoneInput = document.getElementById('customer-phone');
                if (phoneInput) {
                    phoneInput.value = String(originalResults.customerPhone);
                    phoneInput.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('🔧 强制修复客户电话');
                }
            }

            // 强制重新填充项目数据
            if (originalResults.items && originalResults.items.length > 0) {
                console.log('🔧 强制重新填充项目数据');
                await fillItemsForSingleOrder(originalResults.items);
            }

            // 临时禁用强制更新，防止无限循环
            // updateTotalAmount();
            // updatePreview();

            console.log('🔧 自动修复完成');
            // 移除自动重新验证，防止无限循环
            // 验证将在上层函数中根据重试计数器决定是否继续
        }

        /**
         * 填充公司信息
         * @function fillCompanyInfo - 填充公司相关信息到表单（根据文档类型动态控制）
         * @param {Object} results - 包含公司信息的结果对象
         */
        function fillCompanyInfo(results) {
            // 获取当前文档类型
            const currentDocType = document.getElementById('document-type')?.value || 'receipt';

            // 如果是收据模式，跳过公司信息填充
            if (currentDocType === 'receipt') {
                console.log('📋 当前为收据模式，跳过公司信息填充');
                return;
            }

            console.log('📋 当前为发票模式，开始填充公司信息');

            if (results.companyName) {
                const companyNameElement = document.getElementById('company-name');
                if (companyNameElement) {
                    companyNameElement.value = results.companyName;
                    console.log('✅ 公司名称已填充:', results.companyName);
                }
            }
            if (results.taxId) {
                const taxIdElement = document.getElementById('tax-id');
                if (taxIdElement) {
                    taxIdElement.value = results.taxId;
                    console.log('✅ 税号已填充:', results.taxId);
                }
            }
            if (results.companyAddress) {
                const companyAddressElement = document.getElementById('company-address');
                if (companyAddressElement) {
                    companyAddressElement.value = results.companyAddress;
                    console.log('✅ 公司地址已填充:', results.companyAddress);
                }
            }
            if (results.companyPhone) {
                const companyPhoneElement = document.getElementById('company-phone');
                if (companyPhoneElement) {
                    companyPhoneElement.value = results.companyPhone;
                    console.log('✅ 公司电话已填充:', results.companyPhone);
                }
            }
            if (results.contactPerson) {
                const contactPersonElement = document.getElementById('contact-person');
                if (contactPersonElement) {
                    contactPersonElement.value = results.contactPerson;
                    console.log('✅ 负责人已填充:', results.contactPerson);
                }
            }
        }

        /**
         * 执行智能切换功能
         * @function performIntelligentSwitching - 根据AI识别结果执行智能切换（尊重用户的文档类型选择）
         * @param {Object} results - AI分析结果
         * @param {boolean} allowDocumentTypeSwitch - 是否允许自动切换文档类型（默认为true）
         */
        async function performIntelligentSwitching(results, allowDocumentTypeSwitch = true) {
            const currentDocType = document.getElementById('document-type')?.value || 'receipt';

            // 1. 根据公司信息自动切换到发票模式（仅在允许且当前为收据模式时）
            if (allowDocumentTypeSwitch && (results.companyName || results.taxId)) {
                const docTypeSelect = DOMCache.documentType || document.getElementById('document-type');
                if (docTypeSelect && docTypeSelect.value === 'receipt') {
                    docTypeSelect.value = 'invoice';
                    toggleCompanyFields('invoice');
                    console.log('🔄 AI检测到公司信息，已自动切换到发票模式');
                }
            } else if (!allowDocumentTypeSwitch && (results.companyName || results.taxId)) {
                console.log('📋 AI检测到公司信息，但用户已明确选择收据模式，跳过自动切换');
            }

            // 2. 根据货币信息自动切换货币类型
            if (results.currency) {
                const currencyCode = detectCurrencyFromText(results.currency);
                if (currencyCode && currencyCode !== AppConfig.currentCurrency) {
                    switchCurrency(currencyCode);
                    const currencySelector = DOMCache.currencySelector || document.getElementById('currency-selector');
                    if (currencySelector) {
                        currencySelector.value = currencyCode;
                    }
                    console.log(`🔄 AI检测到货币信息，已自动切换到${CurrencyConfig[currencyCode].name}`);
                }
            }

            // 3. 根据公司类型自动切换公司选择器
            if (results.companyName) {
                const companyCode = detectCompanyFromName(results.companyName);
                if (companyCode && companyCode !== AppConfig.currentCompany) {
                    AppConfig.currentCompany = companyCode;
                    const companySelector = DOMCache.companySelector || document.getElementById('company-selector');
                    if (companySelector) {
                        companySelector.value = companyCode;
                    }
                    console.log(`🔄 AI检测到公司信息，已自动切换到${companyCode}`);
                }
            }
        }

        /**
         * 从文本中检测货币类型
         * @function detectCurrencyFromText - 从文本中检测货币类型
         * @param {string} text - 包含货币信息的文本
         * @returns {string|null} 货币代码或null
         */
        function detectCurrencyFromText(text) {
            const lowerText = text.toLowerCase();

            // 检测马来西亚令吉
            if (lowerText.includes('rm') || lowerText.includes('ringgit') ||
                lowerText.includes('malaysia') || lowerText.includes('myr')) {
                return 'MYR';
            }

            // 检测人民币
            if (lowerText.includes('¥') || lowerText.includes('yuan') ||
                lowerText.includes('rmb') || lowerText.includes('cny') ||
                lowerText.includes('人民币')) {
                return 'CNY';
            }

            return null;
        }

        /**
         * 从公司名称检测公司代码
         * @function detectCompanyFromName - 从公司名称检测公司代码
         * @param {string} companyName - 公司名称
         * @returns {string|null} 公司代码或null
         */
        function detectCompanyFromName(companyName) {
            const lowerName = companyName.toLowerCase();

            // 检测GoMyHire
            if (lowerName.includes('gomyhire') || lowerName.includes('go my hire')) {
                return 'gomyhire';
            }

            // 检测Sky Mirror
            if (lowerName.includes('sky mirror') || lowerName.includes('skymirror')) {
                return 'sky-mirror';
            }

            return null;
        }

        /**
         * 从AI结果填充项目数据（支持多订单）
         * @function fillItemsFromAI - 从AI分析结果填充项目表格
         * @param {Array} items - 项目数组
         * @param {string} orderId - 订单ID（可选，用于多订单场景）
         */
        async function fillItemsFromAI(items, orderId = null) {
            console.log('🔄 开始填充AI识别的项目数据:', items.length + '个项目');
            console.log('🔍 项目数据详情:', items);

            const tbody = document.getElementById('items-tbody');
            if (!tbody) {
                console.error('❌ 无法找到项目表格tbody元素');
                return;
            }

            console.log('✅ 找到项目表格元素');

            // 根据模式决定处理方式
            if (AppConfig.multiOrderMode && orderId) {
                console.log('🔄 使用多订单模式填充');
                await fillItemsForMultiOrder(items, orderId);
            } else {
                console.log('🔄 使用单订单模式填充');
                await fillItemsForSingleOrder(items);
            }

            // 安全地更新总金额
            console.log('🔄 安全更新总金额');
            setTimeout(() => {
                try {
                    updateTotalAmount();
                    console.log('✅ 总金额更新完成');
                } catch (error) {
                    console.error('❌ 总金额更新失败:', error);
                }
            }, 200);
            console.log('✅ 项目数据填充完成');
        }

        /**
         * 单订单模式填充项目
         * @function fillItemsForSingleOrder - 单订单模式下填充项目
         * @param {Array} items - 项目数组
         */
        async function fillItemsForSingleOrder(items) {
            const tbody = document.getElementById('items-tbody');

            console.log('🔄 单订单模式填充项目 - 输入数据:', items);
            console.log('🔍 表格元素检查:', tbody ? '✅ 找到' : '❌ 未找到');

            if (!tbody) {
                console.error('❌ 无法找到items-tbody元素，项目填充失败');
                return;
            }

            // 清空现有项目
            SafeDOMBuilder.safeReplaceContent(tbody, []);
            console.log('✅ 已清空现有项目');

            // 验证输入数据
            if (!items || !Array.isArray(items) || items.length === 0) {
                console.error('❌ 无效的项目数据:', items);
                return;
            }

            // 添加AI识别的项目
            items.forEach((item, index) => {
                try {
                    // 修复：确保数值类型正确转换和验证
                    const description = String(item.description || '').trim();

                    // 增强数量处理：确保为正整数
                    let quantity = parseFloat(item.quantity);
                    if (isNaN(quantity) || quantity <= 0) {
                        quantity = 1; // 默认数量为1
                    }
                    quantity = Math.max(1, Math.round(quantity)); // 确保为正整数

                    // 增强价格处理：支持多种格式并确保为有效数字
                    let price = 0;
                    if (item.price !== undefined && item.price !== null && item.price !== '') {
                        if (typeof item.price === 'string') {
                            // 清理价格字符串：移除货币符号、空格等
                            const cleanPrice = item.price.replace(/[^\d.-]/g, '');
                            price = parseFloat(cleanPrice);
                        } else {
                            price = parseFloat(item.price);
                        }
                    }

                    // 确保价格为有效的非负数
                    if (isNaN(price) || price < 0) {
                        price = 0;
                    }

                    const amount = quantity * price;

                    console.log(`📝 处理项目 ${index + 1}:`, {
                        描述原始: item.description,
                        描述处理: description,
                        数量原始: item.quantity,
                        数量类型: typeof item.quantity,
                        数量转换: quantity,
                        价格原始: item.price,
                        价格类型: typeof item.price,
                        价格转换: price,
                        金额计算: amount
                    });

                    // 验证必要数据
                    if (!description) {
                        console.warn(`⚠️ 项目 ${index + 1} 缺少描述，跳过`);
                        return;
                    }

                    const row = document.createElement('tr');
                    const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';
                    const currentOrderName = AppConfig.multiOrderMode && AppConfig.multiOrderData[AppConfig.currentOrderIndex]
                        ? AppConfig.multiOrderData[AppConfig.currentOrderIndex].orderName
                        : '';

                    // 使用安全的HTML转义
                    const safeDescription = description.replace(/"/g, '&quot;').replace(/'/g, '&#39;');

                    row.innerHTML = `
                        <td class="order-column" style="display: ${orderColumnDisplay};">
                            <span class="order-badge">${currentOrderName}</span>
                        </td>
                        <td><input type="text" value="${safeDescription}" class="item-description" title="项目描述 / Item description"></td>
                        <td><input type="number" value="${quantity}" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                        <td><input type="number" value="${price}" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                        <td class="item-amount">${formatCurrency(amount)}</td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                    `;
                    tbody.appendChild(row);

                    // 验证行是否正确添加
                    const addedRow = tbody.lastElementChild;
                    if (!addedRow) {
                        console.error(`❌ 项目 ${index + 1} 行添加失败`);
                        return;
                    }

                    // 为新行绑定事件
                    const quantityInput = addedRow.querySelector('.item-quantity');
                    const priceInput = addedRow.querySelector('.item-price');
                    const descInput = addedRow.querySelector('.item-description');

                    if (quantityInput && priceInput && descInput) {
                        // 检查是否已经绑定过事件监听器，避免重复绑定
                        if (!quantityInput.hasAttribute('data-event-bound')) {
                            quantityInput.addEventListener('input', () => updateItemAmount(addedRow));
                            quantityInput.setAttribute('data-event-bound', 'true');
                        }

                        if (!priceInput.hasAttribute('data-event-bound')) {
                            priceInput.addEventListener('input', () => updateItemAmount(addedRow));
                            priceInput.setAttribute('data-event-bound', 'true');
                        }

                        // 为描述输入框添加自动预览更新
                        if (AppConfig.autoPreview && !descInput.hasAttribute('data-preview-bound')) {
                            descInput.addEventListener('input', () => safeDebouncedUpdatePreview());
                            descInput.setAttribute('data-preview-bound', 'true');
                        }

                        // 触发change事件以确保计算正确
                        quantityInput.dispatchEvent(new Event('input', { bubbles: true }));
                        priceInput.dispatchEvent(new Event('input', { bubbles: true }));

                        console.log(`✅ 项目 ${index + 1} 已添加到表格，金额: ${formatCurrency(amount)}`);
                    } else {
                        console.error(`❌ 项目 ${index + 1} 输入框元素查找失败`);
                    }
                } catch (error) {
                    console.error(`❌ 处理项目 ${index + 1} 时发生错误:`, error);
                }
            });

            console.log(`✅ 单订单模式填充完成，共 ${items.length} 个项目`);

            // 验证最终结果
            const finalRows = tbody.querySelectorAll('tr');
            console.log(`🔍 最终表格行数: ${finalRows.length}`);
        }

        /**
         * 多订单模式填充项目
         * @function fillItemsForMultiOrder - 多订单模式下填充项目到指定订单
         * @param {Array} items - 项目数组
         * @param {string} orderId - 目标订单ID
         */
        async function fillItemsForMultiOrder(items, orderId) {
            // 查找目标订单
            const orderIndex = AppConfig.multiOrderData.findIndex(order => order.orderId === orderId);
            if (orderIndex === -1) {
                console.warn('⚠️ 未找到指定订单:', orderId);
                return;
            }

            // 保存当前订单数据
            if (AppConfig.currentOrderIndex !== orderIndex) {
                MultiOrderManager.saveCurrentOrderData();
            }

            // 切换到目标订单
            AppConfig.currentOrderIndex = orderIndex;

            // 将项目添加到目标订单
            const targetOrder = AppConfig.multiOrderData[orderIndex];
            targetOrder.items = targetOrder.items || [];

            // 合并项目（避免重复）
            items.forEach(newItem => {
                const existingItem = targetOrder.items.find(item =>
                    item.description === newItem.description
                );

                if (existingItem) {
                    // 更新现有项目
                    existingItem.quantity = newItem.quantity;
                    existingItem.price = newItem.price;
                    existingItem.amount = newItem.quantity * newItem.price;
                } else {
                    // 添加新项目
                    targetOrder.items.push({
                        description: newItem.description,
                        quantity: newItem.quantity,
                        price: newItem.price,
                        amount: newItem.quantity * newItem.price
                    });
                }
            });

            // 重新加载表格显示
            MultiOrderManager.loadOrderToForm(targetOrder);
            MultiOrderManager.updateOrderTabs();
            MultiOrderManager.updateCurrentOrderDisplay();

            console.log(`✅ 已将${items.length}个项目添加到订单${orderId}`);
        }

        // 创建优化的防抖预览更新函数 - 缩短延迟时间以提供更好的实时体验
        const debouncedUpdatePreview = DebounceManager.debounce(updatePreview, 300, 'preview_update');

        /**
         * 性能优化的预览更新管理器
         * @description 管理预览更新的性能优化
         */
        const PreviewManager = {
            isUpdating: false,
            pendingUpdate: false,
            updateRetryCount: 0,
            maxRetries: 3,

            /**
             * 安全的预览更新
             * @function safeUpdatePreview - 安全的预览更新，避免重复调用
             */
            async safeUpdatePreview() {
                if (this.isUpdating) {
                    this.pendingUpdate = true;
                    return;
                }

                // 检查重试次数，防止无限循环
                if (this.updateRetryCount >= this.maxRetries) {
                    console.warn('⚠️ 预览更新重试次数已达上限，停止更新');
                    this.updateRetryCount = 0;
                    this.pendingUpdate = false;
                    return;
                }

                this.isUpdating = true;
                try {
                    // 检查关键DOM元素是否存在
                    const documentContainer = document.getElementById('document-container');
                    const documentPreview = document.getElementById('document-preview');

                    if (!documentContainer && !documentPreview) {
                        console.error('❌ 关键预览元素缺失，跳过预览更新');
                        return;
                    }

                    // 检查预览模块是否可用
                    if (typeof PreviewModule !== 'undefined' && PreviewModule.updatePreview) {
                        await PreviewModule.updatePreview();
                    } else if (typeof updatePreview === 'function') {
                        await updatePreview();
                    } else {
                        console.warn('⚠️ 预览更新函数不可用');
                        return;
                    }

                    // 成功更新，重置重试计数器
                    this.updateRetryCount = 0;

                    // 如果有待处理的更新，再次执行（但要检查重试次数）
                    if (this.pendingUpdate) {
                        this.pendingUpdate = false;
                        this.updateRetryCount++;
                        if (this.updateRetryCount < this.maxRetries) {
                            setTimeout(() => this.safeUpdatePreview(), 100);
                        } else {
                            console.warn('⚠️ 待处理更新重试次数已达上限');
                            this.updateRetryCount = 0;
                        }
                    }
                } catch (error) {
                    console.error('预览更新失败:', error);
                    this.updateRetryCount++;

                    // 只在重试次数未达上限时尝试恢复
                    if (this.updateRetryCount < this.maxRetries) {
                        try {
                            const previewContainer = document.getElementById('document-preview');
                            if (previewContainer && !document.getElementById('document-container')) {
                                console.log(`🔧 尝试恢复预览容器结构 (第${this.updateRetryCount}次)`);
                                previewContainer.innerHTML = `
                                    <div id="document-container">
                                        <div class="empty-preview-message">
                                            预览功能已恢复，请重新更新预览<br>
                                            Preview function restored, please update preview again
                                        </div>
                                    </div>
                                `;
                            }
                        } catch (recoveryError) {
                            console.error('❌ 预览功能恢复失败:', recoveryError);
                        }
                    }
                } finally {
                    this.isUpdating = false;
                }
            }
        };

        // 创建安全的防抖预览更新函数 - 优化延迟时间以提供更好的实时体验
        const safeDebouncedUpdatePreview = DebounceManager.debounce(
            () => {
                try {
                    PreviewManager.safeUpdatePreview();
                } catch (error) {
                    console.error('❌ 防抖预览更新失败:', error);
                    // 显示用户友好的错误提示
                    const container = document.getElementById('document-container');
                    if (container) {
                        container.innerHTML = `
                            <div class="error-message" style="color: red; padding: 20px; text-align: center;">
                                预览更新失败，请检查输入数据<br>
                                Preview update failed, please check input data<br>
                                <small>${error.message}</small>
                            </div>
                        `;
                    }
                }
            },
            200, // 减少延迟时间，提供更快的响应
            'safe_preview_update'
        );

        /**
         * 错误处理和性能监控系统
         * @description 统一的错误处理和性能监控
         */
        const ErrorManager = {
            errors: [],
            maxErrors: 50,

            /**
             * 记录错误
             * @function logError - 记录错误信息
             * @param {Error} error - 错误对象
             * @param {string} context - 错误上下文
             */
            logError(error, context = 'Unknown') {
                const errorInfo = {
                    timestamp: new Date().toISOString(),
                    message: error.message,
                    stack: error.stack,
                    context: context
                };

                this.errors.push(errorInfo);

                // 保持错误日志数量在限制内
                if (this.errors.length > this.maxErrors) {
                    this.errors.shift();
                }

                console.error(`[${context}] 错误:`, error);
            },

            /**
             * 安全执行函数
             * @function safeExecute - 安全执行函数，捕获错误
             * @param {Function} func - 要执行的函数
             * @param {string} context - 执行上下文
             * @param {...any} args - 函数参数
             * @returns {any} 函数执行结果或null
             */
            safeExecute(func, context, ...args) {
                try {
                    return func(...args);
                } catch (error) {
                    this.logError(error, context);
                    return null;
                }
            },

            /**
             * 获取错误统计
             * @function getErrorStats - 获取错误统计信息
             * @returns {Object} 错误统计
             */
            getErrorStats() {
                return {
                    totalErrors: this.errors.length,
                    recentErrors: this.errors.slice(-10),
                    errorsByContext: this.errors.reduce((acc, error) => {
                        acc[error.context] = (acc[error.context] || 0) + 1;
                        return acc;
                    }, {})
                };
            }
        };
        // #endregion

        // #region 多订单管理功能
        /**
         * 多订单管理器
         * @description 管理多个订单的数据和界面
         */
        const MultiOrderManager = {
            /**
             * 切换多订单模式
             * @function toggleMode - 切换单订单/多订单模式
             */
            toggleMode() {
                AppConfig.multiOrderMode = !AppConfig.multiOrderMode;
                const container = document.getElementById('multi-order-container');
                const orderColumn = document.getElementById('order-column-header');
                const orderCells = document.querySelectorAll('.order-column');

                if (AppConfig.multiOrderMode) {
                    container.style.display = 'block';
                    orderColumn.style.display = 'table-cell';
                    orderCells.forEach(cell => cell.style.display = 'table-cell');
                    this.initializeMultiOrderMode();
                    console.log('✅ 已切换到多订单模式');
                } else {
                    container.style.display = 'none';
                    orderColumn.style.display = 'none';
                    orderCells.forEach(cell => cell.style.display = 'none');
                    this.resetToSingleMode();
                    console.log('✅ 已切换到单订单模式');
                }
            },

            /**
             * 初始化多订单模式
             * @function initializeMultiOrderMode - 初始化多订单界面和数据
             */
            initializeMultiOrderMode() {
                if (AppConfig.multiOrderData.length === 0) {
                    // 创建第一个订单
                    this.createOrderFromCurrentForm();
                }
                this.updateOrderTabs();
                this.updateCurrentOrderDisplay();

                // 设置默认显示模式为合并显示
                AppConfig.displayMode = 'combined';
                const displayModeSelector = document.getElementById('display-mode');
                if (displayModeSelector) {
                    displayModeSelector.value = 'combined';
                }

                console.log('✅ 多订单模式初始化完成，默认为合并显示模式');
            },

            /**
             * 从当前表单创建订单
             * @function createOrderFromCurrentForm - 从当前表单数据创建新订单
             */
            createOrderFromCurrentForm() {
                const orderData = {
                    orderId: `ORD${String(AppConfig.orderCounter).padStart(3, '0')}`,
                    orderName: `订单${AppConfig.orderCounter}`,
                    documentNumber: document.getElementById('document-number').value || '',
                    customerName: document.getElementById('customer-name').value || '',
                    customerPhone: document.getElementById('customer-phone').value || '',
                    customerEmail: document.getElementById('customer-email').value || '',
                    items: this.getCurrentItems(),
                    notes: document.getElementById('notes').value || ''
                };

                AppConfig.multiOrderData.push(orderData);
                AppConfig.orderCounter++;
                console.log('📋 已创建新订单:', orderData.orderId);
                return orderData;
            },

            /**
             * 获取当前表格中的项目数据
             * @function getCurrentItems - 获取当前项目表格中的所有项目
             * @returns {Array} 项目数组
             */
            getCurrentItems() {
                const items = [];
                const rows = document.querySelectorAll('#items-tbody tr');

                rows.forEach(row => {
                    const description = row.querySelector('.item-description').value.trim();
                    const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
                    const price = parseFloat(row.querySelector('.item-price').value) || 0;

                    if (description || quantity > 0 || price > 0) {
                        items.push({
                            description,
                            quantity,
                            price,
                            amount: quantity * price
                        });
                    }
                });

                return items;
            },

            /**
             * 更新订单标签页
             * @function updateOrderTabs - 更新订单标签页显示
             */
            updateOrderTabs() {
                const tabsContainer = document.getElementById('order-tabs');
                if (!tabsContainer) return;

                tabsContainer.innerHTML = '';

                AppConfig.multiOrderData.forEach((order, index) => {
                    const tab = document.createElement('button');
                    tab.className = `order-tab ${index === AppConfig.currentOrderIndex ? 'active' : ''}`;
                    tab.textContent = order.orderName;
                    tab.onclick = () => this.switchToOrder(index);
                    tabsContainer.appendChild(tab);
                });
            },

            /**
             * 更新当前订单显示
             * @function updateCurrentOrderDisplay - 更新当前订单信息显示
             */
            updateCurrentOrderDisplay() {
                const currentOrder = AppConfig.multiOrderData[AppConfig.currentOrderIndex];
                if (!currentOrder) return;

                const orderDisplay = document.getElementById('current-order-display');
                const customerDisplay = document.getElementById('current-customer-display');

                if (orderDisplay) orderDisplay.textContent = currentOrder.documentNumber || currentOrder.orderId;
                if (customerDisplay) customerDisplay.textContent = currentOrder.customerName || '未填写';
            },

            /**
             * 切换到指定订单
             * @function switchToOrder - 切换到指定索引的订单
             * @param {number} orderIndex - 订单索引
             */
            switchToOrder(orderIndex) {
                if (orderIndex < 0 || orderIndex >= AppConfig.multiOrderData.length) return;

                // 保存当前订单数据
                if (AppConfig.currentOrderIndex !== orderIndex) {
                    this.saveCurrentOrderData();
                }

                // 切换到新订单
                AppConfig.currentOrderIndex = orderIndex;
                this.loadOrderToForm(AppConfig.multiOrderData[orderIndex]);
                this.updateOrderTabs();
                this.updateCurrentOrderDisplay();

                // 根据当前显示模式更新单据号码
                if (AppConfig.displayMode === 'combined') {
                    this.updateCombinedDocumentNumber();
                } else {
                    // 分别显示模式下恢复当前订单的独立单据号码
                    const currentOrder = AppConfig.multiOrderData[orderIndex];
                    const documentNumberInput = document.getElementById('document-number');
                    if (documentNumberInput && currentOrder.documentNumber) {
                        documentNumberInput.value = currentOrder.documentNumber;
                    }
                }

                console.log(`🔄 已切换到订单: ${AppConfig.multiOrderData[orderIndex].orderId}`);
            },

            /**
             * 保存当前订单数据
             * @function saveCurrentOrderData - 保存当前表单数据到订单
             */
            saveCurrentOrderData() {
                const currentOrder = AppConfig.multiOrderData[AppConfig.currentOrderIndex];
                if (!currentOrder) return;

                currentOrder.documentNumber = document.getElementById('document-number').value;
                currentOrder.customerName = document.getElementById('customer-name').value;
                currentOrder.customerPhone = document.getElementById('customer-phone').value;
                currentOrder.customerEmail = document.getElementById('customer-email').value;
                currentOrder.items = this.getCurrentItems();
                currentOrder.notes = document.getElementById('notes').value;

                console.log('💾 已保存订单数据:', currentOrder.orderId);
            },

            /**
             * 加载订单数据到表单
             * @function loadOrderToForm - 将订单数据加载到表单
             * @param {Object} orderData - 订单数据
             */
            loadOrderToForm(orderData) {
                // 填充基本信息
                document.getElementById('document-number').value = orderData.documentNumber || '';
                document.getElementById('customer-name').value = orderData.customerName || '';
                document.getElementById('customer-phone').value = orderData.customerPhone || '';
                document.getElementById('customer-email').value = orderData.customerEmail || '';
                document.getElementById('notes').value = orderData.notes || '';

                // 填充项目数据
                this.loadItemsToTable(orderData.items || []);

                // 更新总金额
                updateTotalAmount();
            },

            /**
             * 加载项目到表格
             * @function loadItemsToTable - 将项目数据加载到表格
             * @param {Array} items - 项目数组
             */
            loadItemsToTable(items) {
                const tbody = document.getElementById('items-tbody');
                tbody.innerHTML = '';

                if (items.length === 0) {
                    // 添加空行
                    addItem();
                    return;
                }

                items.forEach(item => {
                    const row = document.createElement('tr');
                    const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';
                    const currentOrderName = AppConfig.multiOrderData[AppConfig.currentOrderIndex]?.orderName || '';

                    row.innerHTML = `
                        <td class="order-column" style="display: ${orderColumnDisplay};">
                            <span class="order-badge">${currentOrderName}</span>
                        </td>
                        <td><input type="text" value="${item.description || ''}" class="item-description" title="项目描述 / Item description"></td>
                        <td><input type="number" value="${item.quantity || 1}" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                        <td><input type="number" value="${item.price || 0}" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                        <td class="item-amount">${formatCurrency(item.amount || 0)}</td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                    `;

                    tbody.appendChild(row);

                    // 绑定事件
                    const quantityInput = row.querySelector('.item-quantity');
                    const priceInput = row.querySelector('.item-price');
                    const descInput = row.querySelector('.item-description');

                    // 检查是否已经绑定过事件监听器，避免重复绑定
                    if (!quantityInput.hasAttribute('data-event-bound')) {
                        quantityInput.addEventListener('input', () => updateItemAmount(row));
                        quantityInput.setAttribute('data-event-bound', 'true');
                    }

                    if (!priceInput.hasAttribute('data-event-bound')) {
                        priceInput.addEventListener('input', () => updateItemAmount(row));
                        priceInput.setAttribute('data-event-bound', 'true');
                    }

                    if (AppConfig.autoPreview && !descInput.hasAttribute('data-preview-bound')) {
                        descInput.addEventListener('input', () => safeDebouncedUpdatePreview());
                        descInput.setAttribute('data-preview-bound', 'true');
                    }
                });
            },

            /**
             * 重置到单订单模式
             * @function resetToSingleMode - 重置到单订单模式
             */
            resetToSingleMode() {
                AppConfig.multiOrderData = [];
                AppConfig.currentOrderIndex = 0;
                AppConfig.orderCounter = 1;

                // 重新加载表格，移除订单列
                const tbody = document.getElementById('items-tbody');
                const rows = tbody.querySelectorAll('tr');

                rows.forEach(row => {
                    const orderCell = row.querySelector('.order-column');
                    if (orderCell) {
                        orderCell.style.display = 'none';
                    }
                });
            },

            /**
             * 显示合并视图
             * @function showCombinedView - 在单个表格中显示所有订单的项目
             */
            showCombinedView() {
                console.log('🔄 切换到合并显示模式');

                if (AppConfig.multiOrderData.length === 0) {
                    console.warn('⚠️ 没有订单数据可显示');
                    return;
                }

                // 保存当前订单数据
                this.saveCurrentOrderData();

                // 清空表格并添加合并视图样式
                const tbody = document.getElementById('items-tbody');
                const table = document.getElementById('items-table');
                tbody.innerHTML = '';
                table.classList.add('combined-view');

                let totalAmount = 0;
                let totalItems = 0;

                // 遍历所有订单，添加项目到表格
                AppConfig.multiOrderData.forEach((order, orderIndex) => {
                    if (order.items && order.items.length > 0) {
                        order.items.forEach((item, itemIndex) => {
                            const row = document.createElement('tr');
                            const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';

                            row.innerHTML = `
                                <td class="order-column" style="display: ${orderColumnDisplay};">
                                    <span class="order-badge" style="background: ${this.getOrderColor(orderIndex)};">${order.orderName}</span>
                                </td>
                                <td><input type="text" value="${item.description || ''}" class="item-description" title="项目描述 / Item description" readonly></td>
                                <td><input type="number" value="${item.quantity || 1}" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity" readonly></td>
                                <td><input type="number" value="${item.price || 0}" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price" readonly></td>
                                <td class="item-amount">${formatCurrency(item.amount || 0)}</td>
                                <td>
                                    <span class="order-info" style="font-size: 12px; color: #666;">
                                        ${order.customerName || '未知客户'}
                                    </span>
                                </td>
                            `;

                            tbody.appendChild(row);
                            totalAmount += item.amount || 0;
                            totalItems++;
                        });
                    }
                });

                // 使用统一的总金额计算和更新
                updateTotalAmount();

                // 生成并更新合并的单据号码
                this.updateCombinedDocumentNumber();
                const combinedDocumentNumber = this.generateCombinedDocumentNumber();

                // 更新当前订单信息显示
                const orderDisplay = document.getElementById('current-order-display');
                const customerDisplay = document.getElementById('current-customer-display');

                if (orderDisplay) {
                    orderDisplay.textContent = combinedDocumentNumber || `合并视图 (${AppConfig.multiOrderData.length}个订单)`;
                }
                if (customerDisplay) {
                    customerDisplay.textContent = `${totalItems}个项目`;
                }

                // 获取最终计算的总金额用于日志
                const finalTotalAmount = calculateTotalAmount();
                console.log(`✅ 合并显示完成: ${AppConfig.multiOrderData.length}个订单, ${totalItems}个项目, 总金额: ${formatCurrency(finalTotalAmount)}`);
                console.log(`📋 合并单据号码: ${combinedDocumentNumber}`);
            },

            /**
             * 显示分别视图
             * @function showSeparateView - 只显示当前选中订单的项目
             */
            showSeparateView() {
                console.log('🔄 切换到分别显示模式');

                if (AppConfig.multiOrderData.length === 0) {
                    console.warn('⚠️ 没有订单数据可显示');
                    return;
                }

                // 移除合并视图样式
                const table = document.getElementById('items-table');
                table.classList.remove('combined-view');

                // 加载当前订单到表单
                const currentOrder = AppConfig.multiOrderData[AppConfig.currentOrderIndex];
                if (currentOrder) {
                    this.loadOrderToForm(currentOrder);
                    this.updateCurrentOrderDisplay();

                    // 恢复当前订单的独立单据号码
                    const documentNumberInput = document.getElementById('document-number');
                    if (documentNumberInput && currentOrder.documentNumber) {
                        documentNumberInput.value = currentOrder.documentNumber;
                        console.log(`📋 恢复独立单据号码: ${currentOrder.documentNumber}`);
                    }

                    // 更新总金额显示
                    updateTotalAmount();

                    console.log(`✅ 分别显示完成: 当前显示订单 ${currentOrder.orderName}`);
                } else {
                    console.warn('⚠️ 当前订单索引无效:', AppConfig.currentOrderIndex);
                }
            },

            /**
             * 获取订单颜色
             * @function getOrderColor - 为不同订单分配不同的颜色
             * @param {number} orderIndex - 订单索引
             * @returns {string} 颜色值
             */
            getOrderColor(orderIndex) {
                const colors = [
                    '#007bff', // 蓝色
                    '#28a745', // 绿色
                    '#dc3545', // 红色
                    '#ffc107', // 黄色
                    '#6f42c1', // 紫色
                    '#fd7e14', // 橙色
                    '#20c997', // 青色
                    '#e83e8c'  // 粉色
                ];
                return colors[orderIndex % colors.length];
            },

            /**
             * 生成合并的单据号码
             * @function generateCombinedDocumentNumber - 将多个订单的单据号码合并为叠加形态
             * @returns {string} 合并后的单据号码
             */
            generateCombinedDocumentNumber() {
                if (AppConfig.multiOrderData.length === 0) {
                    return '';
                }

                const documentNumbers = AppConfig.multiOrderData
                    .map(order => order.documentNumber)
                    .filter(number => number && number.trim() !== '') // 过滤空值
                    .map(number => number.trim()); // 去除空格

                if (documentNumbers.length === 0) {
                    return '';
                }

                const combinedNumber = documentNumbers.join('/');
                console.log(`📋 生成合并单据号码: ${combinedNumber} (来自${documentNumbers.length}个订单)`);
                return combinedNumber;
            },

            /**
             * 更新合并模式下的单据号码显示
             * @function updateCombinedDocumentNumber - 更新合并模式下的单据号码字段
             */
            updateCombinedDocumentNumber() {
                if (AppConfig.displayMode === 'combined' && AppConfig.multiOrderMode) {
                    const combinedNumber = this.generateCombinedDocumentNumber();
                    const documentNumberInput = document.getElementById('document-number');

                    if (documentNumberInput && combinedNumber) {
                        documentNumberInput.value = combinedNumber;
                        console.log(`✅ 合并单据号码已更新: ${combinedNumber}`);
                    }
                }
            }
        };

        // #endregion

        // #region 项目管理功能
        /**
         * 添加新项目行
         * @function addItem - 在项目表格中添加新的项目行
         */
        function addItem() {
            const tbody = document.getElementById('items-tbody');
            const newRow = document.createElement('tr');

            // 根据多订单模式决定是否显示订单列
            const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';
            const currentOrderName = AppConfig.multiOrderMode && AppConfig.multiOrderData[AppConfig.currentOrderIndex]
                ? AppConfig.multiOrderData[AppConfig.currentOrderIndex].orderName
                : '';

            newRow.innerHTML = `
                <td class="order-column" style="display: ${orderColumnDisplay};">
                    <span class="order-badge">${currentOrderName}</span>
                </td>
                <td><input type="text" placeholder="项目描述 / Item description" class="item-description" title="项目描述 / Item description"></td>
                <td><input type="number" value="1" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                <td><input type="number" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                <td class="item-amount">0.00</td>
                <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
            `;

            tbody.appendChild(newRow);

            // 为新行添加事件监听器
            const quantityInput = newRow.querySelector('.item-quantity');
            const priceInput = newRow.querySelector('.item-price');
            const descInput = newRow.querySelector('.item-description');

            // 检查是否已经绑定过事件监听器，避免重复绑定
            if (!quantityInput.hasAttribute('data-event-bound')) {
                quantityInput.addEventListener('input', () => updateItemAmount(newRow));
                quantityInput.setAttribute('data-event-bound', 'true');
            }

            if (!priceInput.hasAttribute('data-event-bound')) {
                priceInput.addEventListener('input', () => updateItemAmount(newRow));
                priceInput.setAttribute('data-event-bound', 'true');
            }

            // 为描述输入框添加自动预览更新
            if (AppConfig.autoPreview && !descInput.hasAttribute('data-preview-bound')) {
                descInput.addEventListener('input', () => safeDebouncedUpdatePreview());
                descInput.setAttribute('data-preview-bound', 'true');
            }

            AppConfig.itemCounter++;
        }

        /**
         * 切换多订单模式
         * @function toggleMultiOrderMode - 切换单订单/多订单模式
         */
        function toggleMultiOrderMode() {
            MultiOrderManager.toggleMode();
        }

        /**
         * 添加新订单
         * @function addNewOrder - 添加新的订单
         */
        function addNewOrder() {
            if (!AppConfig.multiOrderMode) {
                console.warn('⚠️ 请先切换到多订单模式');
                return;
            }

            // 保存当前订单数据
            if (AppConfig.multiOrderData[AppConfig.currentOrderIndex]) {
                MultiOrderManager.saveCurrentOrderData();
            }

            // 创建新订单
            const newOrder = MultiOrderManager.createOrderFromCurrentForm();
            AppConfig.currentOrderIndex = AppConfig.multiOrderData.length - 1;

            // 清空表单准备输入新订单
            clearFormForNewOrder();

            // 更新界面
            MultiOrderManager.updateOrderTabs();
            MultiOrderManager.updateCurrentOrderDisplay();

            console.log('✅ 已添加新订单:', newOrder.orderId);
        }

        /**
         * 切换显示模式
         * @function switchDisplayMode - 切换分别显示/合并显示模式
         * @param {string} mode - 显示模式 ('separate' | 'combined')
         */
        function switchDisplayMode(mode) {
            if (!AppConfig.multiOrderMode) {
                console.warn('⚠️ 请先切换到多订单模式');
                return;
            }

            if (!mode || (mode !== 'separate' && mode !== 'combined')) {
                console.error('❌ 无效的显示模式:', mode);
                return;
            }

            try {
                AppConfig.displayMode = mode;

                if (mode === 'combined') {
                    MultiOrderManager.showCombinedView();
                    console.log('🔄 已切换到合并显示模式，单据号码已合并');
                } else {
                    MultiOrderManager.showSeparateView();
                    console.log('🔄 已切换到分别显示模式，单据号码已恢复独立');
                }

                // 更新显示模式选择器
                const displayModeSelector = document.getElementById('display-mode');
                if (displayModeSelector && displayModeSelector.value !== mode) {
                    displayModeSelector.value = mode;
                }

                console.log(`🔄 已切换到${mode === 'combined' ? '合并' : '分别'}显示模式`);
            } catch (error) {
                console.error('❌ 切换显示模式失败:', error);
            }
        }

        /**
         * 清空表单准备新订单
         * @function clearFormForNewOrder - 清空表单但保留公司信息
         */
        function clearFormForNewOrder() {
            // 清空客户信息
            document.getElementById('customer-name').value = '';
            document.getElementById('customer-phone').value = '';
            document.getElementById('customer-email').value = '';
            document.getElementById('notes').value = '';

            // 生成新的单据号码
            const docType = document.getElementById('document-type').value;
            document.getElementById('document-number').value = generateDocumentNumber(docType);

            // 清空项目表格，保留一行
            const tbody = document.getElementById('items-tbody');
            const orderColumnDisplay = AppConfig.multiOrderMode ? 'table-cell' : 'none';

            tbody.innerHTML = `
                <tr>
                    <td class="order-column" style="display: ${orderColumnDisplay};"></td>
                    <td><input type="text" placeholder="项目描述 / Item description" class="item-description" title="项目描述 / Item description"></td>
                    <td><input type="number" value="1" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                    <td><input type="number" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                    <td class="item-amount">0.00</td>
                    <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                </tr>
            `;

            // 重新绑定事件
            initializeItemEvents();
            updateTotalAmount();
        }

        /**
         * 删除项目行
         * @function removeItem - 删除指定的项目行
         * @param {HTMLElement} button - 删除按钮元素
         */
        function removeItem(button) {
            const row = button.closest('tr');
            const tbody = document.getElementById('items-tbody');

            // 确保至少保留一行
            if (tbody.children.length > 1) {
                row.remove();
                updateTotalAmount();
            } else {
                // 如果只剩一行，清空内容而不删除
                row.querySelector('.item-description').value = '';
                row.querySelector('.item-quantity').value = '1';
                row.querySelector('.item-price').value = '';
                updateItemAmount(row);
            }
        }

        /**
         * 清空表单
         * @function clearForm - 清空所有表单输入
         */
        function clearForm() {
            // 重置基本信息
            if (DOMCache.documentNumber) DOMCache.documentNumber.value = '';
            if (DOMCache.documentDate) DOMCache.documentDate.value = '';

            // 重置公司信息
            const companyFields = ['company-name', 'tax-id', 'company-address', 'company-phone', 'contact-person'];
            companyFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) element.value = '';
            });

            // 重置客户信息
            if (DOMCache.customerName) DOMCache.customerName.value = '';
            if (DOMCache.channel) DOMCache.channel.value = '';
            if (DOMCache.customerPhone) DOMCache.customerPhone.value = '';
            if (DOMCache.customerEmail) DOMCache.customerEmail.value = '';
            if (DOMCache.notes) DOMCache.notes.value = '';

            // 清空项目表格，只保留一行
            const tbody = DOMCache.itemsTbody || document.getElementById('items-tbody');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td><input type="text" placeholder="项目描述 / Item description" class="item-description" title="项目描述 / Item description"></td>
                        <td><input type="number" value="1" min="1" class="item-quantity" placeholder="数量" title="数量 / Quantity"></td>
                        <td><input type="number" step="0.01" min="0" class="item-price" placeholder="单价" title="单价 / Price"></td>
                        <td class="item-amount">0.00</td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">删除 / Delete</button></td>
                    </tr>
                `;
            }

            // 重新绑定事件
            initializeItemEvents();

            // 更新总金额
            updateTotalAmount();

            // 清空预览
            const documentContainer = SafeDOM.get('document-container');
            if (documentContainer) {
                const emptyMessage = SafeDOMBuilder.createEmptyMessage(
                    '请填写表单信息并点击"更新预览"按钮\nPlease fill in the form and click "Update Preview" button'
                );
                SafeDOMBuilder.safeReplaceContent(documentContainer, emptyMessage);
            }

            // 重新生成单据号码
            const docType = document.getElementById('document-type').value;
            document.getElementById('document-number').value = generateDocumentNumber(docType);
        }

        /**
         * 初始化项目输入事件
         * @function initializeItemEvents - 为项目输入框绑定事件监听器
         */
        function initializeItemEvents() {
            const rows = document.querySelectorAll('#items-tbody tr');

            rows.forEach(row => {
                const quantityInput = row.querySelector('.item-quantity');
                const priceInput = row.querySelector('.item-price');
                const descInput = row.querySelector('.item-description');

                // 检查是否已经绑定过事件监听器，避免重复绑定
                if (!quantityInput.hasAttribute('data-event-bound')) {
                    quantityInput.addEventListener('input', () => updateItemAmount(row));
                    quantityInput.setAttribute('data-event-bound', 'true');
                }

                if (!priceInput.hasAttribute('data-event-bound')) {
                    priceInput.addEventListener('input', () => updateItemAmount(row));
                    priceInput.setAttribute('data-event-bound', 'true');
                }

                // 为描述输入框添加自动预览更新
                if (AppConfig.autoPreview && !descInput.hasAttribute('data-preview-bound')) {
                    descInput.addEventListener('input', () => safeDebouncedUpdatePreview());
                    descInput.setAttribute('data-preview-bound', 'true');
                }
            });
        }
        // #endregion

        // #region 模板渲染功能
        /**
         * 发票模板类 - 简化版
         * @description 处理发票文档的渲染逻辑
         */
        class InvoiceTemplate {
            /**
             * 渲染发票模板
             * @function render - 根据数据渲染发票
             * @param {Object} data - 发票数据
             * @param {boolean} isExport - 是否为导出模式
             * @returns {string} 渲染后的HTML字符串
             */
            static render(data, isExport = false) {
                const company = AppConfig.currentCompany;
                const companyInfo = CompanyInfo[company];

                return `
                    <div class="invoice-container">
                        ${this.renderHeader(company, companyInfo, data, isExport)}
                        ${this.renderTitle('发票 / INVOICE', data.documentNumber, data.date)}
                        ${this.renderCompanyInfo(data)}
                        ${this.renderCustomerInfo(data)}
                        ${this.renderItemsTable(data.items)}
                        ${this.renderTotal(data.total)}
                        ${this.renderNotes(data.notes)}
                        ${this.renderElectronicSignature()}
                        ${this.renderFooter(company, isExport)}
                        ${this.renderStamp(company, isExport)}
                    </div>
                `;
            }

            /**
             * 渲染页眉
             * @function renderHeader - 渲染文档页眉
             * @param {string} company - 公司代码
             * @param {Object} companyInfo - 公司信息
             * @param {Object} data - 表单数据
             * @param {boolean} isExport - 是否为导出模式
             * @returns {string} 页眉HTML
             */
            static renderHeader(company, companyInfo, data, isExport = false) {
                const headerImage = ImageBase64.getHeader(company);
                const logo = ImageBase64.getLogo(company);

                console.log(`📋 渲染页眉 - 公司: ${company}, 导出模式: ${isExport}, 页眉图片: ${!!headerImage}, 标志: ${!!logo}`);

                // 如果有页眉图片，直接显示
                if (headerImage) {
                    return `
                        <div class="document-header-image-container">
                            <img src="${headerImage}" alt="页眉图片">
                        </div>
                    `;
                }

                // 导出模式下，如果没有页眉图片则返回空字符串，完全避免任何占位符
                if (isExport) {
                    console.log(`📋 导出模式 - ${company}公司无页眉图片和logo，返回完全空内容`);
                    return '';
                }

                // 预览模式下显示图片占位区域（包含logo处理）
                console.log(`📋 预览模式 - 显示页眉占位符，logo状态: ${!!logo}`);
                return `
                    <div class="document-header">
                        <div class="image-placeholder header-placeholder">
                            页眉图片区域 / Header Image Area<br>
                            <small style="font-size: 10px; opacity: 0.7;">建议尺寸: 794x130px / Recommended: 794x130px</small>
                            ${logo ? `<br><img src="${logo}" alt="公司标志" style="max-height: 40px; margin-top: 5px;">` : '<br><small style="font-size: 10px;">公司标志占位 / Logo Placeholder</small>'}
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染标题
             * @function renderTitle - 渲染文档标题
             * @param {string} title - 文档标题
             * @param {string} number - 单据号码
             * @param {string} date - 日期
             * @returns {string} 标题HTML
             */
            static renderTitle(title, number, date) {
                return `
                    <div style="text-align: center; margin: 15px 0; padding: 0 10px; box-sizing: border-box; clear: both;">
                        <h1 style="color: var(--primary-color); margin-bottom: 8px; font-size: 22px; line-height: 1.3; word-wrap: break-word; overflow-wrap: break-word; -webkit-hyphens: auto; hyphens: auto; box-sizing: border-box;">${title}</h1>
                        <div style="display: flex; justify-content: space-between; margin-top: 12px; font-size: 13px; flex-wrap: wrap; gap: 10px; line-height: 1.4; box-sizing: border-box;">
                            <div style="flex: 1; min-width: 200px; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box;"><strong>单据号码 / Document Number:</strong> ${number}</div>
                            <div style="flex: 1; min-width: 150px; text-align: right; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box;"><strong>日期 / Date:</strong> ${date}</div>
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染公司信息
             * @function renderCompanyInfo - 渲染公司信息（根据文档类型动态控制显示）
             * @param {Object} data - 表单数据
             * @returns {string} 公司信息HTML
             */
            static renderCompanyInfo(data) {
                // 检查文档类型，如果是收据则不显示公司信息
                const documentType = data.documentType || 'receipt';
                if (documentType === 'receipt') {
                    console.log('📋 收据模式：跳过公司信息渲染');
                    return '';
                }

                console.log('📋 发票模式：开始渲染公司信息');
                const fields = [];

                // 使用安全版本的数据，如果不存在则对原始数据进行转义
                if (data.companyName) {
                    const safeCompanyName = data.safeCompanyName || escapeHtml(data.companyName);
                    fields.push(`<div><strong>公司名称 / Company Name:</strong> ${safeCompanyName}</div>`);
                }
                if (data.taxId) {
                    const safeTaxId = data.safeTaxId || escapeHtml(data.taxId);
                    fields.push(`<div><strong>税号 / Tax ID:</strong> ${safeTaxId}</div>`);
                }
                if (data.companyAddress) {
                    const safeCompanyAddress = data.safeCompanyAddress || escapeHtml(data.companyAddress);
                    fields.push(`<div><strong>公司地址 / Company Address:</strong> ${safeCompanyAddress}</div>`);
                }
                if (data.companyPhone) {
                    const safeCompanyPhone = data.safeCompanyPhone || escapeHtml(data.companyPhone);
                    fields.push(`<div><strong>公司电话 / Company Phone:</strong> ${safeCompanyPhone}</div>`);
                }
                if (data.contactPerson) {
                    const safeContactPerson = data.safeContactPerson || escapeHtml(data.contactPerson);
                    fields.push(`<div><strong>负责人 / Contact Person:</strong> ${safeContactPerson}</div>`);
                }

                if (fields.length === 0) {
                    console.log('📋 发票模式：无公司信息数据，跳过渲染');
                    return '';
                }

                console.log(`📋 发票模式：渲染公司信息完成，包含 ${fields.length} 个字段`);
                return `
                    <div style="margin: 12px 0; padding: 10px; border: 1px solid var(--border-color); border-radius: 6px; background: #f9f9f9; box-sizing: border-box; clear: both;">
                        <h3 style="margin-top: 0; margin-bottom: 8px; color: var(--dark-color); font-size: 15px; line-height: 1.3; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box;">公司信息 / Company Information</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 12px; line-height: 1.5; word-wrap: break-word; overflow-wrap: break-word; -webkit-hyphens: auto; hyphens: auto; box-sizing: border-box;">
                            ${fields.join('')}
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染客户信息
             * @function renderCustomerInfo - 渲染客户信息（智能显示）
             * @param {Object} data - 客户数据
             * @returns {string} 客户信息HTML
             */
            static renderCustomerInfo(data) {
                const fields = [];

                // 使用安全版本的数据，如果不存在则对原始数据进行转义
                if (data.customerName) {
                    const safeCustomerName = data.safeCustomerName || escapeHtml(data.customerName);
                    fields.push(`<div><strong>客户名称 / Customer Name:</strong> ${safeCustomerName}</div>`);
                }
                if (data.channel) {
                    const safeChannel = data.safeChannel || escapeHtml(data.channel);
                    fields.push(`<div><strong>渠道 / Channel:</strong> ${safeChannel}</div>`);
                }
                if (data.customerPhone) {
                    const safeCustomerPhone = data.safeCustomerPhone || escapeHtml(data.customerPhone);
                    fields.push(`<div><strong>客户电话 / Customer Phone:</strong> ${safeCustomerPhone}</div>`);
                }
                if (data.customerEmail) {
                    const safeCustomerEmail = data.safeCustomerEmail || escapeHtml(data.customerEmail);
                    fields.push(`<div><strong>客户邮箱 / Customer Email:</strong> ${safeCustomerEmail}</div>`);
                }

                if (fields.length === 0) return '';

                return `
                    <div style="margin: 12px 0; padding: 10px; border: 1px solid var(--border-color); border-radius: 6px; box-sizing: border-box; clear: both;">
                        <h3 style="margin-top: 0; margin-bottom: 8px; color: var(--dark-color); font-size: 15px; line-height: 1.3; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box;">客户信息 / Customer Information</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 12px; line-height: 1.5; word-wrap: break-word; overflow-wrap: break-word; -webkit-hyphens: auto; hyphens: auto; box-sizing: border-box;">
                            ${fields.join('')}
                        </div>
                    </div>
                `;
            }
            /**
             * 渲染项目表格
             * @function renderItemsTable - 渲染项目明细表格（响应式字体）
             * @param {Array} items - 项目数组
             * @returns {string} 项目表格HTML
             */
            static renderItemsTable(items) {
                if (items.length === 0) return '';

                // 根据项目数量调整字体大小
                const fontSize = items.length > 10 ? '11px' : items.length > 5 ? '12px' : '13px';
                const padding = items.length > 10 ? '6px' : items.length > 5 ? '8px' : '10px';

                const currencySymbol = getCurrentCurrencySymbol();
                const tableRows = items.map((item, index) => {
                    // 使用安全版本的描述，如果不存在则对原始数据进行转义
                    const safeDescription = item.safeDescription || escapeHtml(item.description);

                    return `
                        <tr>
                            <td style="text-align: center; padding: ${padding}; border: 1px solid var(--border-color); word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.4;">${index + 1}</td>
                            <td style="padding: ${padding}; border: 1px solid var(--border-color); word-wrap: break-word; overflow-wrap: break-word; -webkit-hyphens: auto; hyphens: auto; box-sizing: border-box; vertical-align: top; line-height: 1.4; white-space: normal;">${safeDescription}</td>
                            <td style="text-align: center; padding: ${padding}; border: 1px solid var(--border-color); word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.4;">${item.quantity}</td>
                            <td style="text-align: right; padding: ${padding}; border: 1px solid var(--border-color); word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.4; white-space: nowrap;">${currencySymbol} ${formatCurrency(item.price)}</td>
                            <td style="text-align: right; padding: ${padding}; border: 1px solid var(--border-color); word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.4; white-space: nowrap;">${currencySymbol} ${formatCurrency(item.amount)}</td>
                        </tr>
                    `;
                }).join('');

                return `
                    <div style="margin: 12px 0; padding: 0 10px; box-sizing: border-box; clear: both;">
                        <h3 style="color: var(--dark-color); font-size: 15px; margin-bottom: 6px; line-height: 1.3; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box;">项目明细 / Item Details</h3>
                        <table style="width: 100%; border-collapse: collapse; font-size: ${fontSize}; table-layout: fixed; box-sizing: border-box; border-spacing: 0;">
                            <thead>
                                <tr style="background-color: var(--light-color);">
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: center; width: 8%; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.3;">序号<br/>No.</th>
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: left; width: 40%; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.3;">项目描述<br/>Description</th>
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: center; width: 12%; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.3;">数量<br/>Qty</th>
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: right; width: 20%; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.3;">单价<br/>Unit Price</th>
                                    <th style="padding: ${padding}; border: 1px solid var(--border-color); text-align: right; width: 20%; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box; vertical-align: top; line-height: 1.3;">金额<br/>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${tableRows}
                            </tbody>
                        </table>
                    </div>
                `;
            }

            /**
             * 渲染总金额
             * @function renderTotal - 渲染总金额信息
             * @param {number} total - 总金额
             * @returns {string} 总金额HTML
             */
            static renderTotal(total) {
                const currencySymbol = getCurrentCurrencySymbol();

                // 详细调试信息
                console.log(`💰 渲染总金额开始 - 输入参数:`, {
                    原始total: total,
                    类型: typeof total,
                    是否为数字: !isNaN(total),
                    货币符号: currencySymbol,
                    当前货币: AppConfig.currentCurrency,
                    多订单模式: AppConfig.multiOrderMode,
                    显示模式: AppConfig.displayMode
                });

                // 确保总金额有效
                let safeTotal = 0;
                if (total !== null && total !== undefined && !isNaN(total)) {
                    safeTotal = parseFloat(total);
                } else {
                    console.warn(`⚠️ 总金额无效，使用默认值0 - 原始值: ${total}`);
                }

                const displayTotal = formatCurrency(safeTotal);

                console.log(`💰 渲染总金额完成 - 最终显示:`, {
                    安全total: safeTotal,
                    格式化结果: displayTotal,
                    完整显示: `${currencySymbol} ${displayTotal}`,
                    总金额层级: '200 (固定数值)',
                    印章层级: '30 (CSS变量)',
                    层级说明: '总金额层级(200) > 印章层级(30)，确保总金额在上层',
                    CSS修复: '使用不透明背景色，避免导出时出现灰色蒙板'
                });

                return `
                    <div style="margin: 15px 0; text-align: right; position: relative; z-index: 200; clear: both; box-sizing: border-box;">
                        <div class="total-amount-container" style="display: inline-block; padding: 12px 18px; border: 2px solid #1e40af; border-radius: 6px; background-color: #ffffff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); min-width: 200px; max-width: 100%; z-index: 200; box-sizing: border-box; position: relative;">
                            <h3 style="margin: 0; color: #1e40af; font-size: 16px; font-weight: bold; line-height: 1.4; text-shadow: none; word-wrap: break-word; overflow-wrap: break-word; position: relative; z-index: 201; box-sizing: border-box; white-space: nowrap;">总金额 / Total Amount: ${currencySymbol} ${displayTotal}</h3>
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染备注
             * @function renderNotes - 渲染备注信息
             * @param {string} notes - 备注内容
             * @returns {string} 备注HTML
             */
            static renderNotes(notes) {
                if (!notes) return '';

                // 如果传入的是数据对象，使用安全版本；否则直接转义
                const safeNotes = (typeof notes === 'object' && notes.safeNotes)
                    ? notes.safeNotes
                    : escapeHtml(notes);

                return `
                    <div style="margin: 12px 0; padding: 0 10px; box-sizing: border-box; clear: both;">
                        <h3 style="color: var(--dark-color); font-size: 15px; margin-bottom: 6px; line-height: 1.3; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box;">备注 / Notes</h3>
                        <div style="padding: 10px; border: 1px solid var(--border-color); border-radius: 6px; background-color: #f9f9f9; font-size: 12px; line-height: 1.5; word-wrap: break-word; overflow-wrap: break-word; -webkit-hyphens: auto; hyphens: auto; box-sizing: border-box; white-space: pre-wrap;">
                            ${safeNotes}
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染电子生成标识
             * @function renderElectronicSignature - 渲染电子生成提示
             * @returns {string} 电子生成标识HTML
             */
            static renderElectronicSignature() {
                return `
                    <div class="electronic-signature">
                        此文档为电子生成 / This document is electronically generated
                    </div>
                `;
            }

            /**
             * 渲染页脚
             * @function renderFooter - 渲染文档页脚（带占位区域）
             * @param {string} company - 公司代码
             * @param {boolean} isExport - 是否为导出模式
             * @returns {string} 页脚HTML
             */
            static renderFooter(company, isExport = false) {
                const footerImage = ImageBase64.getFooter(company);

                if (footerImage) {
                    return `
                        <div class="unified-document-footer company-footer-image-container">
                            <img src="${footerImage}" alt="页脚图片">
                        </div>
                    `;
                }

                // 导出模式下，如果没有图片则返回空字符串
                if (isExport) {
                    return '';
                }

                // 预览模式下显示图片占位区域
                return `
                    <div class="document-footer">
                        <div class="image-placeholder footer-placeholder">
                            页脚图片区域 / Footer Image Area<br>
                            <small style="font-size: 10px; opacity: 0.7;">建议尺寸: 794x110px / Recommended: 794x110px</small>
                        </div>
                    </div>
                `;
            }

            /**
             * 渲染印章
             * @function renderStamp - 渲染公司印章（带占位区域）
             * @param {string} company - 公司代码
             * @param {boolean} isExport - 是否为导出模式
             * @returns {string} 印章HTML
             */
            static renderStamp(company, isExport = false) {
                const stamp = ImageBase64.getStamp(company);

                if (stamp) {
                    return `
                        <div class="company-stamp">
                            <img src="${stamp}" alt="公司印章">
                        </div>
                    `;
                }

                // 导出模式下，如果没有图片则返回空字符串
                if (isExport) {
                    return '';
                }

                // 预览模式下显示图片占位区域
                return `
                    <div class="image-placeholder stamp-placeholder">
                        印章区域<br>Stamp Area<br>
                        <small style="font-size: 9px; opacity: 0.7;">96x96px</small>
                    </div>
                `;
            }
        }

        /**
         * 收据模板类
         * @description 处理收据文档的渲染逻辑
         */
        class ReceiptTemplate {
            /**
             * 渲染收据模板
             * @function render - 根据数据渲染收据
             * @param {Object} data - 收据数据
             * @param {boolean} isExport - 是否为导出模式
             * @returns {string} 渲染后的HTML字符串
             */
            static render(data, isExport = false) {
                const company = AppConfig.currentCompany;
                const companyInfo = CompanyInfo[company];

                return `
                    <div class="receipt-container">
                        ${InvoiceTemplate.renderHeader(company, companyInfo, data, isExport)}
                        ${InvoiceTemplate.renderTitle('收据 / RECEIPT', data.documentNumber, data.date)}
                        ${InvoiceTemplate.renderCompanyInfo(data)}
                        ${this.renderReceiptInfo(data)}
                        ${InvoiceTemplate.renderItemsTable(data.items)}
                        ${InvoiceTemplate.renderTotal(data.total)}
                        ${InvoiceTemplate.renderNotes(data.notes)}
                        ${InvoiceTemplate.renderElectronicSignature()}
                        ${InvoiceTemplate.renderFooter(company, isExport)}
                        ${InvoiceTemplate.renderStamp(company, isExport)}
                    </div>
                `;
            }

            /**
             * 渲染收据信息
             * @function renderReceiptInfo - 渲染收据特有信息（智能显示）
             * @param {Object} data - 收据数据
             * @returns {string} 收据信息HTML
             */
            static renderReceiptInfo(data) {
                const fields = [];

                // 使用安全版本的数据，如果不存在则对原始数据进行转义
                if (data.customerName) {
                    const safeCustomerName = data.safeCustomerName || escapeHtml(data.customerName);
                    fields.push(`<div><strong>付款人 / Payer:</strong> ${safeCustomerName}</div>`);
                }
                if (data.channel) {
                    const safeChannel = data.safeChannel || escapeHtml(data.channel);
                    fields.push(`<div><strong>渠道 / Channel:</strong> ${safeChannel}</div>`);
                }
                if (data.customerPhone) {
                    const safeCustomerPhone = data.safeCustomerPhone || escapeHtml(data.customerPhone);
                    fields.push(`<div><strong>联系方式 / Contact:</strong> ${safeCustomerPhone}</div>`);
                }
                if (data.customerEmail) {
                    const safeCustomerEmail = data.safeCustomerEmail || escapeHtml(data.customerEmail);
                    fields.push(`<div><strong>邮箱 / Email:</strong> ${safeCustomerEmail}</div>`);
                }

                if (fields.length === 0) return '';

                return `
                    <div style="margin: 12px 0; padding: 10px; border: 1px solid var(--border-color); border-radius: 6px; box-sizing: border-box;">
                        <h3 style="margin-top: 0; margin-bottom: 8px; color: var(--dark-color); font-size: 15px; line-height: 1.3;">收款信息 / Payment Information</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 6px; font-size: 12px; line-height: 1.4; word-wrap: break-word;">
                            ${fields.join('')}
                        </div>
                        <div style="margin-top: 10px; padding: 8px; background-color: var(--light-color); border-radius: 4px; font-size: 12px; line-height: 1.4;">
                            <strong>收款确认 / Payment Confirmation:</strong> 已收到上述款项，特此开具收据。/ The above payment has been received. This receipt is issued accordingly.
                        </div>
                    </div>
                `;
            }
        }
        // #endregion

        // #region 主要功能函数
        // 预览更新重试计数器
        let previewUpdateRetryCount = 0;
        const MAX_PREVIEW_RETRY = 3;

        // 预览状态管理函数已移至 preview-module.js

        /**
         * 检查A4内容溢出
         * @function checkA4ContentOverflow - 检查内容是否超出A4边界
         */
        function checkA4ContentOverflow() {
            const documentContainer = document.getElementById('document-container');
            const documentPreview = document.getElementById('document-preview');

            if (!documentContainer || !documentPreview) {
                return false;
            }

            // 获取实际内容高度（不包括缩放）
            const containerHeight = documentContainer.scrollHeight;
            const a4Height = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--a4-height-px'));

            console.log(`📏 A4内容溢出检查:`, {
                容器实际高度: containerHeight,
                A4标准高度: a4Height,
                是否溢出: containerHeight > a4Height,
                溢出量: Math.max(0, containerHeight - a4Height)
            });

            // 如果内容高度超过A4高度，显示警告
            if (containerHeight > a4Height) {
                console.warn(`⚠️ 内容溢出A4边界: ${containerHeight - a4Height}px`);
                showContentOverflowWarning(containerHeight - a4Height);
                return true;
            } else {
                hideContentOverflowWarning();
                return false;
            }
        }

        /**
         * 显示内容溢出警告
         * @function showContentOverflowWarning - 显示内容溢出警告
         * @param {number} overflowAmount - 溢出的像素数
         */
        function showContentOverflowWarning(overflowAmount) {
            let warningElement = document.querySelector('.content-overflow-warning');

            if (!warningElement) {
                warningElement = document.createElement('div');
                warningElement.className = 'content-overflow-warning';
                document.getElementById('document-preview').appendChild(warningElement);
            }

            warningElement.textContent = `内容溢出A4边界 ${overflowAmount}px / Content overflows A4 boundary by ${overflowAmount}px`;
            warningElement.style.display = 'block';

            // 3秒后自动隐藏
            setTimeout(() => {
                warningElement.style.display = 'none';
            }, 3000);
        }

        /**
         * 隐藏内容溢出警告
         * @function hideContentOverflowWarning - 隐藏内容溢出警告
         */
        function hideContentOverflowWarning() {
            const warningElement = document.querySelector('.content-overflow-warning');
            if (warningElement) {
                warningElement.style.display = 'none';
            }
        }

        // updatePreview函数已移至 preview-module.js

        /**
         * 确保DOM完全就绪的辅助函数
         * @function ensureDOMReady - 确保DOM元素完全加载
         * @returns {Promise<boolean>} DOM是否就绪
         */
        async function ensureDOMReady() {
            return new Promise((resolve) => {
                if (document.readyState === 'complete') {
                    resolve(true);
                } else {
                    const checkReady = () => {
                        if (document.readyState === 'complete') {
                            resolve(true);
                        } else {
                            setTimeout(checkReady, 100);
                        }
                    };
                    checkReady();
                }
            });
        }

        // #region 导出函数已移至独立模块
        // 导出相关的兼容性函数已移至 export-components.js 模块
        // 包括：exportToPDF, exportToImage, debugImageExport
        // #endregion

        // #region 事件监听器和初始化
        /**
         * 检查外部依赖库是否加载成功
         * @function checkExternalDependencies - 检查外部库的可用性
         */
        function checkExternalDependencies() {
            const dependencies = {
                html2canvas: typeof html2canvas !== 'undefined',
                jsPDF: typeof window.jspdf !== 'undefined'
            };

            // 检查html2canvas的具体功能
            if (dependencies.html2canvas) {
                try {
                    // 测试html2canvas是否可以正常调用
                    const testElement = document.createElement('div');
                    testElement.style.width = '1px';
                    testElement.style.height = '1px';
                    testElement.style.position = 'absolute';
                    testElement.style.top = '-9999px';
                    document.body.appendChild(testElement);
                    
                    // 异步测试html2canvas基本功能
                    html2canvas(testElement, { scale: 1, logging: false })
                        .then(() => {
                            console.log('✅ html2canvas功能测试通过');
                            document.body.removeChild(testElement);
                        })
                        .catch((error) => {
                            console.error('❌ html2canvas功能测试失败:', error);
                            document.body.removeChild(testElement);
                        });
                } catch (error) {
                    console.error('❌ html2canvas测试异常:', error);
                    dependencies.html2canvas = false;
                }
            } else {
                console.warn('html2canvas未加载，图片导出功能将不可用');
            }

            if (!dependencies.jsPDF) {
                console.warn('jsPDF未加载，PDF导出功能将不可用');
            }

            return dependencies;
        }

        // initializeDefaultPreview函数已移至 preview-module.js

        /**
         * 初始化应用程序
         * @function initializeApp - 初始化所有事件监听器和默认值
         */
        function initializeApp() {
            // 检查外部依赖
            const dependencies = checkExternalDependencies();

            // 初始化DOM缓存
            DOMCache.initCache();

            // 设置默认日期
            const today = new Date().toISOString().split('T')[0];
            if (DOMCache.documentDate) {
                DOMCache.documentDate.value = today;
            }

            // 从localStorage恢复货币设置
            const savedCurrency = localStorage.getItem('smartoffice_currency');
            if (savedCurrency && CurrencyConfig[savedCurrency]) {
                AppConfig.currentCurrency = savedCurrency;
                if (DOMCache.currencySelector) {
                    DOMCache.currencySelector.value = savedCurrency;
                }
            }

            // 初始化货币显示
            updateAllCurrencyDisplays();

            // 初始化字段显示状态
            const initialDocType = DOMCache.documentType ? DOMCache.documentType.value : 'receipt';
            toggleCompanyFields(initialDocType);

            // 初始化项目事件
            initializeItemEvents();

            // 绑定表单变化事件（自动预览）
            const formElements = [
                'document-type',
                'company-selector',
                'currency-selector',
                'company-name',
                'tax-id',
                'company-address',
                'company-phone',
                'contact-person',
                'document-number',
                'document-date',
                'customer-name',
                'channel',
                'customer-phone',
                'customer-email',
                'notes'
            ];

            formElements.forEach(id => {
                const element = document.getElementById(id);
                if (element && !element.hasAttribute('data-form-event-bound')) {
                    element.addEventListener('change', () => {
                        if (id === 'company-selector') {
                            AppConfig.currentCompany = element.value;
                        } else if (id === 'currency-selector') {
                            switchCurrency(element.value);
                        } else if (id === 'document-type') {
                            toggleCompanyFields(element.value);
                        }

                        // 自动预览更新（使用优化的防抖函数）
                        if (AppConfig.autoPreview) {
                            safeDebouncedUpdatePreview();
                        }
                    });

                    // 为文本输入框添加输入事件（使用优化的防抖函数）
                    if (element.type === 'text' || element.type === 'email' || element.type === 'tel' ||
                        element.type === 'number' || element.tagName === 'TEXTAREA') {
                        element.addEventListener('input', () => {
                            if (AppConfig.autoPreview) {
                                safeDebouncedUpdatePreview();
                            }
                        });

                        // 为数字输入框添加额外的change事件监听
                        if (element.type === 'number') {
                            element.addEventListener('change', () => {
                                if (AppConfig.autoPreview) {
                                    safeDebouncedUpdatePreview();
                                }
                            });
                        }
                    }

                    // 标记已绑定事件
                    element.setAttribute('data-form-event-bound', 'true');
                }
            });

            // 绑定文档类型变化事件（检查是否已绑定）
            const documentTypeElement = document.getElementById('document-type');
            if (documentTypeElement && !documentTypeElement.hasAttribute('data-doctype-event-bound')) {
                documentTypeElement.addEventListener('change', function() {
                    const newType = this.value;
                    const numberInput = document.getElementById('document-number');

                    // 如果单据号码为空或者是旧格式，生成新的单据号码
                    if (!numberInput.value ||
                        (newType === 'invoice' && numberInput.value.startsWith('RCP')) ||
                        (newType === 'receipt' && numberInput.value.startsWith('INV'))) {
                        numberInput.value = generateDocumentNumber(newType);
                    }
                });
                documentTypeElement.setAttribute('data-doctype-event-bound', 'true');
            }

            // 初始化单据号码
            const initialType = document.getElementById('document-type').value;
            document.getElementById('document-number').value = generateDocumentNumber(initialType);

            // 初始化预览模块
            if (typeof PreviewModule !== 'undefined') {
                PreviewModule.init();
            } else {
                console.warn('⚠️ 预览模块未加载，预览功能可能不可用');
            }

            // 验证AI智能填充功能
            setTimeout(() => {
                console.log('🧪 验证AI智能填充功能...');
                const aiElements = {
                    textInput: document.getElementById('ai-text-input'),
                    imageInput: document.getElementById('ai-image-input'),
                    processBtn: document.getElementById('ai-process-btn'),
                    statusDiv: document.getElementById('ai-status'),
                    fillPanel: document.getElementById('ai-fill-panel')
                };

                console.log('🔍 AI元素检查:', aiElements);

                if (aiElements.textInput && aiElements.processBtn) {
                    console.log('✅ AI智能填充功能已就绪');
                } else {
                    console.warn('⚠️ AI智能填充功能可能存在问题');
                }
            }, 1000);

            console.log('发票/收据生成器初始化完成 / Invoice/Receipt Generator Initialized');
        }

        /**
         * 页面加载完成后初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化事件管理器（优先初始化）
            EventManager.init();

            initializeApp();

            // 初始化现代化导出系统（已移至export-components.js模块）
            if (typeof ModernExportSystem !== 'undefined') {
                ModernExportSystem.init();
            }

            // 初始化调试管理器
            DebugManager.init();

            // 初始化图片质量管理器
            ImageQualityManager.init();

            // 显示欢迎信息
            console.log('='.repeat(50));
            console.log('发票/收据生成器 - SmartOffice 3.0');
            console.log('独立HTML版本 - 支持离线使用');
            console.log('功能: 发票生成、收据生成、300DPI高质量导出');
            console.log('新增: 现代化导出系统、无损图片处理、优化事件管理');
            console.log('事件绑定统计:', EventManager.getBindingStats());
            console.log('='.repeat(50));
        });

        /**
         * 全局错误处理
         */
        window.addEventListener('error', function(event) {
            console.error('发生错误:', event.error);
            // 可以在这里添加用户友好的错误提示
        });

        /**
         * 导出全局函数供HTML调用
         */
        window.addItem = addItem;
        window.removeItem = removeItem;
        window.clearForm = clearForm;
        // debugContentConsistency函数已移至 preview-module.js

        // debugImageExport 函数已移至 export-components.js 模块

        // 预览内容提取函数已移至 preview-module.js

        function checkHtmlEscaping(data) {
            const results = {};
            const testStrings = ['<script>', '"test"', "'test'", '&amp;', '<>&"\''];

            for (const field in data) {
                if (typeof data[field] === 'string') {
                    results[field] = {
                        original: data[field],
                        escaped: escapeHtml(data[field]),
                        needsEscaping: testStrings.some(test => data[field].includes(test))
                    };
                }
            }

            return results;
        }

        function checkCharacterEncoding(data) {
            const results = {};

            for (const field in data) {
                if (typeof data[field] === 'string') {
                    const text = data[field];
                    results[field] = {
                        length: text.length,
                        byteLength: new Blob([text]).size,
                        hasChinese: /[\u4e00-\u9fff]/.test(text),
                        hasEmoji: /[\u{1f600}-\u{1f64f}]|[\u{1f300}-\u{1f5ff}]|[\u{1f680}-\u{1f6ff}]|[\u{1f1e0}-\u{1f1ff}]/u.test(text),
                        hasSpecialChars: /[^\x00-\x7F]/.test(text)
                    };
                }
            }

            return results;
        }

        function generateDiagnosisSummary(comparison, escapeTest, encodingTest) {
            const issues = [];

            // 检查一致性问题
            for (const field in comparison) {
                if (!comparison[field].match) {
                    issues.push(`${field}: 表单与预览内容不一致`);
                }
            }

            // 检查转义问题
            for (const field in escapeTest) {
                if (escapeTest[field].needsEscaping) {
                    issues.push(`${field}: 包含需要转义的特殊字符`);
                }
            }

            // 检查编码问题
            for (const field in encodingTest) {
                const encoding = encodingTest[field];
                if (encoding.byteLength !== encoding.length) {
                    issues.push(`${field}: 可能存在多字节字符编码问题`);
                }
            }

            return {
                totalIssues: issues.length,
                issues: issues,
                status: issues.length === 0 ? '✅ 未发现问题' : `⚠️ 发现${issues.length}个问题`
            };
        }



        } // #endregion 开发环境测试函数

        // updatePreview 和 safeDebouncedUpdatePreview 已移至 preview-module.js
        // window.exportToPDF 和 window.exportToImage 已移至 export-components.js 模块
        window.toggleAIFillPanel = toggleAIFillPanel;
        window.processAIFill = processAIFill;
        window.clearAIInput = clearAIInput;
        window.switchCurrency = switchCurrency;
        window.toggleCompanyFields = toggleCompanyFields;
        window.showAIStatus = showAIStatus;
        window.hideAIStatus = hideAIStatus;

        // 确保AI相关函数在全局作用域可用
        if (typeof processAIFill === 'function') {
            console.log('✅ processAIFill 函数已正确定义');
        } else {
            console.error('❌ processAIFill 函数未定义');
        }

        // 注意：updateExportMethodInfo函数已移除，因为简化的导出界面不再需要方法选择器

        // #endregion

        /**
         * 版本信息和开发说明
         * @description
         * 发票/收据生成器 v3.0 - 双语显示专业版
         *
         * 新增特性 v3.0:
         * - 🌐 双语并列显示模式（中文 / English）
         * - 🖼️ 图片占位区域显示（页眉、页脚、印章）
         * - 📋 发票字段增强（公司信息、税号等）
         * - 🧠 智能字段显示（空字段自动隐藏）
         * - 🔖 电子生成标识
         * - 🎨 Material Design风格优化
         * - 📄 A4单页布局优化
         * - 📱 响应式内容适配
         *
         * 核心特性:
         * - 独立HTML文件，支持file://协议
         * - 传统script标签架构，最大兼容性
         * - 内置图片资源管理（base64格式）
         * - 支持发票和收据两种文档类型
         * - 动态项目管理，自动计算金额
         * - PDF和图片导出功能
         * - 完整的中英双语界面
         *
         * 界面特点:
         * - 双语并列显示，无需切换
         * - Material Design设计规范
         * - 图片占位区域可视化
         * - 智能字段隐藏机制
         * - 电子生成水印标识
         *
         * 使用方法:
         * 1. 选择文档类型（发票/收据）
         * 2. 选择公司信息
         * 3. 填写公司详细信息（可选）
         * 4. 填写客户信息（支持实时预览）
         * 5. 添加项目明细（自动计算总金额）
         * 6. 实时查看预览效果
         * 7. 使用"导出PDF"或"导出图片"保存文档
         *
         * 响应式设计:
         * - 桌面端：双列布局，最佳编辑体验
         * - 平板端：优化间距，适中预览比例
         * - 移动端：单列布局，触摸友好界面
         * - 智能字体缩放，确保单页显示
         *
         * 图片资源:
         * - 可通过ImageBase64.updateImage()方法更新图片
         * - 支持logo、header、footer、stamp四种类型
         * - 图片格式为base64编码字符串
         * - 空图片显示占位区域
         *
         * 技术特点:
         * - 防抖机制避免频繁更新
         * - 智能字段显示逻辑
         * - 自适应布局，多设备兼容
         * - A4纸张严格控制
         * - 离线可用，无需网络连接
         */
    </script>
</body>
</html>
